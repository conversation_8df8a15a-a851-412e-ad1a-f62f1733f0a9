---
description: 
globs: 
alwaysApply: true
---
To maintain a consistent and reproducible development environment across the team, all commands must be executed within the context of the running Docker containers. This prevents environment drift and ensures that scripts and tools behave predictably.

Please use docker compose exec [service_name] [command] for running non-interactive commands. We strongly recommend consulting the project's Makefile as it contains convenient aliases for most common development tasks. Avoid running interactive shell commands directly.