---
description: 
globs: *.tsx,*.ts,*.js,*.jsx
alwaysApply: false
---
To ensure a globally consistent, maintainable, and fully localized user experience, we have adopted a component-scoped approach to internationalization. This strategy mandates that each component is fully responsible for its own translations, promoting modularity and simplifying maintenance.

This approach provides us with the combined benefits of:

Component Encapsulation: By requiring each component to have its own locales directory, we make our components self-contained and portable. All dependencies, including text translations, are co-located, eliminating the need to manage large, shared translation files.

Standardized Implementation: The useComponentTranslation hook is the sole, authorized method for accessing translations. This ensures a consistent implementation pattern across the codebase, handling namespaces and fallbacks predictably.

Strict Synchronization: We enforce complete and immediate translation coverage across all supported languages. This prevents a fragmented user experience where some parts of the UI are untranslated in certain locales.

Guideline: When implementing or refactoring a component, all user-facing strings must be extracted into camelCase keys and rendered using the t('key') function from the useComponentTranslation hook. It is a mandatory step to create a locales directory within the component folder, containing fully translated index.json files for all supported languages (en, ar, de, es, fr, it). Before any code is committed, verify that all locale files are perfectly synchronized with the component's needs and contain no unused keys.