---
description: 
globs: *.tsx,*.ts,*.jsx,*.js
alwaysApply: false
---
To ensure consistency, discoverability, and seamless integration with our development tools (TypeScript, React, Storybook, and Vitest), every UI component created within the ui/src/components/ directory must adhere to the following file and folder structure.

Each component must reside within its own dedicated folder, named after the component in kebab-case. Inside this folder, the following four files are mandatory, following a consistent naming convention:

ui/src/components/ui/<component-name>/
├── index.ts
├── <component-name>.tsx
├── <component-name>.stories.tsx
└── <component-name>.test.tsx
File Breakdown & Purpose:

<component-name>.tsx: This file contains the core React component logic and JSX. It is the heart of the component.

<component-name>.stories.tsx: This is the Storybook file. It is required for developing the component in isolation, documenting its various states and props visually, and enabling automated visual regression testing. Every major variant of the component must be represented as a story.

<component-name>.test.tsx: This file contains the unit and integration tests for the component, written using Vitest and React Testing Library. Tests should cover rendering, user interactions, and prop variations to ensure the component is robust and reliable.

index.ts: This is the barrel file responsible for exporting the component. Its purpose is to simplify import statements throughout the application. It should contain a single line:

TypeScript

export * from './<component-name>';
Example: Implementing a Button component

If you were to create a new Button component, the resulting structure must be:

ui/src/components/ui/button/
├── index.ts
├── button.tsx
├── button.stories.tsx
└── button.test.tsx
And the index.ts file would contain:

TypeScript

export * from './button';
This structure allows any other part of the application to import the button cleanly using: import { Button } from '@/components/ui/button';.

Rationale:

This strict structure is not arbitrary. It provides significant benefits by enforcing a consistent pattern that is predictable for all developers, simplifies code reviews, and allows our tooling to reliably locate files for testing, documentation, and code generation. Adherence to this rule is mandatory for all new component contributions.