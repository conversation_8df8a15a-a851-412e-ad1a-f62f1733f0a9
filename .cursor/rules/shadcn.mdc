---
description: 
globs: *.tsx,*.jsx,*.ts,*.js
alwaysApply: false
---
To ensure a cohesive, accessible, and maintainable user interface, we have standardized our frontend development on the Shadcn/UI component methodology.

This approach provides us with the combined benefits of:

Radix UI: For unstyled, fully accessible, and behavior-rich primitives that form the foundation of our components.
Tailwind CSS: For a utility-first styling approach that allows for rapid development while adhering to our design system.
Shadcn/UI: For well-architected, copy-and-paste components that tie this system together.
Guideline: When developing new UI features, do not introduce new, one-off CSS or third-party component libraries. Instead, build by composing components directly from the Shadcn/UI library or by creating new components that follow its architectural principles.