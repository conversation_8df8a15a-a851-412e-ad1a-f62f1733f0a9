---
description: 
globs: test_*.py,tests.py
alwaysApply: false
---
To maintain code quality, every new Django or Pytest test file must be validated upon creation. This is done by running our targeted test command: make test-file path=path/to/your/test_file.py. Should this command result in any test failures, a thorough investigation of both the test cases and their corresponding business logic (located in views, serializers, etc.) is required to identify and fix the issue.