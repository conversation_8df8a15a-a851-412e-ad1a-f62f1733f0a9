import os
import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils.translation import gettext_lazy as _
from drf_spectacular.utils import extend_schema
from django.template.exceptions import TemplateDoesNotExist

from core.decorators.error_handler import api_error_handler
from apps.accounts.auth.serializers import ChangePasswordSerializer


UI_BASE_URL = os.environ.get("UI_BASE_URL", "http://localhost:3000")
logger = logging.getLogger(__name__)


class ChangePasswordView(APIView):
    """
    API View for changing a logged-in user's password.
    """

    permission_classes = (IsAuthenticated,)
    serializer_class = ChangePasswordSerializer

    @api_error_handler
    @extend_schema(
        tags=["auth"],
        request=ChangePasswordSerializer,
        responses={200: {"description": "Password has been changed successfully."}},
    )
    def post(self, request, *args, **kwargs):
        """
        Process password change request.
        Validates current password and sets a new one.
        """
        try:
            serializer = self.serializer_class(
                data=request.data, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(
                {"message": _("Password has been changed successfully.")},
                status=status.HTTP_200_OK,
            )
        except TemplateDoesNotExist as e:
            logger.error(e)
            return Response(
                {"error": _("Email template does not exist.")},
                status=status.HTTP_400_BAD_REQUEST,
            )
