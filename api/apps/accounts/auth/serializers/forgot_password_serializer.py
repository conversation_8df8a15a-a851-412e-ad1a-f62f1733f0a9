import os
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers, status
from rest_framework.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import PasswordResetTokenGenerator

from core.tasks import send_activation_email
from apps.accounts.user.models import UserToken
from core.tasks.sms import send_sms_task

User = get_user_model()



def get_ui_base_url(key, default):
    """Get UI base URL from Django settings or environment variable"""
    return getattr(settings, key, os.environ.get(key, default))


UI_BASE_URL_CANDIDATE = get_ui_base_url("UI_BASE_URL_CANDIDATE", "http://localhost:3000")
UI_BASE_URL_JURY = get_ui_base_url("UI_BASE_URL_JURY", "http://localhost:3001")
UI_BASE_URL_ADMIN = get_ui_base_url("UI_BASE_URL_ADMIN", "http://localhost:3002")
UI_BASE_URL = get_ui_base_url("UI_BASE_URL", UI_BASE_URL_CANDIDATE)


class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    testing = serializers.BooleanField(required=False, default=False)

    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise ValidationError(_("User does not exist."))
        return value

    def get_ui_base_url_for_user(self, user):
        """
        Returns the appropriate UI base URL based on the user's role.
        """
        
        admin_url = get_ui_base_url("UI_BASE_URL_ADMIN", "http://localhost:3002")
        jury_url = get_ui_base_url("UI_BASE_URL_JURY", "http://localhost:3001")
        candidate_url = get_ui_base_url("UI_BASE_URL_CANDIDATE", "http://localhost:3000")
        
        if hasattr(user, "role"):
            
            if user.role == "admin" or user.is_staff or user.is_superuser:
                return admin_url
            
            elif user.role in ["coach", "doctor", "moderator"]:
                return jury_url

        
        return candidate_url

    def save(self, **kwargs):
        email = self.validated_data["email"]
        user = User.objects.get(email=email)

        base_url = self.get_ui_base_url_for_user(user)

        token = PasswordResetTokenGenerator().make_token(user)
        reset_link = f"{base_url}/auth/reset-password?token={token}"
        UserToken.objects.get_or_create(user=user, token=token)
        text_content = _(f"Click the link below to reset your password:\n{reset_link}")
        html_content = render_to_string(
            "forgot_password_email.html", {"reset_url": reset_link}
        )
        if "@" in email:
            send_activation_email(
                _("Password Reset Request"),
                text_content,
                email,
                html_content=html_content,
            )
        else:
            send_sms_task.delay(text_content, [email])
