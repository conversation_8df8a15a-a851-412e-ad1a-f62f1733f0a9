from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.template.loader import render_to_string
from django.db import models
from django.apps import apps

from apps.accounts.user.models import VerificationCode
from core.tasks import send_activation_email


User = get_user_model()
Country = apps.get_model("countries", "Country")


class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, min_length=8, max_length=128
    )
    email = serializers.EmailField(required=True)
    phone_number = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "fullname",
            "password",
            "phone_number",
        ]

    def validate_email(self, value):
        """
        Check that the email doesn't exist already (case-insensitive).
        """
        if value and User.objects.filter(email__iexact=value).exists():
            raise serializers.ValidationError(_("User with this email already exists."))

        return value.lower()

    def validate(self, data):
        phone_number = data.get("phone_number")
        if phone_number and User.objects.filter(phone_number=phone_number).exists():
            raise serializers.ValidationError(
                {"phone_number": _("User with this phone number already exists.")}
            )

        return data

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)
        verification_code = VerificationCode.objects.create(
            user=user, code=VerificationCode.generate_code()
        )

        text_content = _(f"Your verification code is: {verification_code.code}")
        html_content = render_to_string(
            "auth/emails/verification_code.html", {"code": verification_code.code}
        )

        send_activation_email.delay(
            subject=_("Verify Your Email"),
            text_content=text_content,
            html_content=html_content,
            to_email=user.email,
        )

        return user

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        try:
            rep["avatar"] = (
                self.context["request"].build_absolute_uri(instance.avatar.url)
                if instance.avatar
                else None
            )
        except (AttributeError, ValueError, KeyError):
            rep["avatar"] = None
        return rep


class EmailVerificationSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=6, min_length=6)
    email = serializers.EmailField()

    def validate_email(self, value):
        """Normalize email to lowercase and check if user exists"""
        email = value.lower()
        try:
            User.objects.get(email__iexact=email)
            return email
        except User.DoesNotExist:
            raise serializers.ValidationError(_("User does not exist"))

    def validate(self, data):
        code = data.get("code")
        email = data.get("email")

        try:
            user = User.objects.get(email__iexact=email)
            verification_code = VerificationCode.objects.get(code=code)

            if verification_code.user.pk != user.pk:
                raise serializers.ValidationError(
                    {"code": _("Invalid verification code for this email")}
                )

            if hasattr(user, "is_email_verified"):
                setattr(user, "is_email_verified", True)
                user.save()
            return data
        except VerificationCode.DoesNotExist:
            raise serializers.ValidationError({"code": _("Invalid verification code")})

    def create(self, validated_data):
        email = validated_data.get("email")
        code = validated_data.get("code")

        try:
            user = User.objects.get(email__iexact=email)
            verification_code = VerificationCode.objects.get(code=code, user=user)

            verification_code.delete()

            return {"email": email, "verified": True}
        except (User.DoesNotExist, VerificationCode.DoesNotExist):
            return validated_data


class ResendVerificationCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        """Normalize email to lowercase and check if user exists"""
        email = value.lower()
        try:
            User.objects.get(email__iexact=email)
            return email
        except User.DoesNotExist:
            raise serializers.ValidationError(_("User does not exist."))

    def create(self, validated_data):
        email = validated_data.get("email")

        user = User.objects.get(email__iexact=email)

        verification_code = VerificationCode.objects.filter(user=user).first()
        if not verification_code:
            verification_code = VerificationCode.objects.create(
                user=user, code=VerificationCode.generate_code()
            )

        text_content = _(f"Your verification code is: {verification_code.code}")
        html_content = render_to_string(
            "auth/emails/verification_code.html", {"code": verification_code.code}
        )

        send_activation_email.delay(
            subject=_("Verify Your Email"),
            text_content=text_content,
            html_content=html_content,
            to_email=user.email,
        )

        return user
