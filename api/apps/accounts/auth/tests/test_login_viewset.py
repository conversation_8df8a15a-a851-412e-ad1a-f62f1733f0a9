from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
import json

User = get_user_model()


class LoginViewSetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.login_url = "/api/v1/auth/login/"

        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            fullname="Test User",
            username="testuser",
        )

    def test_login_endpoint_accessibility(self):
        """Test that the login endpoint is accessible and doesn't return 500 errors."""
        data = {"email": "<EMAIL>", "password": "password123"}
        response = self.client.post(self.login_url, data)
        
        
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR, 
                          f"Login endpoint returned 500 error. Response: {response.content}")
        
        
        self.assertIn(response.status_code, [
            status.HTTP_200_OK, 
            status.HTTP_400_BAD_REQUEST, 
            status.HTTP_401_UNAUTHORIZED
        ], f"Unexpected status code: {response.status_code}. Response: {response.content}")

    def test_login_with_valid_credentials_returns_200(self):
        """Test that valid credentials return 200 OK."""
        data = {"email": "<EMAIL>", "password": "password123"}
        response = self.client.post(self.login_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK, 
                        f"Expected 200 but got {response.status_code}. Response: {response.content}")
        
        
        response_data = response.json()
        self.assertIn("access", response_data)
        self.assertIn("refresh", response_data)
        self.assertIn("user", response_data)

    def test_login_viewset_case_insensitive_email(self):
        """Test that the login viewset handles case-insensitive emails."""

        data = {"email": "<EMAIL>", "password": "password123"}
        response = self.client.post(self.login_url, data)
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)
        self.assertIn("refresh", response.data)
        self.assertIn("user", response.data)

        data = {"email": "<EMAIL>", "password": "password123"}
        response = self.client.post(self.login_url, data)
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)

        data = {"email": "<EMAIL>", "password": "password123"}
        response = self.client.post(self.login_url, data)
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)

    def test_login_viewset_invalid_credentials(self):
        """Test that the login viewset rejects invalid credentials."""
        data = {"email": "<EMAIL>", "password": "wrongpassword"}
        response = self.client.post(self.login_url, data)
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_viewset_nonexistent_user(self):
        """Test that the login viewset rejects non-existent users."""
        data = {"email": "<EMAIL>", "password": "password123"}
        response = self.client.post(self.login_url, data)
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_with_missing_fields(self):
        """Test login with missing required fields."""
        
        response = self.client.post(self.login_url, {"email": "<EMAIL>"})
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        response = self.client.post(self.login_url, {"password": "password123"})
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        response = self.client.post(self.login_url, {})
        self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_database_connectivity(self):
        """Test that database is accessible during tests."""
        user_count = User.objects.count()
        self.assertEqual(user_count, 1, "Should have exactly one user created in setUp")
        
        
        test_user = User.objects.create_user(
            email="<EMAIL>",
            password="test123",
            fullname="Test DB User",
            username="testdbuser",
        )
        self.assertIsNotNone(test_user.id, "User should be saved to database")
        
        
        test_user.delete()
