from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from django.apps import apps

User = get_user_model()


class RegisterViewSetTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.register_url = "/api/v1/auth/register/"

        self.existing_user = User.objects.create_user(
            email="<EMAIL>",
            password="password123",
            fullname="Existing User",
            username="existinguser",
        )

        self.existing_user.save()

        assert User.objects.filter(email="<EMAIL>").exists()

        self.test_country = apps.get_model("countries", "Country").objects.create(
            name="Test Country", iso2="TC", iso3="TCY"
        )

    def tearDown(self):
        User.objects.all().delete()

    def test_register_viewset_lowercase_email(self):
        """Test that the register viewset converts emails to lowercase."""

        User.objects.filter(email__iexact="<EMAIL>").delete()

        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "New User",
        }

        response = self.client.post(self.register_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        user = User.objects.get(fullname="New User")
        self.assertEqual(user.email, "<EMAIL>")

    def test_register_viewset_existing_email(self):
        """Test that the register viewset rejects existing emails regardless of case."""

        User.objects.filter(email__iexact="<EMAIL>").exclude(
            id=self.existing_user.id
        ).delete()

        self.existing_user.email = "<EMAIL>"
        self.existing_user.save()

        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())

        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "New User",
        }

        response = self.client.post(self.register_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "New User 2",
        }

        self.assertTrue(User.objects.filter(email="<EMAIL>").exists())

        response = self.client.post(self.register_url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_register_viewset_with_phone_number(self):
        """Test registration with phone_number field."""
        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "Phone Number User",
            "phone_number": "1234567890",
        }

        response = self.client.post(self.register_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        user = User.objects.get(email="<EMAIL>")
        self.assertEqual(user.phone_number, "1234567890")

    def test_register_viewset_ignores_location_and_country(self):
        """Test that location and country fields are ignored during registration."""
        data = {
            "email": "<EMAIL>",
            "password": "password123",
            "fullname": "Fields User",
            "location": "Should be ignored",
            "country": self.test_country.id if hasattr(self, "test_country") else 1,
        }

        response = self.client.post(self.register_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        user = User.objects.get(email="<EMAIL>")
        # The User model doesn't have a location field, so we can't test it
        # Country field exists but should be None if ignored during registration
        self.assertIsNone(user.country)
