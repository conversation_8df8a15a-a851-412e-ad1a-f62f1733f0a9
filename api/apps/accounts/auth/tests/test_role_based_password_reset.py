import os
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient

from apps.accounts.user.models import User, UserToken
from apps.accounts.auth.serializers.forgot_password_serializer import (
    ForgotPasswordSerializer,
)
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes

User = get_user_model()


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
    EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend'
)
class RoleBasedPasswordResetTests(APITestCase):
    """
    Test role-based password reset functionality.

    Different user roles should receive different UI base URLs:
    - Admins - UI_BASE_URL_ADMIN
    - Regular users (users) - UI_BASE_URL_CANDIDATE
    """

    def setUp(self):
        
        self.regular_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="user",
        )

        
        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.client = APIClient()
        self.forgot_password_url = reverse("auth-forgot-password")

        
        self.admin_url = "http://test-admin-ui.com"
        self.regular_url = "http://test-candidate-ui.com"

    @override_settings(
        UI_BASE_URL_ADMIN="http://test-admin-ui.com",
        UI_BASE_URL_CANDIDATE="http://test-candidate-ui.com",
    )
    @patch("apps.accounts.auth.serializers.forgot_password_serializer.send_activation_email")
    def test_regular_user_reset_link(self, mock_send_email):
        """Test that regular users receive the correct UI base URL in reset links."""

        response = self.client.post(
            self.forgot_password_url, {"email": self.regular_user.email}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        
        mock_send_email.assert_called_once()
        call_args = mock_send_email.call_args

        
        self.assertIn(
            self.regular_url, call_args[0][1]
        )  

    @override_settings(
        UI_BASE_URL_ADMIN="http://test-admin-ui.com",
        UI_BASE_URL_CANDIDATE="http://test-candidate-ui.com",
    )
    @patch("apps.accounts.auth.serializers.forgot_password_serializer.send_activation_email")
    def test_admin_reset_link(self, mock_send_email):
        """Test that admin users receive the correct UI base URL in reset links."""

        response = self.client.post(
            self.forgot_password_url, {"email": self.admin_user.email}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        
        mock_send_email.assert_called_once()
        call_args = mock_send_email.call_args

        
        self.assertIn(
            self.admin_url, call_args[0][1]
        )  

    @override_settings(
        UI_BASE_URL_ADMIN="http://test-admin-ui.com",
        UI_BASE_URL_CANDIDATE="http://test-candidate-ui.com",
    )
    def test_serializer_url_selection_admin(self):
        """Test that the serializer selects the correct URL for admin users."""
        serializer = ForgotPasswordSerializer()
        url = serializer.get_ui_base_url_for_user(self.admin_user)
        self.assertEqual(url, self.admin_url)

    @override_settings(
        UI_BASE_URL_ADMIN="http://test-admin-ui.com",
        UI_BASE_URL_CANDIDATE="http://test-candidate-ui.com",
    )
    def test_serializer_url_selection_regular_user(self):
        """Test that the serializer selects the correct URL for regular users."""
        serializer = ForgotPasswordSerializer()
        url = serializer.get_ui_base_url_for_user(self.regular_user)
        self.assertEqual(url, self.regular_url)

    @override_settings(
        UI_BASE_URL_ADMIN="http://test-admin-ui.com",
        UI_BASE_URL_CANDIDATE="http://test-candidate-ui.com",
    )
    @patch("apps.accounts.auth.serializers.forgot_password_serializer.send_activation_email")
    def test_token_generation(self, mock_send_email):
        """Test that password reset tokens are generated correctly."""

        response = self.client.post(
            self.forgot_password_url, {"email": self.regular_user.email}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        
        token = UserToken.objects.filter(user=self.regular_user).first()
        self.assertIsNotNone(token)
        self.assertEqual(token.user, self.regular_user)

    def test_invalid_email(self):
        """Test password reset with invalid email."""

        response = self.client.post(
            self.forgot_password_url, {"email": "<EMAIL>"}
        )

        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
