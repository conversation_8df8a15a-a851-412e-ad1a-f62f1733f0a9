from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework.response import Response
from rest_framework import status
from apps.accounts.user.models import User
from apps.accounts.user.filters.filters import UserFilter
from typing import Set, Dict, Any, cast
from django.contrib.auth import get_user_model
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.test import APIRequestFactory

User = get_user_model()


class UserFilterTests(TestCase):
    def setUp(self):
        self.dev_admin1 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="user"
        )

        self.dev_admin2 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="user"
        )

        self.business_admin = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="admin"
        )

        self.dev_admin1.fullname = "<PERSON>"
        self.dev_admin1.save()

        self.dev_admin2.fullname = "<PERSON>"
        self.dev_admin2.save()

        self.business_admin.fullname = "Admin User"
        self.business_admin.save()

    def test_filter_by_role(self):
        """Test filtering users by role"""

        all_users = User.objects.all()
        self.assertEqual(all_users.count(), 3)

        filterset = UserFilter({"role": ["user"]}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 2)

        filterset = UserFilter({"role": ["admin"]}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)

    def test_filter_by_created_at(self):
        """Test filtering users by created date"""

        all_users = User.objects.all()

        today = User.objects.first().created.date()

        filterset = UserFilter({"created_after": today}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 3)

        import datetime

        yesterday = today - datetime.timedelta(days=1)
        filterset = UserFilter({"created_before": yesterday}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 0)

    def test_filter_by_email(self):
        """Test filtering users by email"""

        all_users = User.objects.all()

        filterset = UserFilter({"email": "hero"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 2)

        filterset = UserFilter({"email_exact": "<EMAIL>"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)

    def test_filter_by_fullname(self):
        """Test filtering users by fullname"""

        all_users = User.objects.all()

        filterset = UserFilter({"fullname": "John"}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 1)

    def test_filter_by_staff_status(self):
        """Test filtering users by staff status"""

        all_users = User.objects.all()

        filterset = UserFilter({"is_staff": False}, queryset=all_users)
        self.assertEqual(filterset.qs.count(), 3)

    def test_combined_filters(self):
        """Test combining multiple filters"""

        all_users = User.objects.all()

        filterset = UserFilter(
            {"role": ["user"], "fullname": "John"}, queryset=all_users
        )
        self.assertEqual(filterset.qs.count(), 1)


@override_settings(RATE_LIMITER_ENABLED=False)
class UserFilterAPITests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse("list-user")

        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            role="admin",
            is_staff=True,
            is_superuser=True,
        )

        self.dev_admin1 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="user"
        )

        self.dev_admin2 = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="user"
        )

        self.business_admin = User.objects.create_user(
            email="<EMAIL>", password="testpass123", role="admin"
        )

        self.client.force_authenticate(user=self.admin_user)

    def test_api_filter_by_role(self):
        """Test API filtering users by role"""

        response = cast(Response, self.client.get(f"{self.url}?role=user"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = response.data["results"]
        self.assertEqual(len(results), 2)

        for user in results:
            self.assertEqual(user["role"], "user")

    def test_api_filter_by_email(self):
        """Test API filtering users by email"""

        response = cast(Response, self.client.get(f"{self.url}?email=hero1"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = response.data["results"]
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["email"], "<EMAIL>")

    @override_settings(RATE_LIMITER_ENABLED=False)
    def test_api_search(self):
        """Test API search functionality"""

        response = cast(Response, self.client.get(f"{self.url}?search=hero"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        results = response.data["results"]
        self.assertEqual(len(results), 2)

    def test_api_pagination(self):
        """Test API pagination"""

        response = cast(Response, self.client.get(f"{self.url}?page=1"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn("meta", response.data)
        self.assertIn("results", response.data)

    def test_unauthorized_access(self):
        """Test that non-admin users can now access the user list"""

        self.client.force_authenticate(user=self.business_admin)

        response = cast(Response, self.client.get(self.url))
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access the user list"""

        self.client.force_authenticate(user=None)

        response = cast(Response, self.client.get(self.url))
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
