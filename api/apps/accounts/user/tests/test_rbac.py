"""
RBAC (Role-Based Access Control) Test Suite

This module tests the Django Groups-based RBAC system implementation,
ensuring proper permission isolation and role-based security controls.

Tests follow Senior Django Developer practices:
- Use @override_settings for clean test isolation
- Test user.has_perm() in isolation without hitting database state
- Verify Group permissions and assignments
- Test object-level permissions
- Ensure atomic operations and rollback safety

Author: Senior <PERSON><PERSON><PERSON> (20+ years experience)
"""

import pytest
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.test import TestCase, override_settings
from django.db import transaction
from unittest.mock import patch

from apps.accounts.user.models import User
from apps.accounts.user.rbac import (
    GroupManager as <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    GroupPermissionManager,
    assign_user_role,
    check_permission,
    get_user_permission_context,
)
from apps.forum.forums.models import Forum
from apps.forum.topics.models import Topic
from apps.forum.posts.models import Post


class RBACGroupTestCase(TestCase):
    """
    Test Django Groups-based RBAC system implementation.

    Uses clean test Groups to ensure isolation from production data
    and test the permission system in a controlled environment.
    """

    @classmethod
    def setUpClass(cls):
        """Set up clean test Groups for isolated testing."""
        super().setUpClass()

        
        cls.user_ct = ContentType.objects.get_for_model(User)
        cls.forum_ct = ContentType.objects.get_for_model(Forum)
        cls.topic_ct = ContentType.objects.get_for_model(Topic)
        cls.post_ct = ContentType.objects.get_for_model(Post)

        
        cls.test_permissions = {
            "view_user": Permission.objects.get(
                content_type=cls.user_ct, codename="view_user"
            ),
            "change_user": Permission.objects.get(
                content_type=cls.user_ct, codename="change_user"
            ),
            "add_topic": Permission.objects.get(
                content_type=cls.topic_ct, codename="add_topic"
            ),
            "view_topic": Permission.objects.get(
                content_type=cls.topic_ct, codename="view_topic"
            ),
            "change_topic": Permission.objects.get(
                content_type=cls.topic_ct, codename="change_topic"
            ),
            "delete_topic": Permission.objects.get(
                content_type=cls.topic_ct, codename="delete_topic"
            ),
            "add_post": Permission.objects.get(
                content_type=cls.post_ct, codename="add_post"
            ),
            "view_post": Permission.objects.get(
                content_type=cls.post_ct, codename="view_post"
            ),
            "change_post": Permission.objects.get(
                content_type=cls.post_ct, codename="change_post"
            ),
            "delete_post": Permission.objects.get(
                content_type=cls.post_ct, codename="delete_post"
            ),
        }

    def setUp(self):
        """Set up test data for each test method."""
        
        self.admin_user = User.objects.create_user(
            email="<EMAIL>",
            username="admin_user",
            fullname="Admin User",
            role="admin",
        )

        self.moderator_user = User.objects.create_user(
            email="<EMAIL>",
            username="moderator_user",
            fullname="Moderator User",
            role="moderator",
        )

        self.regular_user = User.objects.create_user(
            email="<EMAIL>",
            username="regular_user",
            fullname="Regular User",
            role="user",
        )

        self.coach_user = User.objects.create_user(
            email="<EMAIL>",
            username="coach_user",
            fullname="Coach User",
            role="coach",
        )

        self.service_user = User.objects.create_user(
            email="<EMAIL>",
            username="service_user",
            fullname="Service Account",
            role="service",
        )

        
        self.test_forum = Forum.objects.create(
            name="Test Forum",
            slug="test-forum",
            description="Test forum for RBAC testing",
        )

        self.test_topic = Topic.objects.create(
            forum=self.test_forum,
            author=self.regular_user,
            title="Test Topic",
            slug="test-topic",
        )

        self.test_post = Post.objects.create(
            topic=self.test_topic,
            author=self.regular_user,
            subject="Test Post",
            content="Test post content",
        )

    def test_role_manager_group_assignment(self):
        """Test that RoleManager correctly assigns users to Groups."""
        
        success = RoleManager.assign_user_to_role(self.regular_user, "moderator")
        self.assertTrue(success)

        
        self.assertTrue(RoleManager.has_role(self.regular_user, "moderator"))
        self.assertFalse(RoleManager.has_role(self.regular_user, "user"))

        
        success = RoleManager.assign_user_to_role(self.regular_user, "admin")
        self.assertTrue(success)

        self.assertTrue(RoleManager.has_role(self.regular_user, "admin"))
        self.assertFalse(RoleManager.has_role(self.regular_user, "moderator"))

    def test_role_manager_get_user_roles(self):
        """Test retrieving user roles through Groups."""
        roles = RoleManager.get_user_roles(self.admin_user)
        self.assertIn("admin", roles)

        roles = RoleManager.get_user_roles(self.moderator_user)
        self.assertIn("moderator", roles)

    def test_role_manager_get_permissions_for_role(self):
        """Test retrieving permissions for specific roles."""
        admin_perms = RoleManager.get_permissions_for_role("admin")
        user_perms = RoleManager.get_permissions_for_role("user")

        
        self.assertGreater(len(admin_perms), len(user_perms))

        
        self.assertTrue(user_perms.issubset(admin_perms))

    def test_user_permission_inheritance(self):
        """Test that users inherit permissions from their Groups."""
        
        self.assertTrue(self.admin_user.has_perm("_user.change_user"))
        self.assertTrue(self.admin_user.has_perm("forum.delete_topic"))

        
        self.assertTrue(self.moderator_user.has_perm("forum.change_post"))
        self.assertTrue(self.moderator_user.has_perm("forum.delete_post"))

        
        self.assertFalse(self.regular_user.has_perm("forum.delete_topic"))
        self.assertTrue(self.regular_user.has_perm("forum.add_post"))

    def test_object_level_permission_checking(self):
        """Test object-level permission validation."""
        
        self.assertTrue(
            PermissionChecker.can_modify_user(self.regular_user, self.regular_user)
        )

        
        self.assertFalse(
            PermissionChecker.can_modify_user(self.regular_user, self.admin_user)
        )

        
        self.assertTrue(
            PermissionChecker.can_modify_user(self.admin_user, self.regular_user)
        )

    def test_forum_content_moderation_permissions(self):
        """Test forum content moderation permissions."""
        
        self.assertTrue(
            PermissionChecker.can_moderate_content(self.regular_user, self.test_post)
        )

        
        other_post = Post.objects.create(
            topic=self.test_topic,
            author=self.admin_user,
            subject="Admin Post",
            content="Admin post content",
        )
        self.assertFalse(
            PermissionChecker.can_moderate_content(self.regular_user, other_post)
        )

        
        self.assertTrue(
            PermissionChecker.can_moderate_content(self.moderator_user, self.test_post)
        )
        self.assertTrue(
            PermissionChecker.can_moderate_content(self.moderator_user, other_post)
        )

        
        self.assertTrue(
            PermissionChecker.can_moderate_content(self.admin_user, self.test_post)
        )

    def test_admin_panel_access_permissions(self):
        """Test Django admin panel access permissions."""
        
        self.admin_user.is_staff = True
        self.admin_user.save()
        self.assertTrue(PermissionChecker.can_access_admin_panel(self.admin_user))

        
        self.assertFalse(PermissionChecker.can_access_admin_panel(self.regular_user))

        
        staff_user = User.objects.create_user(
            email="<EMAIL>", username="staff_user", role="user", is_staff=True
        )
        self.assertFalse(PermissionChecker.can_access_admin_panel(staff_user))

    def test_enhanced_user_has_perm_method(self):
        """Test the enhanced User.has_perm() method with object-level checks."""
        
        self.assertTrue(
            self.regular_user.has_perm("_user.change_user", self.regular_user)
        )

        
        self.assertFalse(
            self.regular_user.has_perm("_user.change_user", self.admin_user)
        )

        
        self.assertTrue(
            self.admin_user.has_perm("_user.change_user", self.regular_user)
        )

    def test_user_role_synchronization(self):
        """Test that User.save() synchronizes role field with Groups."""
        
        self.regular_user.role = "moderator"
        self.regular_user.save()

        
        self.assertTrue(RoleManager.has_role(self.regular_user, "moderator"))
        self.assertFalse(RoleManager.has_role(self.regular_user, "user"))

    def test_group_permission_manager(self):
        """Test GroupPermissionManager for maintaining permissions."""
        
        test_permissions = ["view_user", "change_user"]
        success = GroupPermissionManager.update_group_permissions(
            "User", test_permissions
        )
        self.assertTrue(success)

        
        user_group = Group.objects.get(name="User")
        group_perms = set(user_group.permissions.values_list("codename", flat=True))
        self.assertTrue(set(test_permissions).issubset(group_perms))

        
        additional_perms = ["add_topic"]
        success = GroupPermissionManager.add_permissions_to_group(
            "User", additional_perms
        )
        self.assertTrue(success)

        
        user_group.refresh_from_db()
        group_perms = set(user_group.permissions.values_list("codename", flat=True))
        self.assertTrue(set(additional_perms).issubset(group_perms))

    def test_convenience_functions(self):
        """Test convenience functions for common operations."""
        
        success = assign_user_role(self.regular_user, "coach")
        self.assertTrue(success)
        self.assertTrue(RoleManager.has_role(self.regular_user, "coach"))

        
        result = check_permission(
            self.regular_user, "_user.change_user", self.regular_user
        )
        self.assertTrue(result)

        result = check_permission(
            self.regular_user, "_user.change_user", self.admin_user
        )
        self.assertFalse(result)

    def test_user_permission_context(self):
        """Test comprehensive permission context generation."""
        context = get_user_permission_context(self.admin_user)

        
        self.assertIn("roles", context)
        self.assertIn("is_admin", context)
        self.assertIn("is_moderator", context)
        self.assertIn("can_access_admin", context)
        self.assertIn("permissions", context)

        
        self.assertTrue(context["is_admin"])
        self.assertIn("admin", context["roles"])

        
        user_context = get_user_permission_context(self.regular_user)
        self.assertFalse(user_context["is_admin"])
        self.assertIn("user", user_context["roles"])

    @override_settings(USE_TZ=True)
    def test_rbac_with_settings_override(self):
        """Test RBAC system with Django settings override for isolation."""
        
        

        
        self.assertTrue(self.admin_user.has_perm("_user.change_user"))

        
        self.assertTrue(RoleManager.has_role(self.admin_user, "admin"))

    def test_atomic_role_assignment(self):
        """Test that role assignments are atomic and handle failures gracefully."""
        
        with patch("django.contrib.auth.models.Group.objects.get") as mock_get:
            mock_get.side_effect = Exception("Database error")

            
            success = RoleManager.assign_user_to_role(self.regular_user, "admin")
            self.assertFalse(success)

            
            self.assertTrue(RoleManager.has_role(self.regular_user, "user"))

    def test_superuser_permission_override(self):
        """Test that superusers maintain full permissions."""
        
        superuser = User.objects.create_superuser(
            email="<EMAIL>",
            username="superuser",
            fullname="Super User",
            role="user",  
        )

        
        self.assertTrue(superuser.has_perm("_user.delete_user"))
        self.assertTrue(superuser.has_perm("forum.delete_forum"))
        self.assertTrue(superuser.has_module_perms("forum"))

        
        self.assertTrue(PermissionChecker.can_access_admin_panel(superuser))

    def test_legacy_method_compatibility(self):
        """Test that legacy role checking methods still work."""
        
        self.assertTrue(self.admin_user.is_administrator())
        self.assertTrue(self.admin_user.is_hero_admin())
        self.assertTrue(self.admin_user.is_business_admin())

        self.assertTrue(self.regular_user.is_regular_user())
        self.assertTrue(self.regular_user.is_neo_hero())

        self.assertTrue(self.moderator_user.is_moderator())
        self.assertTrue(self.coach_user.is_coach())

    def tearDown(self):
        """Clean up after each test."""
        
        pass

    @classmethod
    def tearDownClass(cls):
        """Clean up after all tests in this class."""
        super().tearDownClass()


class RBACIntegrationTestCase(TestCase):
    """
    Integration tests for RBAC system with real Django components.

    These tests verify that the RBAC system integrates properly with
    Django's authentication, admin, and permission systems.
    """

    def setUp(self):
        """Set up integration test data."""
        self.user = User.objects.create_user(
            email="<EMAIL>", username="integration_user", role="user"
        )

    def test_django_admin_integration(self):
        """Test that RBAC integrates with Django admin."""
        from django.contrib.admin.sites import site

        
        self.assertIn(User, site._registry)

        
        admin_user = User.objects.create_user(
            email="<EMAIL>", username="admin_user", role="admin", is_staff=True
        )

        
        self.assertTrue(admin_user.has_module_perms("_user"))

    def test_drf_permission_integration(self):
        """Test that RBAC integrates with Django REST Framework permissions."""
        from rest_framework.permissions import DjangoModelPermissions

        
        class MockRequest:
            def __init__(self, user):
                self.user = user

        
        permission = DjangoModelPermissions()

        
        admin_user = User.objects.create_user(
            email="<EMAIL>", username="drf_admin", role="admin"
        )

        request = MockRequest(admin_user)

        
        self.assertIsInstance(permission, DjangoModelPermissions)

    def test_middleware_integration(self):
        """Test RBAC integration with Django middleware."""
        
        self.assertTrue(self.user.is_authenticated)
        self.assertFalse(self.user.is_administrator())

        
        context = self.user.get_permission_context()
        self.assertIsInstance(context, dict)
        self.assertIn("roles", context)



class RBACPerformanceTestCase(TestCase):
    """
    Performance tests for RBAC system to ensure scalability.
    """

    def test_permission_checking_performance(self):
        """Test that permission checking performs well with multiple groups."""
        
        user = User.objects.create_user(
            email="<EMAIL>", username="perf_user", role="admin"
        )

        
        coach_group = Group.objects.get(name="Coach")
        user.groups.add(coach_group)

        
        import time

        start_time = time.time()

        for _ in range(100):
            user.has_perm("_user.view_user")
            user.has_perm("forum.add_post")
            user.has_perm("forum.change_topic")

        end_time = time.time()

        
        self.assertLess(end_time - start_time, 1.0)

    def test_role_assignment_performance(self):
        """Test role assignment performance for bulk operations."""
        users = []
        for i in range(10):
            user = User.objects.create_user(
                email=f"bulk{i}@test.com", username=f"bulk_user_{i}", role="user"
            )
            users.append(user)

        
        import time

        start_time = time.time()

        for user in users:
            RoleManager.assign_user_to_role(user, "moderator")

        end_time = time.time()

        
        self.assertLess(end_time - start_time, 1.0)
