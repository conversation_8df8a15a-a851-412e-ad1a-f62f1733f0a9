import random

from django.contrib.auth.models import (
    AbstractBaseUser,
    PermissionsMixin,
)
from django.db import models
from django.utils.translation import gettext as _

from core.abstract.models import AbstractAutoIncrementModel

from .constants import ROLE_CHOICES
from .managers import User<PERSON><PERSON><PERSON>, VerificationCodeManager


def user_directory_path(instance, filename):
    return "user_{0}/{1}".format(instance.id, filename)


class User(AbstractAutoIncrementModel, AbstractBaseUser, PermissionsMixin):
    username = models.CharField(db_index=True, max_length=255, unique=True)
    email = models.EmailField(
        db_index=True, unique=True, max_length=255, blank=True, null=True
    )
    fullname = models.CharField(max_length=255, blank=True, null=True)
    is_superuser = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)
    is_email_verified = models.BooleanField(default=False)
    avatar = models.ImageField(null=True, blank=True, upload_to=user_directory_path)
    phone_number = models.CharField(max_length=15, unique=True, null=True, blank=True)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default="user")
    country = models.ForeignKey(
        "countries.Country",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="users",
    )

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["fullname"]

    objects: UserManager = UserManager()

    def __str__(self):
        return f"{self.email}" or f"{self.fullname}"

    def has_perm(self, perm, obj=None):
        """
        Enhanced permission checking that integrates Django Groups with object-level permissions.

        This method extends Django's standard permission checking to include:
        - Standard Django Groups permissions
        - Object-level permission validation
        - Superuser privilege override

        Args:
            perm: Permission string in format 'app_label.action_modelname'
            obj: Optional object for object-level permission checks

        Returns:
            bool: True if user has permission
        """

        if self.is_superuser:
            return True

        from .rbac import check_permission

        return check_permission(self, perm, obj)

    def has_module_perms(self, app_label):
        """
        Check if user has any permissions for the given app.

        Superusers have access to all modules. Regular users are checked
        against their group permissions.
        """
        if self.is_superuser:
            return True

        return super().has_module_perms(app_label)

    def get_permission_context(self):
        """
        Get user's permission context for use in templates and APIs.

        Returns:
            dict: Dictionary containing user's groups and capabilities
        """

        from .rbac import get_user_permission_context

        return get_user_permission_context(self)

    def get_groups(self):
        """
        Get all Django Groups assigned to this user.

        Note: Groups are assigned explicitly via management commands,
        not automatically based on role field.

        Returns:
            QuerySet: User's assigned groups
        """
        return self.groups.all()

    def is_admin(self):
        """Check if user has admin role (legacy method)."""
        return self.role == "admin" or self.is_superuser

    def is_coach(self):
        """Check if user has coach role (legacy method)."""
        return self.role == "coach"

    def is_doctor(self):
        """Check if user has doctor role (legacy method)."""
        return self.role == "doctor"

    def is_moderator(self):
        """Check if user has moderator role (legacy method)."""
        return self.role == "moderator"

    @property
    def full_name(self):
        return self.fullname or self.username

    def save(self, *args, **kwargs):
        """
        Enhanced save method for User model.

        Note: Group assignment is handled explicitly via management commands
        or admin interface, never automatically in model save methods.
        """

        if self.is_superuser:
            self.is_staff = True

        if not self.username:
            self.username = str(random.randint(1000000000000000, 9999999999999999))

        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "User"
        verbose_name_plural = "Users"
        db_table = "_user_user"

        permissions = [
            ("can_manage_user_roles", "Can manage user roles"),
            ("can_view_admin_stats", "Can view admin statistics"),
            ("can_moderate_all_content", "Can moderate all forum content"),
            ("can_access_api", "Can access API endpoints"),
        ]

        indexes = [
            models.Index(fields=["email"]),
            models.Index(fields=["username"]),
            models.Index(fields=["role"]),
            models.Index(fields=["created"]),
        ]


class VerificationCode(AbstractAutoIncrementModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    code = models.CharField(max_length=6)
    is_used = models.BooleanField(default=False)

    objects: VerificationCodeManager = VerificationCodeManager()

    @staticmethod
    def generate_code():
        return "".join(random.choices("0123456789", k=6))

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = self.generate_code()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.code}"

    class Meta:
        ordering = ["-created"]


class UserToken(AbstractAutoIncrementModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    token = models.CharField(max_length=255)

    class Meta:
        verbose_name = "User Token"
        verbose_name_plural = "User Tokens"
        indexes = [
            models.Index(fields=["token"]),
        ]
