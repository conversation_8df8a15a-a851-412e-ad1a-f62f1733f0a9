

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("_user", "0015_create_rbac_groups"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="user",
            options={
                "permissions": [
                    ("can_manage_user_roles", "Can manage user roles"),
                    ("can_view_admin_stats", "Can view admin statistics"),
                    ("can_moderate_all_content", "Can moderate all forum content"),
                    ("can_access_api", "Can access API endpoints"),
                ],
                "verbose_name": "User",
                "verbose_name_plural": "Users",
            },
        ),
        migrations.RenameIndex(
            model_name="user",
            new_name="users_email_4b85f2_idx",
            old_name="Duser_user_email_2817fd_idx",
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["username"], name="users_usernam_baeb4b_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["role"], name="users_role_0ace22_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["created"], name="users_created_1ac26f_idx"),
        ),
        migrations.AlterModelTable(
            name="user",
            table="users",
        ),
    ]
