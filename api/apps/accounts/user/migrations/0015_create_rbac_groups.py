from django.db import migrations
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType


def create_rbac_groups(apps, schema_editor):
    """
    Create RBAC groups and assign permissions following Django security best practices.

    This function establishes a comprehensive role-based access control system using
    Django's built-in Group and Permission framework. Each role is carefully designed
    to provide appropriate access levels while maintaining security principles.

    Roles Created:
    - User: Standard platform users with basic self-service capabilities
    - Coach: Healthcare providers for client management (future functionality)
    - Doctor: Medical professionals with enhanced data access (future functionality)
    - Moderator: Community moderators with content management capabilities
    - Admin: Platform administrators with comprehensive system access
    - Service: Non-human service accounts for API integrations
    """

    
    try:
        user_ct = ContentType.objects.get(app_label="_user", model="user")
    except ContentType.DoesNotExist:
        
        print("Warning: ContentType for _user.user not found, skipping RBAC setup")
        return

    try:
        forum_ct = ContentType.objects.get(app_label="forum_forums", model="forum")
    except ContentType.DoesNotExist:
        forum_ct = None
    
    try:
        topic_ct = ContentType.objects.get(app_label="forum_topics", model="topic")
    except ContentType.DoesNotExist:
        topic_ct = None
    
    try:
        post_ct = ContentType.objects.get(app_label="forum_posts", model="post")
    except ContentType.DoesNotExist:
        post_ct = None

    
    
    user_group, created = Group.objects.get_or_create(name="User")

    
    user_permissions = Permission.objects.filter(
        codename__in=[
            
            "view_user",
            "change_user",  
            
            "add_topic",
            "view_topic",
            "add_post",
            "view_post",
            "change_post",  
            "delete_post",  
            
            
        ]
    )
    user_group.permissions.set(user_permissions)

    
    
    coach_group, created = Group.objects.get_or_create(name="Coach")

    
    coach_permissions = Permission.objects.filter(
        codename__in=[
            
            "add_topic",
            "view_topic",
            "add_post",
            "view_post",
            "change_post",
            "delete_post",
            "view_user",
            "change_user",
            
            
            
        ]
    )
    coach_group.permissions.set(coach_permissions)

    
    
    doctor_group, created = Group.objects.get_or_create(name="Doctor")

    
    doctor_permissions = Permission.objects.filter(
        codename__in=[
            
            "add_topic",
            "view_topic",
            "add_post",
            "view_post",
            "change_post",
            "delete_post",
            "view_user",
            "change_user",
            
            
        ]
    )
    doctor_group.permissions.set(doctor_permissions)

    
    
    moderator_group, created = Group.objects.get_or_create(name="Moderator")

    
    moderator_permissions = Permission.objects.filter(
        codename__in=[
            
            "add_topic",
            "view_topic",
            "add_post",
            "view_post",
            "view_user",
            
            "change_topic",  
            "delete_topic",  
            "change_post",  
            "delete_post",  
            "change_forum",  
            
            
        ]
    )
    moderator_group.permissions.set(moderator_permissions)

    
    
    admin_group, created = Group.objects.get_or_create(name="Admin")

    
    admin_permissions = Permission.objects.filter(
        content_type__in=[user_ct, forum_ct, topic_ct, post_ct]
    ).filter(
        codename__in=[
            
            "add_user",
            "view_user",
            "change_user",
            "delete_user",
            
            "add_forum",
            "view_forum",
            "change_forum",
            "delete_forum",
            "add_topic",
            "view_topic",
            "change_topic",
            "delete_topic",
            "add_post",
            "view_post",
            "change_post",
            "delete_post",
            
        ]
    )
    admin_group.permissions.set(admin_permissions)

    
    
    service_group, created = Group.objects.get_or_create(name="Service")

    
    service_permissions = Permission.objects.filter(
        codename__in=[
            
            "view_user",  
            "change_user",  
            "view_topic",  
            "view_post",  
            
        ]
    )
    service_group.permissions.set(service_permissions)


def reverse_rbac_groups(apps, schema_editor):
    """
    Reverse the RBAC group creation.

    This removes all created groups and their permission assignments,
    ensuring the migration is fully reversible for testing and rollback scenarios.
    """
    
    group_names = ["User", "Coach", "Doctor", "Moderator", "Admin", "Service"]

    
    Group.objects.filter(name__in=group_names).delete()


class Migration(migrations.Migration):
    """
    RBAC Groups and Permissions Migration

    This migration establishes a comprehensive role-based access control system
    using Django's built-in Group and Permission framework. Each role is defined
    as a Group with specific permissions that align with the platform's security
    model and user requirements.

    Groups Created:
    - User: Regular platform users with self-service capabilities
    - Coach: Healthcare providers with client management features (future)
    - Doctor: Medical professionals with enhanced data access (future)
    - Moderator: Community moderators with content management powers
    - Admin: Platform administrators with full system access
    - Service: API service accounts with limited, specific permissions

    Security Considerations:
    - Permissions are assigned using codename filtering for maintainability
    - View-level permission checks will enforce object-level security
    - Service accounts require additional API key authentication
    - Admin users should also have is_staff=True for Django admin access
    """

    dependencies = [
        ("_user", "0014_merge_20250511_1654"),
        ("forum_forums", "0002_initial"),  
        ("forum_topics", "0001_initial"),
        ("forum_posts", "0002_initial"),   
        ("contenttypes", "0002_remove_content_type_name"),
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.RunPython(
            create_rbac_groups,
            reverse_rbac_groups,
            atomic=True,
        ),
    ]
