

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("_user", "0016_alter_user_options_and_more"),
    ]

    operations = [
        migrations.RenameIndex(
            model_name="user",
            new_name="Duser_user_email_2817fd_idx",
            old_name="users_email_4b85f2_idx",
        ),
        migrations.RenameIndex(
            model_name="user",
            new_name="Duser_user_usernam_46726c_idx",
            old_name="users_usernam_baeb4b_idx",
        ),
        migrations.RenameIndex(
            model_name="user",
            new_name="Duser_user_role_96ecd5_idx",
            old_name="users_role_0ace22_idx",
        ),
        migrations.RenameIndex(
            model_name="user",
            new_name="Duser_user_created_d1f048_idx",
            old_name="users_created_1ac26f_idx",
        ),
        migrations.AlterModelTable(
            name="user",
            table="_user_user",
        ),
    ]
