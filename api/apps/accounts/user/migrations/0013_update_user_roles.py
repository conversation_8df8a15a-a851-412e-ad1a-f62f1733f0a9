from django.db import migrations


def update_user_roles(apps, schema_editor):
    """
    Update existing user roles to match the new role structure.
    Maps:
    - 'neo_hero' -> 'user'
    - 'neo_admin' -> 'admin'
    - 'business_dev_admin' -> 'admin'
    """
    User = apps.get_model("_user", "User")

    User.objects.filter(role="neo_hero").update(role="user")

    User.objects.filter(role="neo_admin").update(role="admin")
    User.objects.filter(role="business_dev_admin").update(role="admin")


class Migration(migrations.Migration):
    """
    Migration to update user roles to the new structure.
    """

    dependencies = [
        ("_user", "0012_alter_user_profession_id_alter_user_role"),
    ]

    operations = [
        migrations.RunPython(update_user_roles, migrations.RunPython.noop),
    ]
