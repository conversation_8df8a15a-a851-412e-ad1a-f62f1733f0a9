from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from core.abstract.serializers import AbstractSerializer
from apps.accounts.user.models import User
from apps.accounts.user.constants import ROLE_CHOICES


class UserExportSerializer(AbstractSerializer):
    country_name = serializers.SerializerMethodField()
    role_translated = serializers.SerializerMethodField()
    number_of_submitted_events = serializers.SerializerMethodField()
    number_of_submitted_product_ideas = serializers.SerializerMethodField()
    created_at = serializers.SerializerMethodField()
    is_staff = serializers.BooleanField(read_only=True)

    def get_country_name(self, obj):
        if obj.country:
            return obj.country.name
        return None

    def get_role_translated(self, obj):
        role_choices = dict(ROLE_CHOICES)
        if obj.role in role_choices:
            return _(role_choices[obj.role])
        return obj.role

    def get_number_of_submitted_events(self, obj):
        try:
            return obj.event_suggestions.count()
        except AttributeError:
            return 0

    def get_number_of_submitted_product_ideas(self, obj):
        try:
            return obj.product_ideas.count()
        except AttributeError:
            return 0

    def get_created_at(self, obj):
        if obj.created:
            return obj.created.isoformat()
        return None

    class Meta:
        model = User
        fields = [
            "id",
            "_id",
            "username",
            "email",
            "fullname",
            "phone_number",
            "country",
            "country_name",
            "role",
            "role_translated",
            "is_staff",
            "is_superuser",
            "created_at",
            "number_of_submitted_events",
            "number_of_submitted_product_ideas",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        try:
            data["avatar"] = (
                self.context["request"].build_absolute_uri(instance.avatar.url)
                if instance.avatar
                else None
            )
        except (AttributeError, ValueError, KeyError):
            data["avatar"] = None
        return data
