from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from apps.accounts.user.serializers.user import UserSerializer
from apps.accounts.user.models import User


class UpdateUserSerializer(UserSerializer):
    avatar = serializers.ImageField(required=False)

    class Meta:
        model = User
        fields = [
            "id",
            "avatar",
            "fullname",
            "phone_number",
            "country",
            "country_name",
            "role",
            "role_translated",
            "number_of_submitted_events",
            "number_of_submitted_product_ideas",
        ]
        read_only_fields = [
            "id",
            "email",
            "country_name",
            "role_translated",
            "number_of_submitted_events",
            "number_of_submitted_product_ideas",
        ]

    def validate(self, attrs):
        if "fullname" in attrs and not attrs["fullname"]:
            raise serializers.ValidationError(
                {"error": {"fullname": [_("Fullname cannot be empty.")]}}
            )
        return attrs
