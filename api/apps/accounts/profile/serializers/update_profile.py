from rest_framework import serializers
from rest_framework.serializers import ValidationError
from django.utils.translation import gettext_lazy as _

from apps.accounts.profile.serializers.profile import ProfileSerializer


class UpdateProfileSerializer(ProfileSerializer):
    avatar = serializers.ImageField(required=False)

    class Meta:
        model = ProfileSerializer.Meta.model
        fields = [
            "id",
            "avatar",
            "fullname",
            "phone_number",
            "country",
            "country_name",
            "role_translated",
            "number_of_submitted_events",
            "number_of_submitted_product_ideas",
        ]
        read_only_fields = [
            "country_name",
            "role_translated",
            "number_of_submitted_events",
            "number_of_submitted_product_ideas",
        ]

    def to_internal_value(self, data):
        """
        Validate that only allowed fields are present in the input data.
        This runs before standard validation.
        """
        allowed_fields = [
            "avatar",
            "fullname",
            "phone_number",
            "country",
        ]

        for field_name in data.keys():
            if field_name not in allowed_fields:
                raise ValidationError(
                    {"error": {field_name: [_("This field is not allowed.")]}}
                )

        return super().to_internal_value(data)

    def validate(self, attrs):
        if "fullname" in attrs:
            value = attrs["fullname"]
            if value == "":
                raise ValidationError(
                    {"error": {"fullname": [_("Fullname cannot be empty.")]}}
                )
        if "avatar" in attrs:
            value = attrs["avatar"]
            if value and value.size > 1024 * 1024:
                raise ValidationError(
                    {"error": {"avatar": [_("File size too large.")]}}
                )

        return attrs
