from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    """
    Initial migration to create CoachProfile and DoctorProfile models.
    These models replace the fields removed from the User model.
    """

    initial = True

    dependencies = [
        (
            "_user",
            "0013_remove_user_fields_and_update_roles",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="CoachProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "_id",
                    models.CharField(
                        blank=True,
                        editable=False,
                        max_length=255,
                        null=True,
                        unique=True,
                    ),
                ),
                (
                    "specialization",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Specialization",
                    ),
                ),
                (
                    "certification",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="Certification",
                    ),
                ),
                (
                    "experience_years",
                    models.PositiveIntegerField(
                        default=0, verbose_name="Years of Experience"
                    ),
                ),
                (
                    "company",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Company/Organization",
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        blank=True, null=True, verbose_name="Professional Bio"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="coach_profile",
                        to="_user.user",
                    ),
                ),
            ],
            options={
                "verbose_name": "Coach Profile",
                "verbose_name_plural": "Coach Profiles",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="DoctorProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "_id",
                    models.CharField(
                        blank=True,
                        editable=False,
                        max_length=255,
                        null=True,
                        unique=True,
                    ),
                ),
                (
                    "medical_license",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Medical License Number",
                    ),
                ),
                (
                    "specialty",
                    models.CharField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Medical Specialty",
                    ),
                ),
                (
                    "hospital",
                    models.CharField(
                        blank=True,
                        max_length=200,
                        null=True,
                        verbose_name="Hospital/Clinic",
                    ),
                ),
                (
                    "qualifications",
                    models.TextField(
                        blank=True, null=True, verbose_name="Qualifications"
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        blank=True, null=True, verbose_name="Professional Bio"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="doctor_profile",
                        to="_user.user",
                    ),
                ),
            ],
            options={
                "verbose_name": "Doctor Profile",
                "verbose_name_plural": "Doctor Profiles",
                "abstract": False,
            },
        ),
    ]
