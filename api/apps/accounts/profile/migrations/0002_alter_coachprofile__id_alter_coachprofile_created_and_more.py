from django.db import migrations, models
import django.utils.timezone
import uuid


def convert_id_to_uuid(apps, schema_editor):
    """Convert existing bigint IDs to UUIDs while preserving relationships."""
    CoachProfile = apps.get_model('profile', 'CoachProfile')
    DoctorProfile = apps.get_model('profile', 'DoctorProfile')
    
    
    for profile in CoachProfile.objects.all():
        profile.new_id = uuid.uuid4()
        profile.save(update_fields=['new_id'])
    
    
    for profile in DoctorProfile.objects.all():
        profile.new_id = uuid.uuid4()
        profile.save(update_fields=['new_id'])


def reverse_convert_uuid_to_id(apps, schema_editor):
    """Reverse migration - not recommended in production."""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("profile", "0001_initial"),
    ]

    operations = [
        
        migrations.AddField(
            model_name="coachprofile",
            name="new_id",
            field=models.UUIDField(
                db_index=True,
                default=uuid.uuid4,
                editable=False,
                null=True,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="doctorprofile",
            name="new_id",
            field=models.UUIDField(
                db_index=True,
                default=uuid.uuid4,
                editable=False,
                null=True,
                unique=True,
            ),
        ),
        
        
        migrations.RunPython(convert_id_to_uuid, reverse_convert_uuid_to_id),
        
        
        migrations.AlterField(
            model_name="coachprofile",
            name="id",
            field=models.BigIntegerField(),
        ),
        migrations.AlterField(
            model_name="doctorprofile",
            name="id",
            field=models.BigIntegerField(),
        ),
        
        
        migrations.RenameField(
            model_name="coachprofile",
            old_name="id",
            new_name="old_id",
        ),
        migrations.RenameField(
            model_name="coachprofile",
            old_name="new_id",
            new_name="id",
        ),
        migrations.RenameField(
            model_name="doctorprofile",
            old_name="id",
            new_name="old_id",
        ),
        migrations.RenameField(
            model_name="doctorprofile",
            old_name="new_id",
            new_name="id",
        ),
        
        
        migrations.AlterField(
            model_name="coachprofile",
            name="id",
            field=models.UUIDField(
                db_index=True,
                default=uuid.uuid4,
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="doctorprofile",
            name="id",
            field=models.UUIDField(
                db_index=True,
                default=uuid.uuid4,
                editable=False,
                primary_key=True,
                serialize=False,
                unique=True,
            ),
        ),
        
        
        migrations.RemoveField(
            model_name="coachprofile",
            name="old_id",
        ),
        migrations.RemoveField(
            model_name="doctorprofile",
            name="old_id",
        ),
        
        
        migrations.AlterField(
            model_name="coachprofile",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="coachprofile",
            name="created",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="doctorprofile",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="doctorprofile",
            name="created",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
    ]
