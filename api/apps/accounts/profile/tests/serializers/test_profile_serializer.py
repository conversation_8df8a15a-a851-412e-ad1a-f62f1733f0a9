from django.test import TestCase
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.core.files.uploadedfile import SimpleUploadedFile

from apps.accounts.profile.serializers import ProfileSerializer

User = get_user_model()


class ProfileSerializerTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="TestPass123",
            fullname="Test User",
            username="testuser",
            phone_number="**********",
        )

        self.user.avatar = SimpleUploadedFile(
            "test_avatar.jpg", b"file_content", content_type="image/jpeg"
        )
        self.user.save()

        self.factory = RequestFactory()
        self.request = self.factory.get("/")

    def test_profile_serializer_contains_expected_fields(self):
        """Test that ProfileSerializer contains expected fields"""
        serializer = ProfileSerializer(
            instance=self.user, context={"request": self.request}
        )

        expected_fields = [
            "_id",
            "id",
            "username",
            "avatar",
            "email",
            "is_superuser",
            "created",
            "updated",
            "fullname",
            "phone_number",
            "role",
            "role_translated",
            "country",
            "country_name",
            "number_of_submitted_events",
            "number_of_submitted_product_ideas",
        ]

        self.assertEqual(set(serializer.data.keys()), set(expected_fields))

    def test_profile_serializer_event_suggestions_count(self):
        """Test that ProfileSerializer returns the correct event suggestions count"""
        serializer = ProfileSerializer(
            instance=self.user, context={"request": self.request}
        )

        self.assertEqual(serializer.data["number_of_submitted_events"], 0)

    def test_profile_serializer_product_ideas_count(self):
        """Test that ProfileSerializer returns the correct product ideas count"""
        serializer = ProfileSerializer(
            instance=self.user, context={"request": self.request}
        )

        self.assertEqual(serializer.data["number_of_submitted_product_ideas"], 0)

    def test_profile_serializer_avatar_url(self):
        """Test that ProfileSerializer returns the avatar URL with absolute URI"""
        serializer = ProfileSerializer(
            instance=self.user, context={"request": self.request}
        )

        self.assertIsNotNone(serializer.data["avatar"])

    def test_profile_serializer_without_request_context(self):
        """Test that ProfileSerializer works without request context"""
        serializer = ProfileSerializer(instance=self.user)

        self.assertEqual(serializer.data["email"], self.user.email)
        self.assertEqual(serializer.data["fullname"], self.user.fullname)
