from django.test import TestCase
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.serializers import ValidationError

from apps.accounts.profile.serializers import UpdateProfileSerializer

User = get_user_model()


class UpdateProfileSerializerTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="TestPass123",
            fullname="Test User",
            username="testuser",
            phone_number="**********",
        )

        self.factory = RequestFactory()
        self.request = self.factory.get("/")

    def test_update_profile_serializer_contains_expected_fields(self):
        """Test that UpdateProfileSerializer contains expected fields"""
        serializer = UpdateProfileSerializer(
            instance=self.user, context={"request": self.request}
        )

        expected_fields = [
            "id",
            "avatar",
            "fullname",
            "phone_number",
            "country",
            "country_name",
            "role_translated",
            "number_of_submitted_events",
            "number_of_submitted_product_ideas",
            "created",
            "updated",
        ]

        self.assertEqual(set(serializer.data.keys()), set(expected_fields))

    def test_update_profile_serializer_update(self):
        """Test that UpdateProfileSerializer updates fields correctly"""
        data = {
            "fullname": "Updated User",
            "phone_number": "9876543210",
        }

        serializer = UpdateProfileSerializer(
            instance=self.user,
            data=data,
            context={"request": self.request},
            partial=True,
        )

        self.assertTrue(serializer.is_valid())
        updated_user = serializer.save()

        self.assertEqual(updated_user.fullname, "Updated User")
        self.assertEqual(updated_user.phone_number, "9876543210")

    def test_update_profile_serializer_validate_empty_fullname(self):
        """Test that UpdateProfileSerializer validates empty fullname"""
        data = {
            "fullname": "",
        }

        serializer = UpdateProfileSerializer(
            instance=self.user, data=data, context={"request": self.request}
        )

        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_update_profile_serializer_validate_large_avatar(self):
        """Test that UpdateProfileSerializer validates avatar size"""

        large_avatar = SimpleUploadedFile(
            "large_avatar.jpg", b"file_content" * 1000, content_type="image/jpeg"
        )

        large_avatar.size = 2 * 1024 * 1024

        data = {
            "fullname": "User With Large Avatar",
            "avatar": large_avatar,
        }

        serializer = UpdateProfileSerializer(
            instance=self.user, data=data, context={"request": self.request}
        )

        with self.assertRaises(ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_update_profile_serializer_validate_invalid_field(self):
        """Test that UpdateProfileSerializer validates field names"""
        data = {
            "fullname": "Updated User",
            "invalid_field": "should not be allowed",
        }

        serializer = UpdateProfileSerializer(
            instance=self.user, data=data, context={"request": self.request}
        )

        try:
            serializer.is_valid(raise_exception=True)

            self.fail("ValidationError not raised for invalid field")
        except ValidationError as e:

            error_detail = str(e.detail)
            self.assertTrue("invalid_field" in error_detail.lower())
