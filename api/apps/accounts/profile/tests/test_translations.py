from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from core.utilities import tprint

User = get_user_model()


class ProfileTranslationTest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
        )
        self.user.set_password("testpassword")
        self.user.save()
        self.client.login(username="testuser", password="testpassword")

    def test_get_me_unauthenticated(self):
        self.client.logout()
        url = reverse("get-me")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(
            response.json(),
            {"error": _("Authentication credentials were not provided.")},
        )

    def test_update_me_invalid_data(self):
        url = reverse("update-me")

        data = {"fullname": ""}
        self.client.force_authenticate(self.user)  # type: ignore
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {"error": {"error": {"fullname": [_("Fullname cannot be empty.")]}}},
        )

    def test_get_me_authenticated(self):
        self.client.force_authenticate(self.user)  # type: ignore
        url = reverse("get-me")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["role"], "user")
        self.assertEqual(response.json()["role_translated"], "User")

    def test_get_me_authenticated_arabic(self):
        self.client.defaults["HTTP_ACCEPT_LANGUAGE"] = "ar"
        self.client.force_authenticate(self.user)  # type: ignore
        url = reverse("get-me")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["role"], "user")
        self.assertEqual(response.json()["role_translated"], "المستخدم")
