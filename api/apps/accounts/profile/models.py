from django.db import models
from django.utils.translation import gettext_lazy as _
from core.abstract.models import AbstractAutoIncrementModel


class CoachProfile(AbstractAutoIncrementModel):
    """
    Model for storing coach-specific profile information.
    Separate from User to maintain clean separation of concerns.
    """

    user = models.OneToOneField(
        "_user.User",
        on_delete=models.CASCADE,
        related_name="coach_profile",
    )
    specialization = models.CharField(
        _("Specialization"), max_length=100, blank=True, null=True
    )
    certification = models.CharField(
        _("Certification"), max_length=255, blank=True, null=True
    )
    experience_years = models.PositiveIntegerField(_("Years of Experience"), default=0)
    company = models.CharField(
        _("Company/Organization"), max_length=200, blank=True, null=True
    )
    bio = models.TextField(_("Professional Bio"), blank=True, null=True)

    class Meta:
        verbose_name = _("Coach Profile")
        verbose_name_plural = _("Coach Profiles")

    def __str__(self):
        return f"{self.user.fullname} (Coach)"


class DoctorProfile(AbstractAutoIncrementModel):
    """
    Model for storing doctor-specific profile information.
    Separate from User to maintain clean separation of concerns.
    """

    user = models.OneToOneField(
        "_user.User",
        on_delete=models.CASCADE,
        related_name="doctor_profile",
    )
    medical_license = models.CharField(
        _("Medical License Number"), max_length=100, blank=True, null=True
    )
    specialty = models.CharField(
        _("Medical Specialty"), max_length=100, blank=True, null=True
    )
    hospital = models.CharField(
        _("Hospital/Clinic"), max_length=200, blank=True, null=True
    )
    qualifications = models.TextField(_("Qualifications"), blank=True, null=True)
    bio = models.TextField(_("Professional Bio"), blank=True, null=True)

    class Meta:
        verbose_name = _("Doctor Profile")
        verbose_name_plural = _("Doctor Profiles")

    def __str__(self):
        return f"{self.user.fullname} (Doctor)"
