from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class SessionType(models.TextChoices):
    """Available AI coaching session types"""
    
    NUTRITION_COACHING = "nutrition_coaching", _("Nutrition Coaching")
    FITNESS_PLANNING = "fitness_planning", _("Fitness Planning")
    MEAL_PLANNING = "meal_planning", _("Meal Planning")
    HEALTH_ASSESSMENT = "health_assessment", _("Health Assessment")
    GOAL_SETTING = "goal_setting", _("Goal Setting")
    PROGRESS_REVIEW = "progress_review", _("Progress Review")
    CARNIVORE_GUIDANCE = "carnivore_guidance", _("Carnivore Diet Guidance")
    GENERAL_COACHING = "general_coaching", _("General Coaching")


class SessionStatus(models.TextChoices):
    """Session status choices"""
    
    ACTIVE = "active", _("Active")
    COMPLETED = "completed", _("Completed")
    PAUSED = "paused", _("Paused")
    CANCELLED = "cancelled", _("Cancelled")


class AICoachSession(AbstractAutoIncrementModel):
    """
    Model to track AI coaching sessions for users.
    
    Each session represents a coaching interaction between the user and AI,
    with start/end times, session type, and summary of recommendations.
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="ai_coach_sessions",
        verbose_name=_("User"),
    )
    
    session_type = models.CharField(
        max_length=20,
        choices=SessionType.choices,
        default=SessionType.GENERAL_COACHING,
        verbose_name=_("Session Type"),
        help_text=_("Type of AI coaching session"),
    )
    
    status = models.CharField(
        max_length=10,
        choices=SessionStatus.choices,
        default=SessionStatus.ACTIVE,
        verbose_name=_("Status"),
    )
    
    start_time = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Start Time"),
        help_text=_("When the coaching session started"),
    )
    
    end_time = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_("End Time"),
        help_text=_("When the coaching session ended"),
    )
    
    summary = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Session Summary"),
        help_text=_("AI-generated summary of the coaching session"),
    )
    
    goals_discussed = models.JSONField(
        default=list,
        verbose_name=_("Goals Discussed"),
        help_text=_("List of goals discussed during the session"),
    )
    
    recommendations_given = models.JSONField(
        default=list,
        verbose_name=_("Recommendations Given"),
        help_text=_("List of recommendation IDs provided during session"),
    )
    
    user_satisfaction_score = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=[
            MinValueValidator(1),
            MaxValueValidator(5),
        ],
        verbose_name=_("User Satisfaction Score"),
        help_text=_("User rating from 1-5 for session quality"),
    )
    
    session_duration_minutes = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_("Session Duration (minutes)"),
        help_text=_("Total duration of the session in minutes"),
    )
    
    ai_model_version = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_("AI Model Version"),
        help_text=_("Version of AI model used for this session"),
    )
    
    session_metadata = models.JSONField(
        default=dict,
        verbose_name=_("Session Metadata"),
        help_text=_("Additional metadata about the session"),
    )
    
    class Meta:
        app_label = "ai_coaching"
        db_table = "ai_coach_session"
        verbose_name = _("AI Coach Session")
        verbose_name_plural = _("AI Coach Sessions")
        ordering = ["-start_time"]
        indexes = [
            models.Index(fields=["user", "-start_time"]),
            models.Index(fields=["session_type", "-start_time"]),
            models.Index(fields=["status"]),
            models.Index(fields=["user", "status"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.get_session_type_display()} ({self.start_time.strftime('%Y-%m-%d %H:%M')})"
    
    @property
    def is_active(self) -> bool:
        """Check if session is currently active"""
        return self.status == SessionStatus.ACTIVE
    
    @property
    def duration_hours(self) -> float:
        """Get session duration in hours"""
        if self.session_duration_minutes:
            return self.session_duration_minutes / 60.0
        return 0.0
    
    def end_session(self, summary: str = None):
        """End the session and calculate duration"""
        from django.utils import timezone
        
        if self.status == SessionStatus.ACTIVE:
            self.end_time = timezone.now()
            self.status = SessionStatus.COMPLETED
            
            # Calculate duration
            if self.start_time and self.end_time:
                duration = self.end_time - self.start_time
                self.session_duration_minutes = int(duration.total_seconds() / 60)
            
            if summary:
                self.summary = summary
                
            self.save(update_fields=['end_time', 'status', 'session_duration_minutes', 'summary'])
    
    def add_recommendation(self, recommendation_id: str):
        """Add a recommendation ID to the session"""
        if recommendation_id not in self.recommendations_given:
            self.recommendations_given.append(recommendation_id)
            self.save(update_fields=['recommendations_given']) 