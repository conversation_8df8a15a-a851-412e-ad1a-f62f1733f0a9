from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from decimal import Decimal

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class DataType(models.TextChoices):
    """Types of personalization data"""
    
    PREFERENCE = "preference", _("User Preference")
    BEHAVIOR = "behavior", _("User Behavior")
    GOAL = "goal", _("User Goal")
    RESTRICTION = "restriction", _("Dietary Restriction")
    ALLERGY = "allergy", _("Food Allergy")
    HEALTH_CONDITION = "health_condition", _("Health Condition")
    ACTIVITY_LEVEL = "activity_level", _("Activity Level")
    MEAL_TIMING = "meal_timing", _("Meal Timing Preference")
    SUPPLEMENT = "supplement", _("Supplement Usage")
    CARNIVORE_ADAPTATION = "carnivore_adaptation", _("Carnivore Diet Adaptation")
    LEARNING_PATTERN = "learning_pattern", _("Learning Pattern")
    FEEDBACK_PATTERN = "feedback_pattern", _("Feedback Pattern")


class DataSource(models.TextChoices):
    """Source of the personalization data"""
    
    USER_INPUT = "user_input", _("User Input")
    BEHAVIORAL_ANALYSIS = "behavioral_analysis", _("Behavioral Analysis")
    FEEDBACK_ANALYSIS = "feedback_analysis", _("Feedback Analysis")
    CONVERSATION_ANALYSIS = "conversation_analysis", _("Conversation Analysis")
    CALCULATION_RESULT = "calculation_result", _("Calculator Result")
    PROGRESS_TRACKING = "progress_tracking", _("Progress Tracking")
    EXTERNAL_INTEGRATION = "external_integration", _("External Integration")


class PersonalizationData(AbstractAutoIncrementModel):
    """
    Model to store personalization data for AI learning.
    
    This model tracks various user preferences, behaviors, and characteristics
    that the AI uses to personalize recommendations and coaching.
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="personalization_data",
        verbose_name=_("User"),
    )
    
    data_type = models.CharField(
        max_length=25,
        choices=DataType.choices,
        verbose_name=_("Data Type"),
    )
    
    key = models.CharField(
        max_length=100,
        verbose_name=_("Key"),
        help_text=_("Specific key for this data point (e.g., 'breakfast_time', 'protein_preference')"),
    )
    
    value = models.TextField(
        verbose_name=_("Value"),
        help_text=_("The actual value (can be JSON for complex data)"),
    )
    
    weight = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('1.00'),
        validators=[
            MinValueValidator(Decimal('0.00')),
            MaxValueValidator(Decimal('10.00')),
        ],
        verbose_name=_("Weight"),
        help_text=_("Importance weight for this data point (0.00-10.00)"),
    )
    
    confidence_score = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.80'),
        validators=[
            MinValueValidator(Decimal('0.00')),
            MaxValueValidator(Decimal('1.00')),
        ],
        verbose_name=_("Confidence Score"),
        help_text=_("AI confidence in this data point (0.00-1.00)"),
    )
    
    source = models.CharField(
        max_length=25,
        choices=DataSource.choices,
        default=DataSource.USER_INPUT,
        verbose_name=_("Data Source"),
    )
    
    context = models.JSONField(
        default=dict,
        verbose_name=_("Context"),
        help_text=_("Additional context about when/how this data was collected"),
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active"),
        help_text=_("Whether this data point is currently active"),
    )
    
    is_verified = models.BooleanField(
        default=False,
        verbose_name=_("Is Verified"),
        help_text=_("Whether this data has been verified by user"),
    )
    
    expires_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_("Expires At"),
        help_text=_("When this data point expires (if applicable)"),
    )
    
    last_validated_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_("Last Validated At"),
        help_text=_("When this data was last validated"),
    )
    
    validation_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Validation Count"),
        help_text=_("Number of times this data has been validated"),
    )
    
    usage_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_("Usage Count"),
        help_text=_("Number of times this data has been used in recommendations"),
    )
    
    correlation_data = models.JSONField(
        default=dict,
        verbose_name=_("Correlation Data"),
        help_text=_("Data about correlations with other user data points"),
    )
    
    learning_metadata = models.JSONField(
        default=dict,
        verbose_name=_("Learning Metadata"),
        help_text=_("Metadata about how AI learned this data point"),
    )
    
    class Meta:
        app_label = "ai_coaching"
        db_table = "ai_personalization_data"
        verbose_name = _("Personalization Data")
        verbose_name_plural = _("Personalization Data")
        ordering = ["-updated"]
        unique_together = [['user', 'data_type', 'key']]
        indexes = [
            models.Index(fields=["user", "data_type"]),
            models.Index(fields=["user", "is_active"]),
            models.Index(fields=["data_type", "key"]),
            models.Index(fields=["weight", "confidence_score"]),
            models.Index(fields=["source", "-created"]),
            models.Index(fields=["expires_at"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.data_type}: {self.key}"
    
    @property
    def is_expired(self) -> bool:
        """Check if data has expired"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
    
    @property
    def is_high_confidence(self) -> bool:
        """Check if data has high confidence"""
        return self.confidence_score >= Decimal('0.8')
    
    @property
    def is_high_weight(self) -> bool:
        """Check if data has high importance weight"""
        return self.weight >= Decimal('5.0')
    
    @property
    def effective_score(self) -> Decimal:
        """Calculate effective score (weight * confidence)"""
        return self.weight * self.confidence_score
    
    def increment_usage(self):
        """Increment usage count"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])
    
    def validate_data(self):
        """Mark data as validated"""
        from django.utils import timezone
        
        self.is_verified = True
        self.last_validated_at = timezone.now()
        self.validation_count += 1
        self.save(update_fields=['is_verified', 'last_validated_at', 'validation_count'])
    
    def update_confidence(self, new_confidence: Decimal):
        """Update confidence score"""
        if Decimal('0.00') <= new_confidence <= Decimal('1.00'):
            self.confidence_score = new_confidence
            self.save(update_fields=['confidence_score'])
    
    def deactivate(self):
        """Deactivate this data point"""
        self.is_active = False
        self.save(update_fields=['is_active'])
    
    def get_parsed_value(self):
        """Parse value as JSON if possible, otherwise return as string"""
        import json
        try:
            return json.loads(self.value)
        except (json.JSONDecodeError, TypeError):
            return self.value 