from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class MessageSender(models.TextChoices):
    """Who sent the message"""
    
    USER = "user", _("User")
    AI = "ai", _("AI Coach")
    SYSTEM = "system", _("System")


class MessageType(models.TextChoices):
    """Type of message content"""
    
    TEXT = "text", _("Text")
    RECOMMENDATION = "recommendation", _("Recommendation")
    MEAL_PLAN = "meal_plan", _("Meal Plan")
    RECIPE = "recipe", _("Recipe")
    EXERCISE = "exercise", _("Exercise")
    HEALTH_TIP = "health_tip", _("Health Tip")
    QUESTION = "question", _("Question")
    FOLLOW_UP = "follow_up", _("Follow-up")
    CLARIFICATION = "clarification", _("Clarification")
    FEEDBACK_REQUEST = "feedback_request", _("Feedback Request")


class ChatMessage(AbstractAutoIncrementModel):
    """
    Model to store individual chat messages in AI coaching conversations.
    
    Each message is part of a coaching session and tracks the conversation
    flow between user and AI coach.
    """
    
    session = models.ForeignKey(
        'AICoachSession',
        on_delete=models.CASCADE,
        related_name="chat_messages",
        verbose_name=_("Session"),
    )
    
    sender = models.CharField(
        max_length=10,
        choices=MessageSender.choices,
        verbose_name=_("Sender"),
    )
    
    message = models.TextField(
        verbose_name=_("Message"),
        help_text=_("The actual message content"),
    )
    
    message_type = models.CharField(
        max_length=20,
        choices=MessageType.choices,
        default=MessageType.TEXT,
        verbose_name=_("Message Type"),
    )
    
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Timestamp"),
    )
    
    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="replies",
        verbose_name=_("Parent Message"),
        help_text=_("Message this is replying to"),
    )
    
    metadata = models.JSONField(
        default=dict,
        verbose_name=_("Metadata"),
        help_text=_("Additional message metadata (attachments, formatting, etc.)"),
    )
    
    ai_processing_time_ms = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_("AI Processing Time (ms)"),
        help_text=_("Time taken for AI to process and respond"),
    )
    
    confidence_score = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_("Confidence Score"),
        help_text=_("AI confidence in the response (0.00-1.00)"),
    )
    
    is_sensitive = models.BooleanField(
        default=False,
        verbose_name=_("Is Sensitive"),
        help_text=_("Whether message contains sensitive health information"),
    )
    
    intent_detected = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Intent Detected"),
        help_text=_("AI-detected user intent for this message"),
    )
    
    entities_extracted = models.JSONField(
        default=list,
        verbose_name=_("Entities Extracted"),
        help_text=_("Named entities extracted from the message"),
    )
    
    sentiment_score = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_("Sentiment Score"),
        help_text=_("Sentiment analysis score (-1.00 to 1.00)"),
    )
    
    is_flagged = models.BooleanField(
        default=False,
        verbose_name=_("Is Flagged"),
        help_text=_("Whether message is flagged for review"),
    )
    
    flag_reason = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Flag Reason"),
        help_text=_("Reason for flagging this message"),
    )
    
    ai_model_version = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_("AI Model Version"),
        help_text=_("Version of AI model that processed this message"),
    )
    
    class Meta:
        app_label = "ai_coaching"
        db_table = "ai_chat_message"
        verbose_name = _("Chat Message")
        verbose_name_plural = _("Chat Messages")
        ordering = ["timestamp"]
        indexes = [
            models.Index(fields=["session", "timestamp"]),
            models.Index(fields=["sender", "timestamp"]),
            models.Index(fields=["message_type", "timestamp"]),
            models.Index(fields=["is_flagged", "timestamp"]),
            models.Index(fields=["intent_detected"]),
        ]
    
    def __str__(self):
        max_length = 50
        message_preview = (
            self.message[:max_length] + "..." 
            if len(self.message) > max_length 
            else self.message
        )
        return f"{self.get_sender_display()}: {message_preview}"
    
    @property
    def user(self):
        """Get the user from the session"""
        return self.session.user
    
    @property
    def is_from_user(self) -> bool:
        """Check if message is from user"""
        return self.sender == MessageSender.USER
    
    @property
    def is_from_ai(self) -> bool:
        """Check if message is from AI"""
        return self.sender == MessageSender.AI
    
    @property
    def has_replies(self) -> bool:
        """Check if message has replies"""
        return self.replies.exists()
    
    def flag_message(self, reason: str):
        """Flag message for review"""
        self.is_flagged = True
        self.flag_reason = reason
        self.save(update_fields=['is_flagged', 'flag_reason'])
    
    def unflag_message(self):
        """Remove flag from message"""
        self.is_flagged = False
        self.flag_reason = None
        self.save(update_fields=['is_flagged', 'flag_reason']) 