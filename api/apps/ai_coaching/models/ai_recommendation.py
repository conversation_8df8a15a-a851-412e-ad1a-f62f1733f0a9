from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from decimal import Decimal

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class RecommendationType(models.TextChoices):
    """Types of AI recommendations"""
    
    MEAL_PLAN = "meal_plan", _("Meal Plan")
    RECIPE = "recipe", _("Recipe")
    HEALTH_TIP = "health_tip", _("Health Tip")
    EXERCISE = "exercise", _("Exercise")
    CARNIVORE_ADVICE = "carnivore_advice", _("Carnivore Diet Advice")
    SUPPLEMENT = "supplement", _("Supplement")
    LIFESTYLE = "lifestyle", _("Lifestyle Change")
    NUTRITION_TIMING = "nutrition_timing", _("Nutrition Timing")


class RecommendationPriority(models.TextChoices):
    """Priority levels for recommendations"""
    
    LOW = "low", _("Low")
    MEDIUM = "medium", _("Medium")
    HIGH = "high", _("High")
    URGENT = "urgent", _("Urgent")


class RecommendationStatus(models.TextChoices):
    """Status of recommendations"""
    
    PENDING = "pending", _("Pending")
    VIEWED = "viewed", _("Viewed")
    ACCEPTED = "accepted", _("Accepted")
    DECLINED = "declined", _("Declined")
    COMPLETED = "completed", _("Completed")


class AIRecommendation(AbstractAutoIncrementModel):
    """
    Model to store AI-generated recommendations for users.
    
    This includes various types of recommendations such as meal plans,
    recipes, health tips, and exercises with confidence scoring.
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="ai_recommendations",
        verbose_name=_("User"),
    )
    
    session = models.ForeignKey(
        'AICoachSession',
        on_delete=models.CASCADE,
        related_name="recommendations",
        blank=True,
        null=True,
        verbose_name=_("Session"),
        help_text=_("Associated coaching session"),
    )
    
    recommendation_type = models.CharField(
        max_length=20,
        choices=RecommendationType.choices,
        verbose_name=_("Recommendation Type"),
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name=_("Title"),
        help_text=_("Short title for the recommendation"),
    )
    
    content = models.TextField(
        verbose_name=_("Content"),
        help_text=_("Detailed recommendation content"),
    )
    
    confidence_score = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal('0.00')),
            MaxValueValidator(Decimal('1.00')),
        ],
        verbose_name=_("Confidence Score"),
        help_text=_("AI confidence in this recommendation (0.00-1.00)"),
    )
    
    priority = models.CharField(
        max_length=10,
        choices=RecommendationPriority.choices,
        default=RecommendationPriority.MEDIUM,
        verbose_name=_("Priority"),
    )
    
    status = models.CharField(
        max_length=10,
        choices=RecommendationStatus.choices,
        default=RecommendationStatus.PENDING,
        verbose_name=_("Status"),
    )
    
    tags = models.JSONField(
        default=list,
        verbose_name=_("Tags"),
        help_text=_("Tags for categorizing recommendations"),
    )
    
    metadata = models.JSONField(
        default=dict,
        verbose_name=_("Metadata"),
        help_text=_("Additional data specific to recommendation type"),
    )
    
    expected_outcomes = models.JSONField(
        default=list,
        verbose_name=_("Expected Outcomes"),
        help_text=_("List of expected outcomes from following this recommendation"),
    )
    
    personalization_factors = models.JSONField(
        default=dict,
        verbose_name=_("Personalization Factors"),
        help_text=_("Factors used to personalize this recommendation"),
    )
    
    expires_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_("Expires At"),
        help_text=_("When this recommendation expires"),
    )
    
    viewed_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_("Viewed At"),
        help_text=_("When user first viewed this recommendation"),
    )
    
    accepted_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_("Accepted At"),
        help_text=_("When user accepted this recommendation"),
    )
    
    completed_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_("Completed At"),
        help_text=_("When user completed this recommendation"),
    )
    
    user_feedback = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("User Feedback"),
        help_text=_("User's feedback on this recommendation"),
    )
    
    user_rating = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=[
            MinValueValidator(1),
            MaxValueValidator(5),
        ],
        verbose_name=_("User Rating"),
        help_text=_("User rating from 1-5 for recommendation quality"),
    )
    
    ai_model_version = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_("AI Model Version"),
        help_text=_("Version of AI model that generated this recommendation"),
    )
    
    class Meta:
        app_label = "ai_coaching"
        db_table = "ai_recommendation"
        verbose_name = _("AI Recommendation")
        verbose_name_plural = _("AI Recommendations")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["recommendation_type", "-created"]),
            models.Index(fields=["status", "priority"]),
            models.Index(fields=["user", "status"]),
            models.Index(fields=["confidence_score"]),
            models.Index(fields=["expires_at"]),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.user.email} ({self.get_recommendation_type_display()})"
    
    @property
    def is_expired(self) -> bool:
        """Check if recommendation has expired"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False
    
    @property
    def is_high_confidence(self) -> bool:
        """Check if recommendation has high confidence"""
        return self.confidence_score >= Decimal('0.8')
    
    def mark_viewed(self):
        """Mark recommendation as viewed"""
        if self.status == RecommendationStatus.PENDING:
            from django.utils import timezone
            self.status = RecommendationStatus.VIEWED
            self.viewed_at = timezone.now()
            self.save(update_fields=['status', 'viewed_at'])
    
    def mark_accepted(self):
        """Mark recommendation as accepted"""
        if self.status in [RecommendationStatus.PENDING, RecommendationStatus.VIEWED]:
            from django.utils import timezone
            self.status = RecommendationStatus.ACCEPTED
            self.accepted_at = timezone.now()
            self.save(update_fields=['status', 'accepted_at'])
    
    def mark_completed(self):
        """Mark recommendation as completed"""
        if self.status == RecommendationStatus.ACCEPTED:
            from django.utils import timezone
            self.status = RecommendationStatus.COMPLETED
            self.completed_at = timezone.now()
            self.save(update_fields=['status', 'completed_at'])
    
    def mark_declined(self, feedback: str = None):
        """Mark recommendation as declined"""
        if self.status in [RecommendationStatus.PENDING, RecommendationStatus.VIEWED]:
            self.status = RecommendationStatus.DECLINED
            if feedback:
                self.user_feedback = feedback
            self.save(update_fields=['status', 'user_feedback']) 