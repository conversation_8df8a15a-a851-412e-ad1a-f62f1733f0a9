"""
AI Coaching App URLs

URL patterns for the AI coaching and recommendations system.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    AICoachSessionViewSet,
    AIRecommendationViewSet,
    AIChatViewSet,
    PersonalizedMealPlanView,
    SingleMealSuggestionView,
    MealPlanOptimizationView,
    MealPlanFeedbackView,
    MealPlanAnalyticsView,
    HealthInsightsView,
)

app_name = 'ai_coaching'

# Create router for ViewSets
router = DefaultRouter()
router.register(r'sessions', AICoachSessionViewSet, basename='session')
router.register(r'recommendations', AIRecommendationViewSet, basename='recommendation')
router.register(r'chat', AIChatViewSet, basename='chat')

# URL patterns
urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Meal planning endpoints
    path('meal-plans/', PersonalizedMealPlanView.as_view(), name='ai-meal-plans'),
    path('meal-plans/suggestions/', SingleMealSuggestionView.as_view(), name='meal-suggestions'),
    path('meal-plans/optimize/', MealPlanOptimizationView.as_view(), name='meal-plan-optimization'),
    path('meal-plans/feedback/', MealPlanFeedbackView.as_view(), name='meal-plan-feedback'),
    path('meal-plans/analytics/', MealPlanAnalyticsView.as_view(), name='meal-plan-analytics'),
    
    # Health insights endpoint
    path('insights/', HealthInsightsView.as_view(), name='insights'),
    
    # Additional convenience endpoints
    path('quick-chat/', AIChatViewSet.as_view({'post': 'create'}), name='quick-chat'),
    path('generate-recommendations/', AIRecommendationViewSet.as_view({'post': 'create'}), name='generate-recommendations'),
]

# Additional URL patterns for specific actions
session_patterns = [
    path('sessions/<uuid:pk>/end/', AICoachSessionViewSet.as_view({'post': 'end_session'}), name='end-session'),
    path('sessions/<uuid:pk>/rate/', AICoachSessionViewSet.as_view({'post': 'rate_session'}), name='rate-session'),
    path('sessions/<uuid:pk>/messages/', AICoachSessionViewSet.as_view({'get': 'messages'}), name='session-messages'),
    path('sessions/<uuid:pk>/recommendations/', AICoachSessionViewSet.as_view({'get': 'recommendations'}), name='session-recommendations'),
    path('sessions/active/', AICoachSessionViewSet.as_view({'get': 'active_session'}), name='active-session'),
    path('sessions/stats/', AICoachSessionViewSet.as_view({'get': 'stats'}), name='session-stats'),
]

recommendation_patterns = [
    path('recommendations/<uuid:pk>/accept/', AIRecommendationViewSet.as_view({'post': 'accept'}), name='accept-recommendation'),
    path('recommendations/<uuid:pk>/decline/', AIRecommendationViewSet.as_view({'post': 'decline'}), name='decline-recommendation'),
    path('recommendations/<uuid:pk>/complete/', AIRecommendationViewSet.as_view({'post': 'complete'}), name='complete-recommendation'),
    path('recommendations/<uuid:pk>/view/', AIRecommendationViewSet.as_view({'post': 'mark_viewed'}), name='view-recommendation'),
    path('recommendations/bulk/', AIRecommendationViewSet.as_view({'post': 'bulk_action'}), name='bulk-recommendations'),
    path('recommendations/by-type/', AIRecommendationViewSet.as_view({'get': 'by_type'}), name='recommendations-by-type'),
    path('recommendations/high-priority/', AIRecommendationViewSet.as_view({'get': 'high_priority'}), name='high-priority-recommendations'),
    path('recommendations/pending/', AIRecommendationViewSet.as_view({'get': 'pending'}), name='pending-recommendations'),
    path('recommendations/analytics/', AIRecommendationViewSet.as_view({'get': 'analytics'}), name='recommendation-analytics'),
]

chat_patterns = [
    path('chat/<uuid:pk>/reply/', AIChatViewSet.as_view({'post': 'reply'}), name='chat-reply'),
    path('chat/<uuid:pk>/flag/', AIChatViewSet.as_view({'post': 'flag'}), name='flag-message'),
    path('chat/conversation/', AIChatViewSet.as_view({'get': 'conversation'}), name='conversation'),
    path('chat/search/', AIChatViewSet.as_view({'post': 'search'}), name='search-messages'),
    path('chat/recent/', AIChatViewSet.as_view({'get': 'recent'}), name='recent-messages'),
    path('chat/analytics/', AIChatViewSet.as_view({'get': 'analytics'}), name='chat-analytics'),
]

# Add all patterns to urlpatterns
urlpatterns.extend(session_patterns)
urlpatterns.extend(recommendation_patterns)
urlpatterns.extend(chat_patterns)
