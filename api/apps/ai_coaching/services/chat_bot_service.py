"""
AI Chat Bot Service

This service handles AI conversations, including message processing,
response generation, and conversation context management.
"""
import logging
import json
from typing import Dict, List, Optional, Any
from decimal import Decimal
from django.utils import timezone

from ..models import ChatMessage, MessageSender, MessageType, AICoachSession

logger = logging.getLogger(__name__)


class ChatBotService:
    """
    Service for handling AI chatbot conversations and responses.
    """
    
    def __init__(self):
        self.conversation_context = {}
    
    def create_message(
        self,
        session: AICoachSession,
        sender: str,
        message: str,
        message_type: str = MessageType.TEXT,
        parent_message: Optional[ChatMessage] = None,
        confidence_score: Optional[Decimal] = None
    ) -> ChatMessage:
        """
        Create a new chat message in the session.
        
        Args:
            session: The coaching session
            sender: Who sent the message (user/ai/system)
            message: The message content
            message_type: Type of message
            parent_message: Optional parent message for replies
            confidence_score: AI confidence in the message
            
        Returns:
            ChatMessage: The created message
        """
        try:
            # Analyze message for intent and entities if from user
            intent = None
            entities = []
            sentiment = None
            
            if sender == MessageSender.USER:
                analysis = self._analyze_user_message(message)
                intent = analysis.get('intent')
                entities = analysis.get('entities', [])
                sentiment = analysis.get('sentiment')
            
            chat_message = ChatMessage.objects.create(
                session=session,
                sender=sender,
                message=message,
                message_type=message_type,
                parent_message=parent_message,
                confidence_score=confidence_score,
                intent_detected=intent,
                entities_extracted=entities,
                sentiment_score=sentiment,
                ai_model_version="gpt-4-turbo" if sender == MessageSender.AI else None
            )
            
            # Check for content that should be flagged
            if self._should_flag_message(message):
                chat_message.flag_message("Potentially sensitive health content")
            
            logger.info(f"Created message in session {session.id} from {sender}")
            return chat_message
            
        except Exception as e:
            logger.error(f"Error creating message in session {session.id}: {str(e)}")
            raise
    
    def generate_response(
        self,
        session: AICoachSession,
        user_message: str,
        user_profile: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate AI response to user message.
        
        Args:
            session: The coaching session
            user_message: User's message to respond to
            user_profile: User profile for personalization
            context: Additional context for response generation
            
        Returns:
            Dict containing AI response and metadata
        """
        try:
            # Analyze user message
            message_analysis = self._analyze_user_message(user_message)
            
            # Get conversation context
            conversation_context = self._get_conversation_context(session)
            
            # Determine response strategy
            response_strategy = self._determine_response_strategy(
                message_analysis, user_profile, conversation_context
            )
            
            # Generate response based on strategy
            response = self._generate_contextual_response(
                user_message=user_message,
                strategy=response_strategy,
                user_profile=user_profile or {},
                conversation_context=conversation_context,
                session_type=session.session_type
            )
            
            logger.info(f"Generated AI response for session {session.id}")
            return response
            
        except Exception as e:
            logger.error(f"Error generating AI response for session {session.id}: {str(e)}")
            return self._get_fallback_response()
    
    def get_conversation_summary(self, session: AICoachSession) -> Dict[str, Any]:
        """
        Generate a summary of the conversation.
        
        Args:
            session: The coaching session
            
        Returns:
            Dict containing conversation summary
        """
        try:
            messages = session.chat_messages.all().order_by('timestamp')
            
            summary = {
                'total_messages': messages.count(),
                'user_messages': messages.filter(sender=MessageSender.USER).count(),
                'ai_messages': messages.filter(sender=MessageSender.AI).count(),
                'conversation_duration_minutes': self._calculate_conversation_duration(messages),
                'key_topics': self._extract_key_topics(messages),
                'user_intents': self._extract_user_intents(messages),
                'sentiment_analysis': self._analyze_conversation_sentiment(messages),
                'recommendations_mentioned': self._count_recommendations_mentioned(messages)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating conversation summary for session {session.id}: {str(e)}")
            return {}
    
    def _analyze_user_message(self, message: str) -> Dict[str, Any]:
        """
        Analyze user message for intent, entities, and sentiment.
        
        In a production environment, this would use actual NLP models.
        For now, using rule-based analysis.
        """
        
        message_lower = message.lower()
        
        # Intent detection (simplified)
        intent = "general"
        if any(word in message_lower for word in ['recipe', 'cook', 'prepare', 'make']):
            intent = "recipe_request"
        elif any(word in message_lower for word in ['meal', 'plan', 'planning', 'eat']):
            intent = "meal_planning"
        elif any(word in message_lower for word in ['exercise', 'workout', 'fitness', 'training']):
            intent = "exercise_inquiry"
        elif any(word in message_lower for word in ['help', 'advice', 'guidance', 'tips']):
            intent = "advice_seeking"
        elif any(word in message_lower for word in ['carnivore', 'meat', 'beef', 'steak']):
            intent = "carnivore_inquiry"
        
        # Entity extraction (simplified)
        entities = []
        foods = ['beef', 'steak', 'chicken', 'fish', 'eggs', 'bacon', 'lamb']
        for food in foods:
            if food in message_lower:
                entities.append({'type': 'food', 'value': food})
        
        # Sentiment analysis (simplified)
        positive_words = ['good', 'great', 'excellent', 'happy', 'satisfied', 'love']
        negative_words = ['bad', 'terrible', 'hate', 'disappointed', 'frustrated', 'difficult']
        
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        
        if positive_count > negative_count:
            sentiment = Decimal('0.7')  # Positive
        elif negative_count > positive_count:
            sentiment = Decimal('-0.3')  # Negative
        else:
            sentiment = Decimal('0.0')  # Neutral
        
        return {
            'intent': intent,
            'entities': entities,
            'sentiment': sentiment,
            'confidence': Decimal('0.75')
        }
    
    def _get_conversation_context(self, session: AICoachSession) -> Dict[str, Any]:
        """Get conversation context from previous messages"""
        
        recent_messages = session.chat_messages.all().order_by('-timestamp')[:10]
        
        context = {
            'recent_intents': [],
            'mentioned_foods': [],
            'user_preferences': [],
            'conversation_flow': []
        }
        
        for msg in recent_messages:
            if msg.intent_detected:
                context['recent_intents'].append(msg.intent_detected)
            
            if msg.entities_extracted:
                for entity in msg.entities_extracted:
                    if entity.get('type') == 'food':
                        context['mentioned_foods'].append(entity.get('value'))
            
            context['conversation_flow'].append({
                'sender': msg.sender,
                'type': msg.message_type,
                'timestamp': msg.timestamp.isoformat()
            })
        
        return context
    
    def _determine_response_strategy(
        self,
        message_analysis: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]],
        conversation_context: Dict[str, Any]
    ) -> str:
        """Determine the best response strategy based on analysis"""
        
        intent = message_analysis.get('intent', 'general')
        
        strategy_mapping = {
            'recipe_request': 'provide_recipe',
            'meal_planning': 'help_meal_planning',
            'exercise_inquiry': 'provide_exercise_advice',
            'advice_seeking': 'provide_guidance',
            'carnivore_inquiry': 'carnivore_specific_advice',
            'general': 'conversational_response'
        }
        
        return strategy_mapping.get(intent, 'conversational_response')
    
    def _generate_contextual_response(
        self,
        user_message: str,
        strategy: str,
        user_profile: Dict[str, Any],
        conversation_context: Dict[str, Any],
        session_type: str
    ) -> Dict[str, Any]:
        """Generate response based on strategy and context"""
        
        responses = {
            'provide_recipe': {
                'message': "I'd be happy to suggest a carnivore-friendly recipe! Based on your preferences, I recommend a simple ribeye steak. Would you like detailed cooking instructions?",
                'type': MessageType.RECOMMENDATION,
                'confidence': Decimal('0.85'),
                'should_recommend': True,
                'context': {'type': 'recipe'}
            },
            'help_meal_planning': {
                'message': "Let me help you plan some nutritious carnivore meals. What's your current eating schedule like? Do you prefer 1, 2, or 3 meals per day?",
                'type': MessageType.QUESTION,
                'confidence': Decimal('0.90'),
                'should_recommend': True,
                'context': {'type': 'meal_planning'}
            },
            'provide_exercise_advice': {
                'message': "Exercise pairs wonderfully with a carnivore diet! The high protein intake supports muscle building. Are you looking for strength training, cardio, or general fitness advice?",
                'type': MessageType.QUESTION,
                'confidence': Decimal('0.80'),
                'should_recommend': True,
                'context': {'type': 'exercise'}
            },
            'provide_guidance': {
                'message': "I'm here to help with your health and nutrition journey! What specific area would you like guidance on? I can help with carnivore diet tips, meal planning, or general health advice.",
                'type': MessageType.QUESTION,
                'confidence': Decimal('0.85'),
                'should_recommend': False,
                'context': {}
            },
            'carnivore_specific_advice': {
                'message': "Great question about carnivore nutrition! The carnivore diet can be incredibly effective for many people. What specific aspect interests you - getting started, troubleshooting, or optimizing your current approach?",
                'type': MessageType.QUESTION,
                'confidence': Decimal('0.95'),
                'should_recommend': True,
                'context': {'type': 'carnivore'}
            },
            'conversational_response': {
                'message': "Thank you for sharing that with me. I'm here to support your health journey. How can I best help you today?",
                'type': MessageType.TEXT,
                'confidence': Decimal('0.70'),
                'should_recommend': False,
                'context': {}
            }
        }
        
        response = responses.get(strategy, responses['conversational_response'])
        
        # Personalize response if user profile available
        if user_profile and user_profile.get('first_name'):
            name = user_profile['first_name']
            response['message'] = response['message'].replace('!', f', {name}!')
        
        return response
    
    def _get_fallback_response(self) -> Dict[str, Any]:
        """Get fallback response when generation fails"""
        
        return {
            'message': "I apologize, but I'm having trouble processing your request right now. Could you please rephrase your question or try again?",
            'type': MessageType.TEXT,
            'confidence': Decimal('0.50'),
            'should_recommend': False,
            'context': {}
        }
    
    def _should_flag_message(self, message: str) -> bool:
        """Check if message should be flagged for review"""
        
        # Simple keyword-based flagging
        sensitive_keywords = [
            'suicide', 'self-harm', 'eating disorder', 'anorexia', 'bulimia',
            'severe pain', 'chest pain', 'emergency', 'overdose'
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in sensitive_keywords)
    
    def _calculate_conversation_duration(self, messages) -> int:
        """Calculate conversation duration in minutes"""
        
        if messages.count() < 2:
            return 0
        
        first_message = messages.first()
        last_message = messages.last()
        
        duration = last_message.timestamp - first_message.timestamp
        return int(duration.total_seconds() / 60)
    
    def _extract_key_topics(self, messages) -> List[str]:
        """Extract key topics from conversation"""
        
        topics = []
        for message in messages:
            if message.intent_detected:
                topics.append(message.intent_detected)
        
        # Count occurrences and return top topics
        topic_counts = {}
        for topic in topics:
            topic_counts[topic] = topic_counts.get(topic, 0) + 1
        
        return sorted(topic_counts.keys(), key=lambda x: topic_counts[x], reverse=True)[:5]
    
    def _extract_user_intents(self, messages) -> List[str]:
        """Extract user intents from conversation"""
        
        user_messages = messages.filter(sender=MessageSender.USER)
        intents = []
        
        for message in user_messages:
            if message.intent_detected:
                intents.append(message.intent_detected)
        
        return list(set(intents))  # Remove duplicates
    
    def _analyze_conversation_sentiment(self, messages) -> Dict[str, Any]:
        """Analyze overall conversation sentiment"""
        
        user_messages = messages.filter(
            sender=MessageSender.USER,
            sentiment_score__isnull=False
        )
        
        if not user_messages.exists():
            return {'overall': 'neutral', 'average_score': 0.0}
        
        scores = [float(msg.sentiment_score) for msg in user_messages]
        average_score = sum(scores) / len(scores)
        
        if average_score > 0.3:
            overall = 'positive'
        elif average_score < -0.3:
            overall = 'negative'
        else:
            overall = 'neutral'
        
        return {
            'overall': overall,
            'average_score': average_score,
            'message_count': len(scores)
        }
    
    def _count_recommendations_mentioned(self, messages) -> int:
        """Count how many recommendations were mentioned"""
        
        ai_messages = messages.filter(sender=MessageSender.AI)
        recommendation_count = 0
        
        for message in ai_messages:
            if message.message_type in [MessageType.RECOMMENDATION, MessageType.MEAL_PLAN, MessageType.RECIPE]:
                recommendation_count += 1
        
        return recommendation_count 