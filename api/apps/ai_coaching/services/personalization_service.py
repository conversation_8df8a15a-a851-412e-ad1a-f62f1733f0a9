"""
AI Personalization Service

This service manages user personalization data and learns from user
interactions to improve AI recommendations and coaching.
"""
import logging
import json
from typing import Dict, List, Optional, Any
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.utils import timezone

from ..models import PersonalizationData, DataType, DataSource, AICoachSession

User = get_user_model()
logger = logging.getLogger(__name__)


class PersonalizationService:
    """
    Service for managing user personalization and AI learning.
    """
    
    def get_user_profile(self, user: User) -> Dict[str, Any]:
        """
        Get comprehensive user profile for personalization.
        
        Args:
            user: The user to get profile for
            
        Returns:
            Dict containing user profile data
        """
        try:
            profile = {
                'user_id': str(user.id),
                'preferences': {},
                'goals': [],
                'restrictions': [],
                'behaviors': {},
                'health_conditions': [],
                'activity_level': 'moderate',
                'learning_patterns': {},
                'feedback_patterns': {}
            }
            
            # Load personalization data
            personalization_data = PersonalizationData.objects.filter(
                user=user,
                is_active=True
            ).select_related()
            
            for data in personalization_data:
                if data.data_type == DataType.PREFERENCE:
                    profile['preferences'][data.key] = data.get_parsed_value()
                elif data.data_type == DataType.GOAL:
                    profile['goals'].append(data.get_parsed_value())
                elif data.data_type == DataType.RESTRICTION:
                    profile['restrictions'].append(data.get_parsed_value())
                elif data.data_type == DataType.BEHAVIOR:
                    profile['behaviors'][data.key] = data.get_parsed_value()
                elif data.data_type == DataType.HEALTH_CONDITION:
                    profile['health_conditions'].append(data.get_parsed_value())
                elif data.data_type == DataType.ACTIVITY_LEVEL:
                    profile['activity_level'] = data.get_parsed_value()
                elif data.data_type == DataType.LEARNING_PATTERN:
                    profile['learning_patterns'][data.key] = data.get_parsed_value()
                elif data.data_type == DataType.FEEDBACK_PATTERN:
                    profile['feedback_patterns'][data.key] = data.get_parsed_value()
            
            logger.info(f"Retrieved user profile for {user.email}")
            return profile
            
        except Exception as e:
            logger.error(f"Error getting user profile for {user.email}: {str(e)}")
            return {}
    
    def update_user_preference(
        self,
        user: User,
        preference_key: str,
        preference_value: Any,
        weight: Decimal = Decimal('1.0'),
        source: str = DataSource.USER_INPUT
    ) -> PersonalizationData:
        """
        Update or create a user preference.
        
        Args:
            user: The user
            preference_key: Key for the preference
            preference_value: Value of the preference
            weight: Importance weight
            source: Source of the data
            
        Returns:
            PersonalizationData: The created/updated preference
        """
        try:
            # Convert value to JSON string if needed
            if isinstance(preference_value, (dict, list)):
                value_str = json.dumps(preference_value)
            else:
                value_str = str(preference_value)
            
            # Update or create preference
            preference, created = PersonalizationData.objects.update_or_create(
                user=user,
                data_type=DataType.PREFERENCE,
                key=preference_key,
                defaults={
                    'value': value_str,
                    'weight': weight,
                    'source': source,
                    'confidence_score': Decimal('0.8'),
                    'is_active': True
                }
            )
            
            if not created:
                preference.increment_usage()
            
            logger.info(f"Updated preference {preference_key} for user {user.email}")
            return preference
            
        except Exception as e:
            logger.error(f"Error updating preference for user {user.email}: {str(e)}")
            raise
    
    def learn_from_feedback(
        self,
        user: User,
        recommendation_type: str,
        feedback_type: str,
        feedback_data: Dict[str, Any],
        rating: Optional[int] = None
    ):
        """
        Learn from user feedback on recommendations.
        
        Args:
            user: The user providing feedback
            recommendation_type: Type of recommendation
            feedback_type: Type of feedback (positive/negative/neutral)
            feedback_data: Additional feedback data
            rating: Optional rating (1-5)
        """
        try:
            # Update feedback patterns
            feedback_key = f"{recommendation_type}_feedback"
            
            # Get existing feedback pattern or create new
            try:
                feedback_pattern = PersonalizationData.objects.get(
                    user=user,
                    data_type=DataType.FEEDBACK_PATTERN,
                    key=feedback_key
                )
                
                existing_data = feedback_pattern.get_parsed_value()
                if not isinstance(existing_data, dict):
                    existing_data = {}
                
            except PersonalizationData.DoesNotExist:
                existing_data = {}
            
            # Update feedback data
            if feedback_type not in existing_data:
                existing_data[feedback_type] = []
            
            existing_data[feedback_type].append({
                'timestamp': timezone.now().isoformat(),
                'rating': rating,
                'data': feedback_data
            })
            
            # Calculate new weight based on feedback
            weight = self._calculate_feedback_weight(existing_data)
            
            # Save updated feedback pattern
            PersonalizationData.objects.update_or_create(
                user=user,
                data_type=DataType.FEEDBACK_PATTERN,
                key=feedback_key,
                defaults={
                    'value': json.dumps(existing_data),
                    'weight': weight,
                    'source': DataSource.FEEDBACK_ANALYSIS,
                    'confidence_score': Decimal('0.9')
                }
            )
            
            # Learn preferences from feedback
            self._extract_preferences_from_feedback(
                user, recommendation_type, feedback_type, feedback_data, rating
            )
            
            logger.info(f"Learned from feedback for user {user.email}")
            
        except Exception as e:
            logger.error(f"Error learning from feedback for user {user.email}: {str(e)}")
    
    def update_from_conversation(
        self,
        user: User,
        user_message: str,
        ai_response: Dict[str, Any],
        session_type: str
    ):
        """
        Update personalization data from conversation analysis.
        
        Args:
            user: The user
            user_message: User's message
            ai_response: AI's response
            session_type: Type of coaching session
        """
        try:
            # Extract preferences from conversation
            preferences = self._extract_preferences_from_message(user_message)
            
            for pref_key, pref_value in preferences.items():
                self.update_user_preference(
                    user=user,
                    preference_key=pref_key,
                    preference_value=pref_value,
                    weight=Decimal('0.7'),
                    source=DataSource.CONVERSATION_ANALYSIS
                )
            
            # Update behavioral patterns
            self._update_behavioral_patterns(user, user_message, session_type)
            
            # Update learning patterns
            self._update_learning_patterns(user, ai_response, session_type)
            
            logger.info(f"Updated personalization from conversation for user {user.email}")
            
        except Exception as e:
            logger.error(f"Error updating from conversation for user {user.email}: {str(e)}")
    
    def update_from_session_end(
        self,
        user: User,
        session: AICoachSession,
        satisfaction_score: Optional[int] = None,
        feedback: Optional[str] = None
    ):
        """
        Update personalization data when a session ends.
        
        Args:
            user: The user
            session: The completed session
            satisfaction_score: User satisfaction rating
            feedback: User feedback
        """
        try:
            # Update session preferences
            session_pref_key = f"session_type_{session.session_type}"
            
            # Get current session preferences
            try:
                session_pref = PersonalizationData.objects.get(
                    user=user,
                    data_type=DataType.PREFERENCE,
                    key=session_pref_key
                )
                
                existing_data = session_pref.get_parsed_value()
                if not isinstance(existing_data, dict):
                    existing_data = {'count': 0, 'total_satisfaction': 0}
                
            except PersonalizationData.DoesNotExist:
                existing_data = {'count': 0, 'total_satisfaction': 0}
            
            # Update session data
            existing_data['count'] += 1
            existing_data['last_session'] = timezone.now().isoformat()
            existing_data['last_duration'] = session.session_duration_minutes
            
            if satisfaction_score:
                existing_data['total_satisfaction'] += satisfaction_score
                existing_data['average_satisfaction'] = existing_data['total_satisfaction'] / existing_data['count']
            
            # Calculate preference weight
            weight = min(Decimal('5.0'), Decimal(str(existing_data['count'] * 0.1)))
            
            # Save updated preference
            PersonalizationData.objects.update_or_create(
                user=user,
                data_type=DataType.PREFERENCE,
                key=session_pref_key,
                defaults={
                    'value': json.dumps(existing_data),
                    'weight': weight,
                    'source': DataSource.BEHAVIORAL_ANALYSIS,
                    'confidence_score': Decimal('0.8')
                }
            )
            
            # Learn from feedback if provided
            if feedback and satisfaction_score:
                self.learn_from_feedback(
                    user=user,
                    recommendation_type="session",
                    feedback_type="positive" if satisfaction_score >= 4 else "negative",
                    feedback_data={'text': feedback, 'session_type': session.session_type},
                    rating=satisfaction_score
                )
            
            logger.info(f"Updated personalization from session end for user {user.email}")
            
        except Exception as e:
            logger.error(f"Error updating from session end for user {user.email}: {str(e)}")
    
    def get_personalization_insights(self, user: User) -> Dict[str, Any]:
        """
        Get insights about user's personalization data.
        
        Args:
            user: The user
            
        Returns:
            Dict containing personalization insights
        """
        try:
            data = PersonalizationData.objects.filter(user=user, is_active=True)
            
            insights = {
                'total_data_points': data.count(),
                'data_by_type': {},
                'high_confidence_data': data.filter(confidence_score__gte=Decimal('0.8')).count(),
                'verified_data': data.filter(is_verified=True).count(),
                'most_used_preferences': [],
                'learning_progress': self._calculate_learning_progress(data),
                'personalization_score': self._calculate_personalization_score(data)
            }
            
            # Group by data type
            for data_type in DataType.choices:
                type_data = data.filter(data_type=data_type[0])
                insights['data_by_type'][data_type[1]] = type_data.count()
            
            # Most used preferences
            preferences = data.filter(data_type=DataType.PREFERENCE).order_by('-usage_count')[:5]
            insights['most_used_preferences'] = [
                {'key': p.key, 'usage_count': p.usage_count, 'weight': float(p.weight)}
                for p in preferences
            ]
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting personalization insights for user {user.email}: {str(e)}")
            return {}
    
    def _extract_preferences_from_message(self, message: str) -> Dict[str, Any]:
        """Extract preferences from user message"""
        
        preferences = {}
        message_lower = message.lower()
        
        # Food preferences
        if 'love' in message_lower or 'like' in message_lower:
            foods = ['beef', 'steak', 'chicken', 'fish', 'eggs', 'bacon', 'lamb']
            for food in foods:
                if food in message_lower:
                    preferences[f'food_{food}'] = 'positive'
        
        if 'hate' in message_lower or 'dislike' in message_lower:
            foods = ['beef', 'steak', 'chicken', 'fish', 'eggs', 'bacon', 'lamb']
            for food in foods:
                if food in message_lower:
                    preferences[f'food_{food}'] = 'negative'
        
        # Meal timing preferences
        if 'morning' in message_lower:
            preferences['meal_timing'] = 'morning_person'
        elif 'evening' in message_lower:
            preferences['meal_timing'] = 'evening_person'
        
        # Cooking preferences
        if 'simple' in message_lower or 'easy' in message_lower:
            preferences['cooking_complexity'] = 'simple'
        elif 'complex' in message_lower or 'elaborate' in message_lower:
            preferences['cooking_complexity'] = 'complex'
        
        return preferences
    
    def _update_behavioral_patterns(self, user: User, message: str, session_type: str):
        """Update user behavioral patterns"""
        
        behavior_key = f"messaging_behavior_{session_type}"
        
        # Get existing behavior data
        try:
            behavior_data = PersonalizationData.objects.get(
                user=user,
                data_type=DataType.BEHAVIOR,
                key=behavior_key
            )
            
            existing_data = behavior_data.get_parsed_value()
            if not isinstance(existing_data, dict):
                existing_data = {'message_count': 0, 'total_length': 0}
                
        except PersonalizationData.DoesNotExist:
            existing_data = {'message_count': 0, 'total_length': 0}
        
        # Update behavior data
        existing_data['message_count'] += 1
        existing_data['total_length'] += len(message)
        existing_data['average_length'] = existing_data['total_length'] / existing_data['message_count']
        existing_data['last_message_time'] = timezone.now().isoformat()
        
        # Save updated behavior
        PersonalizationData.objects.update_or_create(
            user=user,
            data_type=DataType.BEHAVIOR,
            key=behavior_key,
            defaults={
                'value': json.dumps(existing_data),
                'weight': Decimal('1.0'),
                'source': DataSource.BEHAVIORAL_ANALYSIS,
                'confidence_score': Decimal('0.9')
            }
        )
    
    def _update_learning_patterns(self, user: User, ai_response: Dict[str, Any], session_type: str):
        """Update user learning patterns"""
        
        learning_key = f"learning_{session_type}"
        
        # Get existing learning data
        try:
            learning_data = PersonalizationData.objects.get(
                user=user,
                data_type=DataType.LEARNING_PATTERN,
                key=learning_key
            )
            
            existing_data = learning_data.get_parsed_value()
            if not isinstance(existing_data, dict):
                existing_data = {'response_types': {}, 'total_responses': 0}
                
        except PersonalizationData.DoesNotExist:
            existing_data = {'response_types': {}, 'total_responses': 0}
        
        # Update learning data
        response_type = ai_response.get('type', 'text')
        if response_type not in existing_data['response_types']:
            existing_data['response_types'][response_type] = 0
        
        existing_data['response_types'][response_type] += 1
        existing_data['total_responses'] += 1
        existing_data['last_response_time'] = timezone.now().isoformat()
        
        # Save updated learning pattern
        PersonalizationData.objects.update_or_create(
            user=user,
            data_type=DataType.LEARNING_PATTERN,
            key=learning_key,
            defaults={
                'value': json.dumps(existing_data),
                'weight': Decimal('1.0'),
                'source': DataSource.CONVERSATION_ANALYSIS,
                'confidence_score': Decimal('0.8')
            }
        )
    
    def _extract_preferences_from_feedback(
        self,
        user: User,
        recommendation_type: str,
        feedback_type: str,
        feedback_data: Dict[str, Any],
        rating: Optional[int]
    ):
        """Extract and update preferences from feedback"""
        
        # Extract preferences based on positive/negative feedback
        if feedback_type == 'positive' and rating and rating >= 4:
            # Strengthen preferences that led to positive feedback
            for key, value in feedback_data.items():
                if key != 'text':  # Skip feedback text
                    self.update_user_preference(
                        user=user,
                        preference_key=f"{recommendation_type}_{key}",
                        preference_value=value,
                        weight=Decimal('1.5'),
                        source=DataSource.FEEDBACK_ANALYSIS
                    )
        
        elif feedback_type == 'negative' and rating and rating <= 2:
            # Create negative preferences
            for key, value in feedback_data.items():
                if key != 'text':
                    self.update_user_preference(
                        user=user,
                        preference_key=f"{recommendation_type}_{key}_avoid",
                        preference_value=value,
                        weight=Decimal('2.0'),
                        source=DataSource.FEEDBACK_ANALYSIS
                    )
    
    def _calculate_feedback_weight(self, feedback_data: Dict[str, Any]) -> Decimal:
        """Calculate weight based on feedback patterns"""
        
        total_feedback = 0
        positive_feedback = 0
        
        for feedback_type, feedback_list in feedback_data.items():
            total_feedback += len(feedback_list)
            if feedback_type == 'positive':
                positive_feedback += len(feedback_list)
        
        if total_feedback == 0:
            return Decimal('1.0')
        
        # Higher weight for more positive feedback
        positive_ratio = positive_feedback / total_feedback
        return Decimal(str(min(5.0, 1.0 + positive_ratio * 2.0)))
    
    def _calculate_learning_progress(self, data) -> Dict[str, Any]:
        """Calculate user's learning progress"""
        
        total_usage = sum(d.usage_count for d in data)
        verified_count = data.filter(is_verified=True).count()
        total_count = data.count()
        
        return {
            'total_usage': total_usage,
            'verification_rate': verified_count / total_count if total_count > 0 else 0,
            'average_confidence': float(data.aggregate(
                avg_confidence=models.Avg('confidence_score')
            )['avg_confidence'] or 0)
        }
    
    def _calculate_personalization_score(self, data) -> float:
        """Calculate overall personalization score (0-100)"""
        
        if not data.exists():
            return 0.0
        
        # Factors contributing to personalization score
        factors = {
            'data_quantity': min(20, data.count()) / 20 * 30,  # 30% for having data
            'data_quality': float(data.aggregate(
                avg_confidence=models.Avg('confidence_score')
            )['avg_confidence'] or 0) * 25,  # 25% for confidence
            'verification_rate': (data.filter(is_verified=True).count() / data.count()) * 20,  # 20% for verification
            'usage_patterns': min(100, sum(d.usage_count for d in data)) / 100 * 25  # 25% for usage
        }
        
        return sum(factors.values()) 