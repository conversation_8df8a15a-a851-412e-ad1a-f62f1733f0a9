"""
AI Meal Plan Generation Service

This service generates personalized meal plans using AI based on user
preferences, dietary restrictions, and health goals.
"""
import logging
import json
from typing import Dict, List, Optional, Any
from decimal import Decimal
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model

from ..models import AIRecommendation, RecommendationType, RecommendationPriority

User = get_user_model()
logger = logging.getLogger(__name__)


class MealPlanAIService:
    """
    Service for generating AI-powered personalized meal plans.
    """
    
    def __init__(self):
        self.carnivore_foods = {
            'proteins': {
                'beef': {
                    'cuts': ['ribeye', 'sirloin', 'ground beef', 'filet mignon', 'chuck roast'],
                    'nutrition': {'protein': 25, 'fat': 15, 'calories': 250}
                },
                'pork': {
                    'cuts': ['bacon', 'pork chops', 'ground pork', 'pork shoulder'],
                    'nutrition': {'protein': 22, 'fat': 18, 'calories': 280}
                },
                'chicken': {
                    'cuts': ['thighs', 'drumsticks', 'whole chicken', 'ground chicken'],
                    'nutrition': {'protein': 28, 'fat': 8, 'calories': 180}
                },
                'fish': {
                    'cuts': ['salmon', 'mackerel', 'sardines', 'tuna', 'cod'],
                    'nutrition': {'protein': 25, 'fat': 12, 'calories': 200}
                },
                'other': {
                    'cuts': ['eggs', 'organ meats', 'bone marrow'],
                    'nutrition': {'protein': 20, 'fat': 15, 'calories': 220}
                }
            },
            'cooking_methods': ['grilled', 'pan-fried', 'roasted', 'sous-vide', 'slow-cooked'],
            'seasonings': ['salt', 'pepper', 'garlic powder', 'herbs']
        }
    
    def generate_weekly_meal_plan(
        self,
        user: User,
        preferences: Optional[Dict[str, Any]] = None,
        dietary_restrictions: Optional[List[str]] = None,
        calorie_target: Optional[int] = None,
        meal_count_per_day: int = 2
    ) -> List[AIRecommendation]:
        """
        Generate a complete weekly meal plan.
        
        Args:
            user: The user to generate meal plan for
            preferences: User food preferences
            dietary_restrictions: Any dietary restrictions
            calorie_target: Daily calorie target
            meal_count_per_day: Number of meals per day
            
        Returns:
            List of meal plan recommendations for the week
        """
        try:
            # Get user profile and preferences
            user_profile = self._build_user_profile(user, preferences, dietary_restrictions)
            
            # Calculate nutritional targets
            nutrition_targets = self._calculate_nutrition_targets(
                user_profile, calorie_target, meal_count_per_day
            )
            
            weekly_plans = []
            
            # Generate meal plan for each day
            for day in range(7):
                day_name = (datetime.now() + timedelta(days=day)).strftime('%A')
                
                daily_plan = self._generate_daily_meal_plan(
                    user_profile=user_profile,
                    nutrition_targets=nutrition_targets,
                    meal_count=meal_count_per_day,
                    day_number=day + 1,
                    day_name=day_name
                )
                
                recommendation = AIRecommendation.objects.create(
                    user=user,
                    recommendation_type=RecommendationType.MEAL_PLAN,
                    title=f"{day_name} Carnivore Meal Plan",
                    content=daily_plan['content'],
                    confidence_score=daily_plan['confidence'],
                    priority=RecommendationPriority.MEDIUM,
                    metadata=daily_plan['metadata'],
                    tags=['carnivore', 'meal-plan', day_name.lower()],
                    ai_model_version="meal-planner-carnivore-v1.0"
                )
                
                weekly_plans.append(recommendation)
            
            logger.info(f"Generated weekly meal plan for user {user.email}")
            return weekly_plans
            
        except Exception as e:
            logger.error(f"Error generating weekly meal plan for user {user.email}: {str(e)}")
            return []
    
    def generate_daily_meal_plan(
        self,
        user: User,
        date: Optional[datetime] = None,
        preferences: Optional[Dict[str, Any]] = None,
        meal_count: int = 2
    ) -> AIRecommendation:
        """
        Generate a meal plan for a specific day.
        
        Args:
            user: The user
            date: Date for the meal plan
            preferences: User preferences
            meal_count: Number of meals
            
        Returns:
            AIRecommendation for the daily meal plan
        """
        try:
            if not date:
                date = datetime.now()
            
            user_profile = self._build_user_profile(user, preferences)
            nutrition_targets = self._calculate_nutrition_targets(user_profile, None, meal_count)
            
            daily_plan = self._generate_daily_meal_plan(
                user_profile=user_profile,
                nutrition_targets=nutrition_targets,
                meal_count=meal_count,
                day_number=1,
                day_name=date.strftime('%A')
            )
            
            recommendation = AIRecommendation.objects.create(
                user=user,
                recommendation_type=RecommendationType.MEAL_PLAN,
                title=f"Carnivore Meal Plan - {date.strftime('%B %d, %Y')}",
                content=daily_plan['content'],
                confidence_score=daily_plan['confidence'],
                priority=RecommendationPriority.MEDIUM,
                metadata=daily_plan['metadata'],
                tags=['carnivore', 'meal-plan', 'daily'],
                ai_model_version="meal-planner-carnivore-v1.0"
            )
            
            logger.info(f"Generated daily meal plan for user {user.email}")
            return recommendation
            
        except Exception as e:
            logger.error(f"Error generating daily meal plan for user {user.email}: {str(e)}")
            raise
    
    def generate_meal_suggestions(
        self,
        user: User,
        meal_type: str = 'any',
        protein_preference: Optional[str] = None,
        cooking_time_limit: Optional[int] = None,
        limit: int = 5
    ) -> List[AIRecommendation]:
        """
        Generate specific meal suggestions.
        
        Args:
            user: The user
            meal_type: Type of meal (breakfast, lunch, dinner, any)
            protein_preference: Preferred protein type
            cooking_time_limit: Maximum cooking time in minutes
            limit: Number of suggestions
            
        Returns:
            List of meal recommendations
        """
        try:
            user_profile = self._build_user_profile(user)
            suggestions = []
            
            for i in range(limit):
                meal_suggestion = self._generate_single_meal(
                    user_profile=user_profile,
                    meal_type=meal_type,
                    protein_preference=protein_preference,
                    cooking_time_limit=cooking_time_limit
                )
                
                recommendation = AIRecommendation.objects.create(
                    user=user,
                    recommendation_type=RecommendationType.MEAL_PLAN,
                    title=meal_suggestion['title'],
                    content=meal_suggestion['content'],
                    confidence_score=meal_suggestion['confidence'],
                    priority=RecommendationPriority.MEDIUM,
                    metadata=meal_suggestion['metadata'],
                    tags=meal_suggestion['tags'],
                    ai_model_version="meal-suggester-v1.0"
                )
                
                suggestions.append(recommendation)
            
            logger.info(f"Generated {limit} meal suggestions for user {user.email}")
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating meal suggestions for user {user.email}: {str(e)}")
            return []
    
    def optimize_meal_plan_for_goals(
        self,
        user: User,
        health_goals: List[str],
        current_meal_plan: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Optimize meal plan based on specific health goals.
        
        Args:
            user: The user
            health_goals: List of health goals
            current_meal_plan: Current meal plan to optimize
            
        Returns:
            Optimized meal plan data
        """
        try:
            user_profile = self._build_user_profile(user)
            
            # Add health goals to profile
            user_profile['health_goals'] = health_goals
            
            # Determine optimization strategies
            optimization_strategies = self._determine_optimization_strategies(health_goals)
            
            # Apply optimizations
            optimized_plan = self._apply_optimizations(
                user_profile, current_meal_plan, optimization_strategies
            )
            
            logger.info(f"Optimized meal plan for user {user.email} with goals: {health_goals}")
            return optimized_plan
            
        except Exception as e:
            logger.error(f"Error optimizing meal plan for user {user.email}: {str(e)}")
            return {}
    
    def _build_user_profile(
        self,
        user: User,
        preferences: Optional[Dict[str, Any]] = None,
        dietary_restrictions: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Build comprehensive user profile for meal planning"""
        
        # Base profile
        profile = {
            'user_id': str(user.id),
            'age': getattr(user, 'age', 30),
            'gender': getattr(user, 'gender', 'unknown'),
            'activity_level': 'moderate',
            'preferred_proteins': ['beef', 'pork', 'chicken'],
            'cooking_skill': 'intermediate',
            'cooking_time_preference': 30,  # minutes
            'meal_frequency': 2,  # meals per day
            'carnivore_experience': 'intermediate',
            'budget_level': 'medium',
            'equipment_available': ['stove', 'oven', 'grill'],
            'dietary_restrictions': dietary_restrictions or [],
            'health_goals': []
        }
        
        # Merge provided preferences
        if preferences:
            profile.update(preferences)
        
        # Load user personalization data if available
        try:
            from .personalization_service import PersonalizationService
            personalization_service = PersonalizationService()
            user_data = personalization_service.get_user_profile(user)
            
            # Merge personalization data
            if user_data.get('preferences'):
                profile.update(user_data['preferences'])
            
            if user_data.get('restrictions'):
                profile['dietary_restrictions'].extend(user_data['restrictions'])
            
            if user_data.get('goals'):
                profile['health_goals'].extend(user_data['goals'])
                
        except Exception:
            pass  # Continue with base profile if personalization fails
        
        return profile
    
    def _calculate_nutrition_targets(
        self,
        user_profile: Dict[str, Any],
        calorie_target: Optional[int],
        meal_count: int
    ) -> Dict[str, Any]:
        """Calculate nutritional targets for meal planning"""
        
        # Estimate calorie needs if not provided
        if not calorie_target:
            # Basic calorie estimation (would be more sophisticated in production)
            base_calories = 1800 if user_profile['gender'] == 'female' else 2200
            
            activity_multipliers = {
                'sedentary': 1.0,
                'light': 1.2,
                'moderate': 1.4,
                'active': 1.6,
                'very_active': 1.8
            }
            
            multiplier = activity_multipliers.get(user_profile.get('activity_level', 'moderate'), 1.4)
            calorie_target = int(base_calories * multiplier)
        
        # Carnivore diet macros (approximate)
        protein_calories = calorie_target * 0.3  # 30% protein
        fat_calories = calorie_target * 0.70     # 70% fat
        
        return {
            'daily_calories': calorie_target,
            'daily_protein_g': protein_calories / 4,  # 4 calories per gram
            'daily_fat_g': fat_calories / 9,          # 9 calories per gram
            'daily_carbs_g': 0,                       # Carnivore = 0 carbs
            'meal_count': meal_count,
            'calories_per_meal': calorie_target / meal_count,
            'protein_per_meal': (protein_calories / 4) / meal_count,
            'fat_per_meal': (fat_calories / 9) / meal_count
        }
    
    def _generate_daily_meal_plan(
        self,
        user_profile: Dict[str, Any],
        nutrition_targets: Dict[str, Any],
        meal_count: int,
        day_number: int,
        day_name: str
    ) -> Dict[str, Any]:
        """Generate a complete daily meal plan"""
        
        meals = []
        total_calories = 0
        total_protein = 0
        total_fat = 0
        
        meal_types = self._get_meal_types(meal_count)
        
        for i, meal_type in enumerate(meal_types):
            meal = self._generate_single_meal(
                user_profile=user_profile,
                meal_type=meal_type,
                target_calories=nutrition_targets['calories_per_meal'],
                target_protein=nutrition_targets['protein_per_meal'],
                target_fat=nutrition_targets['fat_per_meal']
            )
            
            meals.append(meal)
            total_calories += meal['metadata']['calories']
            total_protein += meal['metadata']['protein']
            total_fat += meal['metadata']['fat']
        
        # Format meal plan content
        content = self._format_daily_meal_plan_content(meals, day_name)
        
        # Calculate confidence based on how well we met targets
        calorie_accuracy = 1 - abs(total_calories - nutrition_targets['daily_calories']) / nutrition_targets['daily_calories']
        confidence = max(Decimal('0.6'), Decimal(str(min(1.0, calorie_accuracy))))
        
        return {
            'content': content,
            'confidence': confidence,
            'metadata': {
                'day': day_name,
                'meal_count': meal_count,
                'total_calories': total_calories,
                'total_protein': total_protein,
                'total_fat': total_fat,
                'target_calories': nutrition_targets['daily_calories'],
                'meals': [meal['metadata'] for meal in meals]
            }
        }
    
    def _generate_single_meal(
        self,
        user_profile: Dict[str, Any],
        meal_type: str = 'any',
        protein_preference: Optional[str] = None,
        cooking_time_limit: Optional[int] = None,
        target_calories: Optional[float] = None,
        target_protein: Optional[float] = None,
        target_fat: Optional[float] = None
    ) -> Dict[str, Any]:
        """Generate a single meal"""
        
        # Select protein based on preferences
        available_proteins = list(self.carnivore_foods['proteins'].keys())
        
        if protein_preference and protein_preference in available_proteins:
            protein_type = protein_preference
        else:
            # Choose based on user preferences or randomly
            preferred = [p for p in user_profile.get('preferred_proteins', []) if p in available_proteins]
            if preferred:
                import random
                protein_type = random.choice(preferred)
            else:
                import random
                protein_type = random.choice(available_proteins)
        
        # Select specific cut
        protein_data = self.carnivore_foods['proteins'][protein_type]
        import random
        cut = random.choice(protein_data['cuts'])
        
        # Select cooking method
        cooking_methods = self.carnivore_foods['cooking_methods']
        if cooking_time_limit and cooking_time_limit < 20:
            # Quick cooking methods
            cooking_methods = ['pan-fried', 'grilled']
        
        cooking_method = random.choice(cooking_methods)
        
        # Calculate portions based on targets
        if target_calories:
            base_nutrition = protein_data['nutrition']
            portion_multiplier = target_calories / base_nutrition['calories']
            calories = target_calories
            protein = base_nutrition['protein'] * portion_multiplier
            fat = base_nutrition['fat'] * portion_multiplier
            portion_oz = 4 * portion_multiplier  # Base 4 oz serving
        else:
            portion_oz = random.choice([4, 6, 8, 10])
            portion_multiplier = portion_oz / 4
            base_nutrition = protein_data['nutrition']
            calories = base_nutrition['calories'] * portion_multiplier
            protein = base_nutrition['protein'] * portion_multiplier
            fat = base_nutrition['fat'] * portion_multiplier
        
        # Generate meal content
        title = f"{cooking_method.title()} {cut.title()}"
        
        content = self._generate_meal_instructions(
            protein_type, cut, cooking_method, portion_oz, meal_type
        )
        
        return {
            'title': title,
            'content': content,
            'confidence': Decimal('0.85'),
            'metadata': {
                'meal_type': meal_type,
                'protein_type': protein_type,
                'cut': cut,
                'cooking_method': cooking_method,
                'portion_oz': round(portion_oz, 1),
                'calories': round(calories),
                'protein': round(protein, 1),
                'fat': round(fat, 1),
                'carbs': 0,
                'cooking_time': self._estimate_cooking_time(cooking_method, portion_oz),
                'difficulty': 'easy'
            },
            'tags': ['carnivore', protein_type, cooking_method, meal_type]
        }
    
    def _get_meal_types(self, meal_count: int) -> List[str]:
        """Get meal types based on number of meals per day"""
        
        if meal_count == 1:
            return ['omad']  # One Meal A Day
        elif meal_count == 2:
            return ['breakfast', 'dinner']
        elif meal_count == 3:
            return ['breakfast', 'lunch', 'dinner']
        else:
            types = ['breakfast', 'lunch', 'dinner']
            types.extend(['snack'] * (meal_count - 3))
            return types
    
    def _generate_meal_instructions(
        self,
        protein_type: str,
        cut: str,
        cooking_method: str,
        portion_oz: float,
        meal_type: str
    ) -> str:
        """Generate cooking instructions for the meal"""
        
        instructions = {
            'grilled': f"Season {portion_oz} oz {cut} with salt and pepper. Preheat grill to medium-high heat. Grill for 4-6 minutes per side until desired doneness.",
            'pan-fried': f"Heat a cast iron pan over medium-high heat. Season {portion_oz} oz {cut} with salt. Cook for 3-4 minutes per side, letting it develop a nice crust.",
            'roasted': f"Preheat oven to 400°F. Season {portion_oz} oz {cut} with salt and herbs. Roast for 15-20 minutes until cooked through.",
            'sous-vide': f"Season {portion_oz} oz {cut} with salt. Vacuum seal and cook sous-vide at 135°F for 1-2 hours. Finish with a quick sear in a hot pan.",
            'slow-cooked': f"Season {portion_oz} oz {cut} with salt and place in slow cooker. Cook on low for 6-8 hours until tender."
        }
        
        base_instruction = instructions.get(cooking_method, instructions['pan-fried'])
        
        # Add meal-specific suggestions
        if meal_type == 'breakfast':
            base_instruction += " Serve with scrambled eggs cooked in the rendered fat."
        elif meal_type == 'dinner':
            base_instruction += " Let rest for 5 minutes before serving to retain juices."
        
        return base_instruction
    
    def _format_daily_meal_plan_content(self, meals: List[Dict], day_name: str) -> str:
        """Format the daily meal plan content"""
        
        content = f"# {day_name} Carnivore Meal Plan\n\n"
        
        for i, meal in enumerate(meals, 1):
            meal_meta = meal['metadata']
            content += f"## Meal {i}: {meal['title']}\n"
            content += f"{meal['content']}\n\n"
            content += f"**Nutrition:** {meal_meta['calories']} calories, "
            content += f"{meal_meta['protein']}g protein, {meal_meta['fat']}g fat\n"
            content += f"**Cooking time:** {meal_meta['cooking_time']} minutes\n\n"
        
        total_calories = sum(meal['metadata']['calories'] for meal in meals)
        total_protein = sum(meal['metadata']['protein'] for meal in meals)
        total_fat = sum(meal['metadata']['fat'] for meal in meals)
        
        content += f"## Daily Totals\n"
        content += f"- **Calories:** {total_calories}\n"
        content += f"- **Protein:** {round(total_protein, 1)}g\n"
        content += f"- **Fat:** {round(total_fat, 1)}g\n"
        content += f"- **Carbs:** 0g\n"
        
        return content
    
    def _estimate_cooking_time(self, cooking_method: str, portion_oz: float) -> int:
        """Estimate cooking time based on method and portion size"""
        
        base_times = {
            'grilled': 15,
            'pan-fried': 10,
            'roasted': 25,
            'sous-vide': 90,
            'slow-cooked': 360
        }
        
        base_time = base_times.get(cooking_method, 15)
        
        # Adjust for portion size
        if portion_oz > 8:
            base_time += 5
        elif portion_oz < 4:
            base_time -= 5
        
        return max(5, base_time)  # Minimum 5 minutes
    
    def _determine_optimization_strategies(self, health_goals: List[str]) -> List[str]:
        """Determine optimization strategies based on health goals"""
        
        strategies = []
        
        for goal in health_goals:
            if 'weight loss' in goal.lower():
                strategies.extend(['reduce_calories', 'increase_protein_ratio'])
            elif 'muscle gain' in goal.lower():
                strategies.extend(['increase_protein', 'add_organ_meats'])
            elif 'energy' in goal.lower():
                strategies.extend(['optimize_fat_sources', 'meal_timing'])
            elif 'inflammation' in goal.lower():
                strategies.extend(['focus_grass_fed', 'add_fish'])
        
        return list(set(strategies))  # Remove duplicates
    
    def _apply_optimizations(
        self,
        user_profile: Dict[str, Any],
        current_plan: Optional[Dict[str, Any]],
        strategies: List[str]
    ) -> Dict[str, Any]:
        """Apply optimization strategies to meal plan"""
        
        optimizations = {
            'strategies_applied': strategies,
            'recommendations': [],
            'macro_adjustments': {},
            'food_swaps': []
        }
        
        for strategy in strategies:
            if strategy == 'reduce_calories':
                optimizations['macro_adjustments']['calorie_reduction'] = 0.8
                optimizations['recommendations'].append(
                    "Reduced portion sizes by 20% to support weight loss goals"
                )
            
            elif strategy == 'increase_protein':
                optimizations['macro_adjustments']['protein_increase'] = 1.2
                optimizations['recommendations'].append(
                    "Increased protein intake to support muscle building"
                )
            
            elif strategy == 'focus_grass_fed':
                optimizations['food_swaps'].append({
                    'from': 'conventional beef',
                    'to': 'grass-fed beef',
                    'reason': 'Better omega-3 profile for inflammation reduction'
                })
        
        return optimizations 