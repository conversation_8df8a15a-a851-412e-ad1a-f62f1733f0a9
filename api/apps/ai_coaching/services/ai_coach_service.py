"""
Core AI Coaching Service

This service handles the main AI coaching logic, coordinating between
different AI services and managing coaching sessions.
"""
import logging
from typing import Dict, List, Optional, Any
from django.contrib.auth import get_user_model
from django.utils import timezone

from ..models import AICoachSession, SessionType, SessionStatus
from .recommendation_engine_service import RecommendationEngineService
from .chat_bot_service import ChatBotService
from .personalization_service import PersonalizationService

User = get_user_model()
logger = logging.getLogger(__name__)


class AICoachService:
    """
    Core AI coaching service that orchestrates various AI components
    to provide personalized coaching experiences.
    """
    
    def __init__(self):
        self.recommendation_engine = RecommendationEngineService()
        self.chat_bot = ChatBotService()
        self.personalization = PersonalizationService()
    
    def start_coaching_session(
        self, 
        user: User, 
        session_type: str = SessionType.GENERAL_COACHING,
        initial_message: Optional[str] = None
    ) -> AICoachSession:
        """
        Start a new AI coaching session for a user.
        
        Args:
            user: The user starting the session
            session_type: Type of coaching session
            initial_message: Optional initial message from user
            
        Returns:
            AICoachSession: The created session
        """
        try:
            # End any active sessions first
            self._end_active_sessions(user)
            
            # Create new session
            session = AICoachSession.objects.create(
                user=user,
                session_type=session_type,
                status=SessionStatus.ACTIVE,
                ai_model_version="gpt-4-coaching-v1.0"
            )
            
            # Load user personalization data
            user_profile = self.personalization.get_user_profile(user)
            
            # Generate initial greeting and recommendations
            greeting = self._generate_session_greeting(user, session_type, user_profile)
            
            # Create initial AI message
            if greeting:
                self.chat_bot.create_message(
                    session=session,
                    sender="ai",
                    message=greeting,
                    message_type="text"
                )
            
            # Process initial user message if provided
            if initial_message:
                self.chat_bot.create_message(
                    session=session,
                    sender="user",
                    message=initial_message,
                    message_type="text"
                )
                
                # Generate AI response
                ai_response = self.chat_bot.generate_response(
                    session=session,
                    user_message=initial_message,
                    user_profile=user_profile
                )
                
                if ai_response:
                    self.chat_bot.create_message(
                        session=session,
                        sender="ai",
                        message=ai_response['message'],
                        message_type=ai_response.get('type', 'text'),
                        confidence_score=ai_response.get('confidence')
                    )
            
            logger.info(f"Started coaching session {session.id} for user {user.email}")
            return session
            
        except Exception as e:
            logger.error(f"Error starting coaching session for user {user.email}: {str(e)}")
            raise
    
    def process_user_message(
        self, 
        session: AICoachSession, 
        message: str,
        message_type: str = "text"
    ) -> Dict[str, Any]:
        """
        Process a user message and generate AI response.
        
        Args:
            session: The coaching session
            message: User's message
            message_type: Type of message
            
        Returns:
            Dict containing AI response and any recommendations
        """
        try:
            if session.status != SessionStatus.ACTIVE:
                raise ValueError("Session is not active")
            
            # Create user message record
            user_msg = self.chat_bot.create_message(
                session=session,
                sender="user",
                message=message,
                message_type=message_type
            )
            
            # Get user profile for personalization
            user_profile = self.personalization.get_user_profile(session.user)
            
            # Generate AI response
            ai_response = self.chat_bot.generate_response(
                session=session,
                user_message=message,
                user_profile=user_profile
            )
            
            # Create AI response message
            ai_msg = None
            if ai_response:
                ai_msg = self.chat_bot.create_message(
                    session=session,
                    sender="ai",
                    message=ai_response['message'],
                    message_type=ai_response.get('type', 'text'),
                    confidence_score=ai_response.get('confidence'),
                    parent_message=user_msg
                )
            
            # Generate recommendations if appropriate
            recommendations = []
            if ai_response.get('should_recommend', False):
                recommendations = self.recommendation_engine.generate_contextual_recommendations(
                    user=session.user,
                    session=session,
                    context=ai_response.get('context', {}),
                    limit=3
                )
            
            # Update personalization data based on conversation
            self.personalization.update_from_conversation(
                user=session.user,
                user_message=message,
                ai_response=ai_response,
                session_type=session.session_type
            )
            
            return {
                'ai_message': ai_msg,
                'ai_response_text': ai_response.get('message', '') if ai_response else '',
                'recommendations': recommendations,
                'confidence': ai_response.get('confidence') if ai_response else None,
                'should_end_session': ai_response.get('should_end_session', False) if ai_response else False
            }
            
        except Exception as e:
            logger.error(f"Error processing user message in session {session.id}: {str(e)}")
            raise
    
    def end_coaching_session(
        self, 
        session: AICoachSession,
        user_feedback: Optional[str] = None,
        satisfaction_score: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        End a coaching session and generate summary.
        
        Args:
            session: The session to end
            user_feedback: Optional user feedback
            satisfaction_score: User satisfaction rating (1-5)
            
        Returns:
            Dict containing session summary
        """
        try:
            if session.status != SessionStatus.ACTIVE:
                raise ValueError("Session is not active")
            
            # Generate session summary
            summary = self._generate_session_summary(session)
            
            # Update session
            session.end_session(summary=summary)
            
            if satisfaction_score:
                session.user_satisfaction_score = satisfaction_score
                session.save(update_fields=['user_satisfaction_score'])
            
            # Update personalization based on session outcome
            self.personalization.update_from_session_end(
                user=session.user,
                session=session,
                satisfaction_score=satisfaction_score,
                feedback=user_feedback
            )
            
            logger.info(f"Ended coaching session {session.id} for user {session.user.email}")
            
            return {
                'summary': summary,
                'duration_minutes': session.session_duration_minutes,
                'recommendations_count': len(session.recommendations_given),
                'satisfaction_score': satisfaction_score
            }
            
        except Exception as e:
            logger.error(f"Error ending coaching session {session.id}: {str(e)}")
            raise
    
    def get_user_coaching_stats(self, user: User) -> Dict[str, Any]:
        """
        Get coaching statistics for a user.
        
        Args:
            user: The user to get stats for
            
        Returns:
            Dict containing user coaching statistics
        """
        try:
            sessions = AICoachSession.objects.filter(user=user)
            
            stats = {
                'total_sessions': sessions.count(),
                'completed_sessions': sessions.filter(status=SessionStatus.COMPLETED).count(),
                'total_coaching_hours': sum(
                    s.duration_hours for s in sessions 
                    if s.session_duration_minutes
                ) or 0,
                'average_satisfaction': sessions.filter(
                    user_satisfaction_score__isnull=False
                ).aggregate(
                    avg_satisfaction=models.Avg('user_satisfaction_score')
                )['avg_satisfaction'],
                'session_types': list(
                    sessions.values('session_type').annotate(
                        count=models.Count('id')
                    )
                ),
                'recent_sessions': sessions.order_by('-start_time')[:5]
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting coaching stats for user {user.email}: {str(e)}")
            raise
    
    def _end_active_sessions(self, user: User):
        """End any active sessions for the user"""
        active_sessions = AICoachSession.objects.filter(
            user=user,
            status=SessionStatus.ACTIVE
        )
        
        for session in active_sessions:
            summary = "Session ended automatically when new session started"
            session.end_session(summary=summary)
    
    def _generate_session_greeting(
        self, 
        user: User, 
        session_type: str, 
        user_profile: Dict[str, Any]
    ) -> str:
        """Generate personalized greeting for the session"""
        
        greetings = {
            SessionType.NUTRITION_COACHING: f"Hello {user.first_name or 'there'}! I'm here to help you with your nutrition goals. What would you like to focus on today?",
            SessionType.FITNESS_PLANNING: f"Hi {user.first_name or 'there'}! Ready to plan your fitness journey? Let's create a workout plan that works for you.",
            SessionType.MEAL_PLANNING: f"Welcome {user.first_name or 'there'}! I'm excited to help you plan some delicious and nutritious meals. What are you in the mood for?",
            SessionType.CARNIVORE_GUIDANCE: f"Hello {user.first_name or 'there'}! I'm here to guide you on your carnivore diet journey. How can I help you today?",
            SessionType.GENERAL_COACHING: f"Hi {user.first_name or 'there'}! I'm your AI health coach. What health or nutrition topic would you like to explore today?"
        }
        
        return greetings.get(session_type, greetings[SessionType.GENERAL_COACHING])
    
    def _generate_session_summary(self, session: AICoachSession) -> str:
        """Generate a summary of the coaching session"""
        
        messages = session.chat_messages.all()
        recommendations = session.recommendations.all()
        
        summary_parts = [
            f"Coaching session summary for {session.get_session_type_display()}:",
            f"- Duration: {session.session_duration_minutes or 0} minutes",
            f"- Messages exchanged: {messages.count()}",
            f"- Recommendations provided: {recommendations.count()}",
        ]
        
        if recommendations.exists():
            rec_types = recommendations.values_list('recommendation_type', flat=True).distinct()
            summary_parts.append(f"- Recommendation types: {', '.join(rec_types)}")
        
        # Add key topics discussed (this would be enhanced with NLP)
        ai_messages = messages.filter(sender="ai")
        if ai_messages.exists():
            summary_parts.append("- Key topics covered during the session")
        
        return "\n".join(summary_parts) 