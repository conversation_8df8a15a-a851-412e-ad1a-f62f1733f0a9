# Generated by Django 4.2.23 on 2025-06-20 17:56

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AICoachSession",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("nutrition_coaching", "Nutrition Coaching"),
                            ("fitness_planning", "Fitness Planning"),
                            ("meal_planning", "Meal Planning"),
                            ("health_assessment", "Health Assessment"),
                            ("goal_setting", "Goal Setting"),
                            ("progress_review", "Progress Review"),
                            ("carnivore_guidance", "Carnivore Diet Guidance"),
                            ("general_coaching", "General Coaching"),
                        ],
                        default="general_coaching",
                        help_text="Type of AI coaching session",
                        max_length=20,
                        verbose_name="Session Type",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("paused", "Paused"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="active",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="When the coaching session started",
                        verbose_name="Start Time",
                    ),
                ),
                (
                    "end_time",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the coaching session ended",
                        null=True,
                        verbose_name="End Time",
                    ),
                ),
                (
                    "summary",
                    models.TextField(
                        blank=True,
                        help_text="AI-generated summary of the coaching session",
                        null=True,
                        verbose_name="Session Summary",
                    ),
                ),
                (
                    "goals_discussed",
                    models.JSONField(
                        default=list,
                        help_text="List of goals discussed during the session",
                        verbose_name="Goals Discussed",
                    ),
                ),
                (
                    "recommendations_given",
                    models.JSONField(
                        default=list,
                        help_text="List of recommendation IDs provided during session",
                        verbose_name="Recommendations Given",
                    ),
                ),
                (
                    "user_satisfaction_score",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="User rating from 1-5 for session quality",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="User Satisfaction Score",
                    ),
                ),
                (
                    "session_duration_minutes",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Total duration of the session in minutes",
                        null=True,
                        verbose_name="Session Duration (minutes)",
                    ),
                ),
                (
                    "ai_model_version",
                    models.CharField(
                        blank=True,
                        help_text="Version of AI model used for this session",
                        max_length=50,
                        null=True,
                        verbose_name="AI Model Version",
                    ),
                ),
                (
                    "session_metadata",
                    models.JSONField(
                        default=dict,
                        help_text="Additional metadata about the session",
                        verbose_name="Session Metadata",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_coach_sessions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "AI Coach Session",
                "verbose_name_plural": "AI Coach Sessions",
                "db_table": "ai_coach_session",
                "ordering": ["-start_time"],
            },
        ),
        migrations.CreateModel(
            name="PersonalizationData",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "data_type",
                    models.CharField(
                        choices=[
                            ("preference", "User Preference"),
                            ("behavior", "User Behavior"),
                            ("goal", "User Goal"),
                            ("restriction", "Dietary Restriction"),
                            ("allergy", "Food Allergy"),
                            ("health_condition", "Health Condition"),
                            ("activity_level", "Activity Level"),
                            ("meal_timing", "Meal Timing Preference"),
                            ("supplement", "Supplement Usage"),
                            ("carnivore_adaptation", "Carnivore Diet Adaptation"),
                            ("learning_pattern", "Learning Pattern"),
                            ("feedback_pattern", "Feedback Pattern"),
                        ],
                        max_length=25,
                        verbose_name="Data Type",
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        help_text="Specific key for this data point (e.g., 'breakfast_time', 'protein_preference')",
                        max_length=100,
                        verbose_name="Key",
                    ),
                ),
                (
                    "value",
                    models.TextField(
                        help_text="The actual value (can be JSON for complex data)",
                        verbose_name="Value",
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.00"),
                        help_text="Importance weight for this data point (0.00-10.00)",
                        max_digits=3,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("10.00")),
                        ],
                        verbose_name="Weight",
                    ),
                ),
                (
                    "confidence_score",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.80"),
                        help_text="AI confidence in this data point (0.00-1.00)",
                        max_digits=3,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("1.00")),
                        ],
                        verbose_name="Confidence Score",
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("user_input", "User Input"),
                            ("behavioral_analysis", "Behavioral Analysis"),
                            ("feedback_analysis", "Feedback Analysis"),
                            ("conversation_analysis", "Conversation Analysis"),
                            ("calculation_result", "Calculator Result"),
                            ("progress_tracking", "Progress Tracking"),
                            ("external_integration", "External Integration"),
                        ],
                        default="user_input",
                        max_length=25,
                        verbose_name="Data Source",
                    ),
                ),
                (
                    "context",
                    models.JSONField(
                        default=dict,
                        help_text="Additional context about when/how this data was collected",
                        verbose_name="Context",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this data point is currently active",
                        verbose_name="Is Active",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this data has been verified by user",
                        verbose_name="Is Verified",
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When this data point expires (if applicable)",
                        null=True,
                        verbose_name="Expires At",
                    ),
                ),
                (
                    "last_validated_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When this data was last validated",
                        null=True,
                        verbose_name="Last Validated At",
                    ),
                ),
                (
                    "validation_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of times this data has been validated",
                        verbose_name="Validation Count",
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of times this data has been used in recommendations",
                        verbose_name="Usage Count",
                    ),
                ),
                (
                    "correlation_data",
                    models.JSONField(
                        default=dict,
                        help_text="Data about correlations with other user data points",
                        verbose_name="Correlation Data",
                    ),
                ),
                (
                    "learning_metadata",
                    models.JSONField(
                        default=dict,
                        help_text="Metadata about how AI learned this data point",
                        verbose_name="Learning Metadata",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="personalization_data",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Personalization Data",
                "verbose_name_plural": "Personalization Data",
                "db_table": "ai_personalization_data",
                "ordering": ["-updated"],
                "indexes": [
                    models.Index(
                        fields=["user", "data_type"],
                        name="ai_personal_user_id_a3c4dc_idx",
                    ),
                    models.Index(
                        fields=["user", "is_active"],
                        name="ai_personal_user_id_f44997_idx",
                    ),
                    models.Index(
                        fields=["data_type", "key"],
                        name="ai_personal_data_ty_246cb6_idx",
                    ),
                    models.Index(
                        fields=["weight", "confidence_score"],
                        name="ai_personal_weight_6aaf4e_idx",
                    ),
                    models.Index(
                        fields=["source", "-created"],
                        name="ai_personal_source_ee98c9_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="ai_personal_expires_01fb50_idx"
                    ),
                ],
                "unique_together": {("user", "data_type", "key")},
            },
        ),
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "sender",
                    models.CharField(
                        choices=[
                            ("user", "User"),
                            ("ai", "AI Coach"),
                            ("system", "System"),
                        ],
                        max_length=10,
                        verbose_name="Sender",
                    ),
                ),
                (
                    "message",
                    models.TextField(
                        help_text="The actual message content", verbose_name="Message"
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("text", "Text"),
                            ("recommendation", "Recommendation"),
                            ("meal_plan", "Meal Plan"),
                            ("recipe", "Recipe"),
                            ("exercise", "Exercise"),
                            ("health_tip", "Health Tip"),
                            ("question", "Question"),
                            ("follow_up", "Follow-up"),
                            ("clarification", "Clarification"),
                            ("feedback_request", "Feedback Request"),
                        ],
                        default="text",
                        max_length=20,
                        verbose_name="Message Type",
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="Timestamp"),
                ),
                (
                    "metadata",
                    models.JSONField(
                        default=dict,
                        help_text="Additional message metadata (attachments, formatting, etc.)",
                        verbose_name="Metadata",
                    ),
                ),
                (
                    "ai_processing_time_ms",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Time taken for AI to process and respond",
                        null=True,
                        verbose_name="AI Processing Time (ms)",
                    ),
                ),
                (
                    "confidence_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="AI confidence in the response (0.00-1.00)",
                        max_digits=3,
                        null=True,
                        verbose_name="Confidence Score",
                    ),
                ),
                (
                    "is_sensitive",
                    models.BooleanField(
                        default=False,
                        help_text="Whether message contains sensitive health information",
                        verbose_name="Is Sensitive",
                    ),
                ),
                (
                    "intent_detected",
                    models.CharField(
                        blank=True,
                        help_text="AI-detected user intent for this message",
                        max_length=100,
                        null=True,
                        verbose_name="Intent Detected",
                    ),
                ),
                (
                    "entities_extracted",
                    models.JSONField(
                        default=list,
                        help_text="Named entities extracted from the message",
                        verbose_name="Entities Extracted",
                    ),
                ),
                (
                    "sentiment_score",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Sentiment analysis score (-1.00 to 1.00)",
                        max_digits=3,
                        null=True,
                        verbose_name="Sentiment Score",
                    ),
                ),
                (
                    "is_flagged",
                    models.BooleanField(
                        default=False,
                        help_text="Whether message is flagged for review",
                        verbose_name="Is Flagged",
                    ),
                ),
                (
                    "flag_reason",
                    models.CharField(
                        blank=True,
                        help_text="Reason for flagging this message",
                        max_length=100,
                        null=True,
                        verbose_name="Flag Reason",
                    ),
                ),
                (
                    "ai_model_version",
                    models.CharField(
                        blank=True,
                        help_text="Version of AI model that processed this message",
                        max_length=50,
                        null=True,
                        verbose_name="AI Model Version",
                    ),
                ),
                (
                    "parent_message",
                    models.ForeignKey(
                        blank=True,
                        help_text="Message this is replying to",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="ai_coaching.chatmessage",
                        verbose_name="Parent Message",
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_messages",
                        to="ai_coaching.aicoachsession",
                        verbose_name="Session",
                    ),
                ),
            ],
            options={
                "verbose_name": "Chat Message",
                "verbose_name_plural": "Chat Messages",
                "db_table": "ai_chat_message",
                "ordering": ["timestamp"],
                "indexes": [
                    models.Index(
                        fields=["session", "timestamp"],
                        name="ai_chat_mes_session_5ca83e_idx",
                    ),
                    models.Index(
                        fields=["sender", "timestamp"],
                        name="ai_chat_mes_sender_828a07_idx",
                    ),
                    models.Index(
                        fields=["message_type", "timestamp"],
                        name="ai_chat_mes_message_9ffa5e_idx",
                    ),
                    models.Index(
                        fields=["is_flagged", "timestamp"],
                        name="ai_chat_mes_is_flag_ae0451_idx",
                    ),
                    models.Index(
                        fields=["intent_detected"],
                        name="ai_chat_mes_intent__82ae6b_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="AIRecommendation",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "recommendation_type",
                    models.CharField(
                        choices=[
                            ("meal_plan", "Meal Plan"),
                            ("recipe", "Recipe"),
                            ("health_tip", "Health Tip"),
                            ("exercise", "Exercise"),
                            ("carnivore_advice", "Carnivore Diet Advice"),
                            ("supplement", "Supplement"),
                            ("lifestyle", "Lifestyle Change"),
                            ("nutrition_timing", "Nutrition Timing"),
                        ],
                        max_length=20,
                        verbose_name="Recommendation Type",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="Short title for the recommendation",
                        max_length=200,
                        verbose_name="Title",
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        help_text="Detailed recommendation content",
                        verbose_name="Content",
                    ),
                ),
                (
                    "confidence_score",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="AI confidence in this recommendation (0.00-1.00)",
                        max_digits=3,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("1.00")),
                        ],
                        verbose_name="Confidence Score",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("viewed", "Viewed"),
                            ("accepted", "Accepted"),
                            ("declined", "Declined"),
                            ("completed", "Completed"),
                        ],
                        default="pending",
                        max_length=10,
                        verbose_name="Status",
                    ),
                ),
                (
                    "tags",
                    models.JSONField(
                        default=list,
                        help_text="Tags for categorizing recommendations",
                        verbose_name="Tags",
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        default=dict,
                        help_text="Additional data specific to recommendation type",
                        verbose_name="Metadata",
                    ),
                ),
                (
                    "expected_outcomes",
                    models.JSONField(
                        default=list,
                        help_text="List of expected outcomes from following this recommendation",
                        verbose_name="Expected Outcomes",
                    ),
                ),
                (
                    "personalization_factors",
                    models.JSONField(
                        default=dict,
                        help_text="Factors used to personalize this recommendation",
                        verbose_name="Personalization Factors",
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When this recommendation expires",
                        null=True,
                        verbose_name="Expires At",
                    ),
                ),
                (
                    "viewed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When user first viewed this recommendation",
                        null=True,
                        verbose_name="Viewed At",
                    ),
                ),
                (
                    "accepted_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When user accepted this recommendation",
                        null=True,
                        verbose_name="Accepted At",
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When user completed this recommendation",
                        null=True,
                        verbose_name="Completed At",
                    ),
                ),
                (
                    "user_feedback",
                    models.TextField(
                        blank=True,
                        help_text="User's feedback on this recommendation",
                        null=True,
                        verbose_name="User Feedback",
                    ),
                ),
                (
                    "user_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="User rating from 1-5 for recommendation quality",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="User Rating",
                    ),
                ),
                (
                    "ai_model_version",
                    models.CharField(
                        blank=True,
                        help_text="Version of AI model that generated this recommendation",
                        max_length=50,
                        null=True,
                        verbose_name="AI Model Version",
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        blank=True,
                        help_text="Associated coaching session",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recommendations",
                        to="ai_coaching.aicoachsession",
                        verbose_name="Session",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_recommendations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "AI Recommendation",
                "verbose_name_plural": "AI Recommendations",
                "db_table": "ai_recommendation",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="ai_recommen_user_id_af90a3_idx",
                    ),
                    models.Index(
                        fields=["recommendation_type", "-created"],
                        name="ai_recommen_recomme_4d6836_idx",
                    ),
                    models.Index(
                        fields=["status", "priority"],
                        name="ai_recommen_status_df57e9_idx",
                    ),
                    models.Index(
                        fields=["user", "status"], name="ai_recommen_user_id_f9c3b9_idx"
                    ),
                    models.Index(
                        fields=["confidence_score"],
                        name="ai_recommen_confide_226a48_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="ai_recommen_expires_7a463f_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="aicoachsession",
            index=models.Index(
                fields=["user", "-start_time"], name="ai_coach_se_user_id_529ad0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aicoachsession",
            index=models.Index(
                fields=["session_type", "-start_time"],
                name="ai_coach_se_session_4ac483_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aicoachsession",
            index=models.Index(fields=["status"], name="ai_coach_se_status_272ca7_idx"),
        ),
        migrations.AddIndex(
            model_name="aicoachsession",
            index=models.Index(
                fields=["user", "status"], name="ai_coach_se_user_id_497be1_idx"
            ),
        ),
    ]
