"""
Tests for AI Coaching Models

Comprehensive test suite for all AI coaching models including
session management, recommendations, chat messages, and personalization.
"""
import pytest
from decimal import Decimal
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>

from ..models import (
    AICoachSession, SessionType, SessionStatus,
    AIRecommendation, RecommendationType, RecommendationPriority, RecommendationStatus,
    ChatMessage, MessageSender, MessageType,
    PersonalizationData, DataType, DataSource
)

User = get_user_model()


class AICoachSessionModelTest(TestCase):
    """Test AICoachSession model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_session_creation(self):
        """Test creating an AI coach session"""
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.NUTRITION_COACHING,
            ai_model_version='gpt-4-v1.0'
        )
        
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.session_type, SessionType.NUTRITION_COACHING)
        self.assertEqual(session.status, SessionStatus.ACTIVE)
        self.assertIsNotNone(session.start_time)
        self.assertIsNone(session.end_time)
        self.assertTrue(session.is_active)
    
    def test_session_end(self):
        """Test ending a session"""
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.GENERAL_COACHING
        )
        
        summary = "Great session covering nutrition basics"
        session.end_session(summary=summary)
        
        self.assertEqual(session.status, SessionStatus.COMPLETED)
        self.assertEqual(session.summary, summary)
        self.assertIsNotNone(session.end_time)
        self.assertFalse(session.is_active)
        self.assertGreater(session.session_duration_minutes, 0)
    
    def test_session_duration_calculation(self):
        """Test session duration calculation"""
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.MEAL_PLANNING
        )
        
        # Simulate session lasting 30 minutes
        session.start_time = timezone.now() - timedelta(minutes=30)
        session.end_session("Test session completed")
        
        self.assertEqual(session.session_duration_minutes, 30)
        self.assertEqual(session.duration_hours, 0.5)
    
    def test_session_str_representation(self):
        """Test string representation of session"""
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.FITNESS_PLANNING
        )
        
        expected = f"Fitness Planning session for {self.user.email}"
        self.assertEqual(str(session), expected)


class AIRecommendationModelTest(TestCase):
    """Test AIRecommendation model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.NUTRITION_COACHING
        )
    
    def test_recommendation_creation(self):
        """Test creating a recommendation"""
        recommendation = AIRecommendation.objects.create(
            user=self.user,
            session=self.session,
            recommendation_type=RecommendationType.MEAL_PLAN,
            title="7-Day Carnivore Meal Plan",
            content="A comprehensive meal plan for the week...",
            confidence_score=Decimal('0.85'),
            priority=RecommendationPriority.HIGH
        )
        
        self.assertEqual(recommendation.user, self.user)
        self.assertEqual(recommendation.session, self.session)
        self.assertEqual(recommendation.status, RecommendationStatus.PENDING)
        self.assertEqual(recommendation.confidence_score, Decimal('0.85'))
        self.assertTrue(recommendation.is_high_confidence)
        self.assertFalse(recommendation.is_expired)
    
    def test_recommendation_status_transitions(self):
        """Test recommendation status transitions"""
        recommendation = AIRecommendation.objects.create(
            user=self.user,
            recommendation_type=RecommendationType.HEALTH_TIP,
            title="Stay Hydrated",
            content="Drink plenty of water throughout the day"
        )
        
        # Test viewing
        recommendation.mark_viewed()
        self.assertEqual(recommendation.status, RecommendationStatus.VIEWED)
        self.assertIsNotNone(recommendation.viewed_at)
        
        # Test acceptance
        recommendation.mark_accepted()
        self.assertEqual(recommendation.status, RecommendationStatus.ACCEPTED)
        self.assertIsNotNone(recommendation.accepted_at)
        
        # Test completion
        recommendation.mark_completed()
        self.assertEqual(recommendation.status, RecommendationStatus.COMPLETED)
        self.assertIsNotNone(recommendation.completed_at)
    
    def test_recommendation_decline(self):
        """Test declining a recommendation"""
        recommendation = AIRecommendation.objects.create(
            user=self.user,
            recommendation_type=RecommendationType.EXERCISE,
            title="Morning Workout",
            content="Start your day with 20 minutes of exercise"
        )
        
        feedback = "Not suitable for my schedule"
        recommendation.mark_declined(feedback)
        
        self.assertEqual(recommendation.status, RecommendationStatus.DECLINED)
        self.assertEqual(recommendation.user_feedback, feedback)
    
    def test_recommendation_expiration(self):
        """Test recommendation expiration"""
        # Create expired recommendation
        recommendation = AIRecommendation.objects.create(
            user=self.user,
            recommendation_type=RecommendationType.RECIPE,
            title="Quick Steak Recipe",
            content="Pan-seared ribeye steak",
            expires_at=timezone.now() - timedelta(days=1)
        )
        
        self.assertTrue(recommendation.is_expired)
    
    def test_recommendation_str_representation(self):
        """Test string representation of recommendation"""
        recommendation = AIRecommendation.objects.create(
            user=self.user,
            recommendation_type=RecommendationType.CARNIVORE_ADVICE,
            title="Start with Quality Meat",
            content="Choose grass-fed beef when possible"
        )
        
        expected = f"Carnivore Diet Advice: Start with Quality Meat"
        self.assertEqual(str(recommendation), expected)


class ChatMessageModelTest(TestCase):
    """Test ChatMessage model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.GENERAL_COACHING
        )
    
    def test_user_message_creation(self):
        """Test creating a user message"""
        message = ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.USER,
            message="Hello, I need help with meal planning",
            message_type=MessageType.QUESTION
        )
        
        self.assertEqual(message.session, self.session)
        self.assertEqual(message.sender, MessageSender.USER)
        self.assertTrue(message.is_from_user)
        self.assertFalse(message.is_from_ai)
        self.assertIsNotNone(message.timestamp)
    
    def test_ai_message_creation(self):
        """Test creating an AI message"""
        message = ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.AI,
            message="I'd be happy to help you with meal planning!",
            message_type=MessageType.TEXT,
            confidence_score=Decimal('0.90'),
            ai_model_version='gpt-4-turbo'
        )
        
        self.assertEqual(message.sender, MessageSender.AI)
        self.assertTrue(message.is_from_ai)
        self.assertFalse(message.is_from_user)
        self.assertEqual(message.confidence_score, Decimal('0.90'))
        self.assertEqual(message.ai_model_version, 'gpt-4-turbo')
    
    def test_message_reply_functionality(self):
        """Test reply functionality"""
        parent_message = ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.USER,
            message="What should I eat for breakfast?",
            message_type=MessageType.QUESTION
        )
        
        reply = ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.AI,
            message="I recommend eggs and bacon for a carnivore breakfast",
            message_type=MessageType.RECOMMENDATION,
            parent_message=parent_message
        )
        
        self.assertEqual(reply.parent_message, parent_message)
        self.assertTrue(parent_message.has_replies)
        self.assertEqual(parent_message.replies.count(), 1)
    
    def test_message_flagging(self):
        """Test message flagging functionality"""
        message = ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.USER,
            message="Inappropriate content here",
            message_type=MessageType.TEXT
        )
        
        reason = "Inappropriate content detected"
        message.flag_message(reason)
        
        self.assertTrue(message.is_flagged)
        self.assertEqual(message.flag_reason, reason)
    
    def test_message_str_representation(self):
        """Test string representation of message"""
        message = ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.USER,
            message="Hello world",
            message_type=MessageType.TEXT
        )
        
        expected = f"User message in session {self.session.id}"
        self.assertEqual(str(message), expected)


class PersonalizationDataModelTest(TestCase):
    """Test PersonalizationData model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_preference_data_creation(self):
        """Test creating preference data"""
        data = PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.PREFERENCE,
            key='favorite_protein',
            value='beef',
            weight=Decimal('1.5'),
            source=DataSource.USER_INPUT
        )
        
        self.assertEqual(data.user, self.user)
        self.assertEqual(data.data_type, DataType.PREFERENCE)
        self.assertEqual(data.key, 'favorite_protein')
        self.assertEqual(data.value, 'beef')
        self.assertEqual(data.get_parsed_value(), 'beef')
        self.assertTrue(data.is_active)
    
    def test_json_value_handling(self):
        """Test JSON value storage and parsing"""
        json_data = {'proteins': ['beef', 'pork'], 'frequency': 'daily'}
        import json
        
        data = PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.BEHAVIOR,
            key='eating_patterns',
            value=json.dumps(json_data),
            source=DataSource.BEHAVIORAL_ANALYSIS
        )
        
        parsed_value = data.get_parsed_value()
        self.assertEqual(parsed_value, json_data)
        self.assertEqual(parsed_value['proteins'], ['beef', 'pork'])
    
    def test_usage_tracking(self):
        """Test usage count increment"""
        data = PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.PREFERENCE,
            key='meal_timing',
            value='morning_person'
        )
        
        initial_count = data.usage_count
        data.increment_usage()
        
        self.assertEqual(data.usage_count, initial_count + 1)
        self.assertTrue(data.last_used > data.created)
    
    def test_data_verification(self):
        """Test data verification functionality"""
        data = PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.GOAL,
            key='target_weight',
            value='180'
        )
        
        self.assertFalse(data.is_verified)
        
        data.verify_data("Confirmed by user")
        
        self.assertTrue(data.is_verified)
        self.assertEqual(data.verification_notes, "Confirmed by user")
    
    def test_data_deactivation(self):
        """Test data deactivation"""
        data = PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.RESTRICTION,
            key='food_allergy',
            value='dairy'
        )
        
        self.assertTrue(data.is_active)
        
        data.deactivate("No longer applicable")
        
        self.assertFalse(data.is_active)
        self.assertEqual(data.deactivation_reason, "No longer applicable")
    
    def test_data_str_representation(self):
        """Test string representation of personalization data"""
        data = PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.PREFERENCE,
            key='cooking_style',
            value='simple'
        )
        
        expected = f"{self.user.email} - Preference: cooking_style"
        self.assertEqual(str(data), expected)


@pytest.mark.django_db
class TestModelRelationships:
    """Test model relationships and foreign key constraints"""
    
    def test_session_user_relationship(self):
        """Test session-user relationship"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        session = AICoachSession.objects.create(
            user=user,
            session_type=SessionType.CARNIVORE_GUIDANCE
        )
        
        # Test forward relationship
        assert session.user == user
        
        # Test reverse relationship
        assert session in user.ai_coach_sessions.all()
    
    def test_recommendation_session_relationship(self):
        """Test recommendation-session relationship"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        session = AICoachSession.objects.create(
            user=user,
            session_type=SessionType.MEAL_PLANNING
        )
        
        recommendation = AIRecommendation.objects.create(
            user=user,
            session=session,
            recommendation_type=RecommendationType.MEAL_PLAN,
            title="Test Meal Plan",
            content="Test content"
        )
        
        # Test forward relationship
        assert recommendation.session == session
        assert recommendation.user == user
        
        # Test reverse relationship
        assert recommendation in session.recommendations.all()
    
    def test_chat_message_relationships(self):
        """Test chat message relationships"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        session = AICoachSession.objects.create(
            user=user,
            session_type=SessionType.GENERAL_COACHING
        )
        
        parent_message = ChatMessage.objects.create(
            session=session,
            sender=MessageSender.USER,
            message="Parent message",
            message_type=MessageType.TEXT
        )
        
        reply_message = ChatMessage.objects.create(
            session=session,
            sender=MessageSender.AI,
            message="Reply message",
            message_type=MessageType.TEXT,
            parent_message=parent_message
        )
        
        # Test parent-child relationship
        assert reply_message.parent_message == parent_message
        assert reply_message in parent_message.replies.all()
        
        # Test session relationship
        assert parent_message.session == session
        assert reply_message.session == session
        assert parent_message in session.chat_messages.all()
        assert reply_message in session.chat_messages.all()


@pytest.mark.django_db
class TestModelValidation:
    """Test model validation and constraints"""
    
    def test_confidence_score_validation(self):
        """Test confidence score validation"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test valid confidence score
        recommendation = AIRecommendation.objects.create(
            user=user,
            recommendation_type=RecommendationType.HEALTH_TIP,
            title="Valid Tip",
            content="Content",
            confidence_score=Decimal('0.85')
        )
        assert recommendation.confidence_score == Decimal('0.85')
        
        # Test edge cases
        recommendation.confidence_score = Decimal('0.00')
        recommendation.save()
        
        recommendation.confidence_score = Decimal('1.00')
        recommendation.save()
    
    def test_user_satisfaction_score_validation(self):
        """Test user satisfaction score validation"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        session = AICoachSession.objects.create(
            user=user,
            session_type=SessionType.FITNESS_PLANNING
        )
        
        # Test valid satisfaction scores
        for score in [1, 2, 3, 4, 5]:
            session.user_satisfaction_score = score
            session.save()
            assert session.user_satisfaction_score == score
    
    def test_weight_validation(self):
        """Test weight field validation"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        data = PersonalizationData.objects.create(
            user=user,
            data_type=DataType.PREFERENCE,
            key='test_key',
            value='test_value',
            weight=Decimal('2.5')
        )
        
        assert data.weight == Decimal('2.5')
        
        # Test edge cases
        data.weight = Decimal('0.0')
        data.save()
        
        data.weight = Decimal('5.0')
        data.save() 