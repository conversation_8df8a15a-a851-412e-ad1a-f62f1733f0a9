"""
Tests for AI Coaching Services

Comprehensive test suite for all AI coaching services including
AI coach service, recommendation engine, chatbot, and personalization.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from decimal import Decimal
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

from ..models import (
    AICoachSession, SessionType, SessionStatus,
    AIRecommendation, RecommendationType,
    ChatMessage, MessageSender, MessageType,
    PersonalizationData, DataType
)
from ..services import (
    AICoachService,
    RecommendationEngineService,
    ChatBotService,
    PersonalizationService,
    MealPlanAIService
)

User = get_user_model()


class AICoachServiceTest(TestCase):
    """Test AI Coach Service functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.ai_coach_service = AICoachService()
    
    def test_start_coaching_session(self):
        """Test starting a new coaching session"""
        session = self.ai_coach_service.start_coaching_session(
            user=self.user,
            session_type=SessionType.NUTRITION_COACHING,
            initial_message="Hello, I need nutrition help"
        )
        
        self.assertIsInstance(session, AICoachSession)
        self.assertEqual(session.user, self.user)
        self.assertEqual(session.session_type, SessionType.NUTRITION_COACHING)
        self.assertEqual(session.status, SessionStatus.ACTIVE)
        
        # Check that initial messages were created
        messages = session.chat_messages.all()
        self.assertGreater(messages.count(), 0)
    
    def test_end_active_sessions_before_new(self):
        """Test that active sessions are ended before starting new ones"""
        # Create an active session
        old_session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.GENERAL_COACHING,
            status=SessionStatus.ACTIVE
        )
        
        # Start a new session
        new_session = self.ai_coach_service.start_coaching_session(
            user=self.user,
            session_type=SessionType.MEAL_PLANNING
        )
        
        # Check that old session was ended
        old_session.refresh_from_db()
        self.assertEqual(old_session.status, SessionStatus.COMPLETED)
        
        # Check that new session is active
        self.assertEqual(new_session.status, SessionStatus.ACTIVE)
    
    @patch('apps.ai_coaching.services.ChatBotService.generate_response')
    def test_process_user_message(self, mock_generate_response):
        """Test processing user messages"""
        # Setup mock response
        mock_generate_response.return_value = {
            'message': 'Great question! Here are some carnivore meal ideas...',
            'type': 'recommendation',
            'confidence': Decimal('0.85'),
            'should_recommend': True,
            'context': {'type': 'meal_planning'}
        }
        
        # Create active session
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.MEAL_PLANNING,
            status=SessionStatus.ACTIVE
        )
        
        # Process user message
        result = self.ai_coach_service.process_user_message(
            session=session,
            message="What should I eat for lunch?"
        )
        
        self.assertIsNotNone(result['ai_message'])
        self.assertIn('Great question!', result['ai_response_text'])
        self.assertEqual(result['confidence'], Decimal('0.85'))
        self.assertTrue(result['should_recommend'])
    
    def test_end_coaching_session(self):
        """Test ending a coaching session"""
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.FITNESS_PLANNING,
            status=SessionStatus.ACTIVE
        )
        
        # Add some test messages
        ChatMessage.objects.create(
            session=session,
            sender=MessageSender.USER,
            message="Test message",
            message_type=MessageType.TEXT
        )
        
        result = self.ai_coach_service.end_coaching_session(
            session=session,
            user_feedback="Great session!",
            satisfaction_score=5
        )
        
        session.refresh_from_db()
        self.assertEqual(session.status, SessionStatus.COMPLETED)
        self.assertEqual(session.user_satisfaction_score, 5)
        self.assertIsNotNone(session.summary)
        self.assertIsNotNone(result['summary'])
    
    def test_get_user_coaching_stats(self):
        """Test getting user coaching statistics"""
        # Create test sessions
        session1 = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.NUTRITION_COACHING,
            status=SessionStatus.COMPLETED,
            user_satisfaction_score=4
        )
        
        session2 = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.MEAL_PLANNING,
            status=SessionStatus.COMPLETED,
            user_satisfaction_score=5
        )
        
        stats = self.ai_coach_service.get_user_coaching_stats(self.user)
        
        self.assertEqual(stats['total_sessions'], 2)
        self.assertEqual(stats['completed_sessions'], 2)
        self.assertEqual(stats['average_satisfaction'], 4.5)


class RecommendationEngineServiceTest(TestCase):
    """Test Recommendation Engine Service functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.recommendation_service = RecommendationEngineService()
    
    def test_generate_contextual_recommendations(self):
        """Test generating contextual recommendations"""
        recommendations = self.recommendation_service.generate_contextual_recommendations(
            user=self.user,
            context={'meal_planning': True},
            limit=3
        )
        
        self.assertIsInstance(recommendations, list)
        self.assertLessEqual(len(recommendations), 3)
        
        for rec in recommendations:
            self.assertIsInstance(rec, AIRecommendation)
            self.assertEqual(rec.user, self.user)
    
    def test_generate_meal_plan_recommendations(self):
        """Test generating meal plan recommendations"""
        recommendations = self.recommendation_service.generate_meal_plan_recommendations(
            user=self.user,
            days=7,
            preferences={'protein_preference': ['beef', 'pork']}
        )
        
        self.assertIsInstance(recommendations, list)
        self.assertEqual(len(recommendations), 7)  # One per day
        
        for rec in recommendations:
            self.assertEqual(rec.recommendation_type, RecommendationType.MEAL_PLAN)
            self.assertEqual(rec.user, self.user)
    
    def test_generate_recipe_recommendations(self):
        """Test generating recipe recommendations"""
        recommendations = self.recommendation_service.generate_recipe_recommendations(
            user=self.user,
            dietary_restrictions=['dairy'],
            cuisine_preferences=['american'],
            limit=5
        )
        
        self.assertIsInstance(recommendations, list)
        self.assertLessEqual(len(recommendations), 5)
        
        for rec in recommendations:
            self.assertEqual(rec.recommendation_type, RecommendationType.RECIPE)
            self.assertEqual(rec.user, self.user)
    
    def test_generate_health_tips(self):
        """Test generating health tips"""
        recommendations = self.recommendation_service.generate_health_tips(
            user=self.user,
            focus_areas=['hydration', 'sleep'],
            limit=3
        )
        
        self.assertIsInstance(recommendations, list)
        self.assertLessEqual(len(recommendations), 3)
        
        for rec in recommendations:
            self.assertEqual(rec.recommendation_type, RecommendationType.HEALTH_TIP)
            self.assertEqual(rec.user, self.user)


class ChatBotServiceTest(TestCase):
    """Test Chat Bot Service functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.GENERAL_COACHING
        )
        self.chatbot_service = ChatBotService()
    
    def test_create_user_message(self):
        """Test creating a user message"""
        message = self.chatbot_service.create_message(
            session=self.session,
            sender=MessageSender.USER,
            message="Hello, I need help with my diet",
            message_type=MessageType.QUESTION
        )
        
        self.assertIsInstance(message, ChatMessage)
        self.assertEqual(message.session, self.session)
        self.assertEqual(message.sender, MessageSender.USER)
        self.assertEqual(message.message_type, MessageType.QUESTION)
        self.assertIsNotNone(message.intent_detected)
    
    def test_create_ai_message(self):
        """Test creating an AI message"""
        message = self.chatbot_service.create_message(
            session=self.session,
            sender=MessageSender.AI,
            message="I'd be happy to help you with your diet!",
            message_type=MessageType.TEXT,
            confidence_score=Decimal('0.90')
        )
        
        self.assertEqual(message.sender, MessageSender.AI)
        self.assertEqual(message.confidence_score, Decimal('0.90'))
        self.assertIsNotNone(message.ai_model_version)
    
    def test_generate_response(self):
        """Test generating AI response"""
        response = self.chatbot_service.generate_response(
            session=self.session,
            user_message="What should I eat for breakfast?",
            user_profile={'dietary_preferences': 'carnivore'}
        )
        
        self.assertIsInstance(response, dict)
        self.assertIn('message', response)
        self.assertIn('type', response)
        self.assertIn('confidence', response)
    
    def test_message_analysis(self):
        """Test user message analysis"""
        analysis = self.chatbot_service._analyze_user_message(
            "I love eating steak and bacon but hate vegetables"
        )
        
        self.assertIn('intent', analysis)
        self.assertIn('entities', analysis)
        self.assertIn('sentiment', analysis)
        self.assertIsInstance(analysis['entities'], list)
    
    def test_conversation_summary(self):
        """Test conversation summary generation"""
        # Create test messages
        ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.USER,
            message="Hello",
            message_type=MessageType.TEXT
        )
        
        ChatMessage.objects.create(
            session=self.session,
            sender=MessageSender.AI,
            message="Hi there! How can I help?",
            message_type=MessageType.TEXT
        )
        
        summary = self.chatbot_service.get_conversation_summary(self.session)
        
        self.assertIn('total_messages', summary)
        self.assertIn('user_messages', summary)
        self.assertIn('ai_messages', summary)
        self.assertEqual(summary['total_messages'], 2)
        self.assertEqual(summary['user_messages'], 1)
        self.assertEqual(summary['ai_messages'], 1)


class PersonalizationServiceTest(TestCase):
    """Test Personalization Service functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.personalization_service = PersonalizationService()
    
    def test_get_user_profile(self):
        """Test getting user profile"""
        # Create test personalization data
        PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.PREFERENCE,
            key='favorite_protein',
            value='beef'
        )
        
        PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.GOAL,
            key='weight_loss',
            value='lose_10_pounds'
        )
        
        profile = self.personalization_service.get_user_profile(self.user)
        
        self.assertIsInstance(profile, dict)
        self.assertIn('preferences', profile)
        self.assertIn('goals', profile)
        self.assertEqual(profile['preferences']['favorite_protein'], 'beef')
        self.assertIn('lose_10_pounds', profile['goals'])
    
    def test_update_user_preference(self):
        """Test updating user preferences"""
        preference = self.personalization_service.update_user_preference(
            user=self.user,
            preference_key='cooking_style',
            preference_value='simple',
            weight=Decimal('1.5')
        )
        
        self.assertIsInstance(preference, PersonalizationData)
        self.assertEqual(preference.key, 'cooking_style')
        self.assertEqual(preference.get_parsed_value(), 'simple')
        self.assertEqual(preference.weight, Decimal('1.5'))
    
    def test_learn_from_feedback(self):
        """Test learning from user feedback"""
        self.personalization_service.learn_from_feedback(
            user=self.user,
            recommendation_type='meal_plan',
            feedback_type='positive',
            feedback_data={'protein_type': 'beef', 'meal_type': 'breakfast'},
            rating=5
        )
        
        # Check that feedback pattern was created
        feedback_data = PersonalizationData.objects.filter(
            user=self.user,
            data_type=DataType.FEEDBACK_PATTERN
        ).first()
        
        self.assertIsNotNone(feedback_data)
        self.assertIn('meal_plan_feedback', feedback_data.key)
    
    def test_update_from_conversation(self):
        """Test updating personalization from conversation"""
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=SessionType.NUTRITION_COACHING
        )
        
        ai_response = {
            'message': 'Great choice!',
            'type': 'text',
            'confidence': Decimal('0.85')
        }
        
        self.personalization_service.update_from_conversation(
            user=self.user,
            user_message="I love ribeye steak",
            ai_response=ai_response,
            session_type=SessionType.NUTRITION_COACHING
        )
        
        # Check that preferences were extracted
        preferences = PersonalizationData.objects.filter(
            user=self.user,
            data_type=DataType.PREFERENCE
        )
        
        self.assertGreater(preferences.count(), 0)
    
    def test_get_personalization_insights(self):
        """Test getting personalization insights"""
        # Create test data
        PersonalizationData.objects.create(
            user=self.user,
            data_type=DataType.PREFERENCE,
            key='test_pref',
            value='test_value',
            usage_count=5,
            is_verified=True
        )
        
        insights = self.personalization_service.get_personalization_insights(self.user)
        
        self.assertIsInstance(insights, dict)
        self.assertIn('total_data_points', insights)
        self.assertIn('personalization_score', insights)
        self.assertIn('most_used_preferences', insights)
        self.assertEqual(insights['total_data_points'], 1)


class MealPlanAIServiceTest(TestCase):
    """Test Meal Plan AI Service functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.meal_plan_service = MealPlanAIService()
    
    def test_generate_weekly_meal_plan(self):
        """Test generating weekly meal plan"""
        meal_plans = self.meal_plan_service.generate_weekly_meal_plan(
            user=self.user,
            preferences={'protein_preference': ['beef', 'pork']},
            calorie_target=2000,
            meal_count_per_day=2
        )
        
        self.assertIsInstance(meal_plans, list)
        self.assertEqual(len(meal_plans), 7)  # 7 days
        
        for plan in meal_plans:
            self.assertIsInstance(plan, AIRecommendation)
            self.assertEqual(plan.recommendation_type, RecommendationType.MEAL_PLAN)
            self.assertEqual(plan.user, self.user)
    
    def test_generate_daily_meal_plan(self):
        """Test generating daily meal plan"""
        meal_plan = self.meal_plan_service.generate_daily_meal_plan(
            user=self.user,
            date=timezone.now(),
            meal_count=3
        )
        
        self.assertIsInstance(meal_plan, AIRecommendation)
        self.assertEqual(meal_plan.recommendation_type, RecommendationType.MEAL_PLAN)
        self.assertIn('meal_count', meal_plan.metadata)
        self.assertEqual(meal_plan.metadata['meal_count'], 3)
    
    def test_generate_meal_suggestions(self):
        """Test generating meal suggestions"""
        suggestions = self.meal_plan_service.generate_meal_suggestions(
            user=self.user,
            meal_type='breakfast',
            protein_preference='beef',
            cooking_time_limit=30,
            limit=3
        )
        
        self.assertIsInstance(suggestions, list)
        self.assertLessEqual(len(suggestions), 3)
        
        for suggestion in suggestions:
            self.assertIsInstance(suggestion, AIRecommendation)
            self.assertEqual(suggestion.user, self.user)
    
    def test_optimize_meal_plan_for_goals(self):
        """Test optimizing meal plan for health goals"""
        # Create a sample meal plan
        current_plan = {
            'plans': [
                {
                    'id': 'test-id',
                    'title': 'Test Meal',
                    'content': 'Test content',
                    'metadata': {'calories': 500}
                }
            ]
        }
        
        optimization = self.meal_plan_service.optimize_meal_plan_for_goals(
            user=self.user,
            health_goals=['weight_loss', 'muscle_gain'],
            current_meal_plan=current_plan
        )
        
        self.assertIsInstance(optimization, dict)
        self.assertIn('strategies_applied', optimization)
        self.assertIn('recommendations', optimization)


@pytest.mark.django_db
class TestServiceIntegration:
    """Test integration between different services"""
    
    def test_ai_coach_service_integration(self):
        """Test integration between AI coach and other services"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        ai_coach_service = AICoachService()
        
        # Start session
        session = ai_coach_service.start_coaching_session(
            user=user,
            session_type=SessionType.MEAL_PLANNING,
            initial_message="I need help with meal planning"
        )
        
        # Process message (should involve chatbot and recommendation services)
        result = ai_coach_service.process_user_message(
            session=session,
            message="What should I eat for dinner?"
        )
        
        # Verify integration worked
        assert session.chat_messages.count() >= 2  # User + AI messages
        assert result['ai_message'] is not None
    
    def test_recommendation_personalization_integration(self):
        """Test integration between recommendation engine and personalization"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Add personalization data
        PersonalizationData.objects.create(
            user=user,
            data_type=DataType.PREFERENCE,
            key='favorite_protein',
            value='beef'
        )
        
        recommendation_service = RecommendationEngineService()
        
        # Generate recommendations (should use personalization data)
        recommendations = recommendation_service.generate_contextual_recommendations(
            user=user,
            limit=3
        )
        
        assert len(recommendations) > 0
        for rec in recommendations:
            assert rec.user == user


@pytest.mark.django_db
class TestServiceErrorHandling:
    """Test error handling in services"""
    
    def test_ai_coach_service_invalid_session(self):
        """Test AI coach service with invalid session"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create completed session
        session = AICoachSession.objects.create(
            user=user,
            session_type=SessionType.GENERAL_COACHING,
            status=SessionStatus.COMPLETED
        )
        
        ai_coach_service = AICoachService()
        
        # Should raise error for non-active session
        with pytest.raises(ValueError):
            ai_coach_service.process_user_message(
                session=session,
                message="Test message"
            )
    
    def test_recommendation_service_empty_results(self):
        """Test recommendation service with no results"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        recommendation_service = RecommendationEngineService()
        
        # Request recommendations with very restrictive criteria
        recommendations = recommendation_service.generate_contextual_recommendations(
            user=user,
            recommendation_types=[],  # Empty types
            limit=0  # Zero limit
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) == 0


@pytest.mark.django_db
class TestServicePerformance:
    """Test service performance characteristics"""
    
    def test_bulk_recommendation_generation(self):
        """Test generating many recommendations efficiently"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        recommendation_service = RecommendationEngineService()
        
        # Generate many recommendations
        recommendations = recommendation_service.generate_meal_plan_recommendations(
            user=user,
            days=30,  # Large number
            preferences={}
        )
        
        assert len(recommendations) == 30
        
        # Verify all were created efficiently
        db_recommendations = AIRecommendation.objects.filter(user=user)
        assert db_recommendations.count() == 30
    
    def test_personalization_data_retrieval(self):
        """Test efficient personalization data retrieval"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create many personalization data points
        for i in range(50):
            PersonalizationData.objects.create(
                user=user,
                data_type=DataType.PREFERENCE,
                key=f'pref_{i}',
                value=f'value_{i}'
            )
        
        personalization_service = PersonalizationService()
        
        # Should efficiently retrieve and process all data
        profile = personalization_service.get_user_profile(user)
        
        assert len(profile['preferences']) == 50
        assert 'pref_0' in profile['preferences']
        assert 'pref_49' in profile['preferences'] 