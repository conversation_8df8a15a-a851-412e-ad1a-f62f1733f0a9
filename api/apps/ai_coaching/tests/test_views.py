import json
from datetime import datetime, timedelta
from decimal import Decimal
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock

from ..models import (
    AIRecommendation,
    AICoachSession,
    PersonalizationData,
    RecommendationType,
)
from ..serializers import (
    AIRecommendationListSerializer,
    AICoachSessionListSerializer,
    MealPlanRequestSerializer,
)

User = get_user_model()


class HealthInsightsViewTest(TestCase):
    """Tests for HealthInsightsView."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        self.session = AICoachSession.objects.create(
            user=self.user,
            session_type=AICoachSession.SessionType.GENERAL_COACHING,
            start_time=timezone.now() - timedelta(hours=1),
            end_time=timezone.now(),
            status=AICoachSession.Status.COMPLETED,
            user_satisfaction=4,
            notes="Great session on carnivore adaptation",
        )

        self.recommendation = AIRecommendation.objects.create(
            user=self.user,
            recommendation_type=RecommendationType.NUTRITION,
            title="Increase protein intake",
            content="Consider adding more beef to your meals",
            confidence_score=Decimal("0.85"),
            priority=AIRecommendation.Priority.MEDIUM,
            status=AIRecommendation.Status.ACTIVE,
        )

        self.personalization_data = PersonalizationData.objects.create(
            user=self.user,
            carnivore_experience_level="intermediate",
            health_goals=["weight_loss", "energy"],
            dietary_restrictions=[],
            preferred_meal_timing="intermittent_fasting",
        )

    def test_get_health_insights(self):
        """Test getting comprehensive health insights."""
        with patch("apps.ai_coaching.services.PersonalizationService") as mock_service:
            mock_service.return_value.get_user_profile.return_value = {
                "goals": ["weight_loss"],
                "preferences": {"meal_timing": "intermittent_fasting"},
                "experience_level": "intermediate",
            }

            url = reverse("ai_coaching:health-insights")
            response = self.client.get(url)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            data = response.data

            
            self.assertIn("overview", data)
            self.assertIn("carnivore_journey", data)
            self.assertIn("progress_analysis", data)
            self.assertIn("recommendation_patterns", data)
            self.assertIn("personalization_score", data)
            self.assertIn("health_trends", data)
            self.assertIn("actionable_insights", data)
            self.assertIn("generated_at", data)

            
            overview = data["overview"]
            self.assertIn("total_coaching_sessions", overview)
            self.assertIn("current_activity_level", overview)
            self.assertIn("summary", overview)

    def test_post_targeted_health_insights(self):
        """Test generating targeted health insights."""
        with patch(
            "apps.ai_coaching.services.RecommendationEngineService"
        ) as mock_service:
            mock_rec = MagicMock()
            mock_rec.id = "test-id"
            mock_rec.title = "Test Recommendation"
            mock_rec.content = "Test content"
            mock_rec.confidence_score = Decimal("0.9")
            mock_rec.priority = "high"

            mock_service.return_value.generate_health_tips.return_value = [mock_rec]

            url = reverse("ai_coaching:health-insights")
            data = {
                "focus_areas": ["nutrition", "adaptation"],
                "time_period": 14,
                "include_recommendations": True,
            }

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            result = response.data

            
            self.assertEqual(result["time_period_days"], 14)
            self.assertEqual(result["focus_areas"], ["nutrition", "adaptation"])
            self.assertIn("insights", result)
            self.assertIn("new_recommendations", result)

            
            insights = result["insights"]
            self.assertIn("nutrition", insights)
            self.assertIn("adaptation", insights)

    def test_health_insights_service_error(self):
        """Test health insights with service error."""
        with patch("apps.ai_coaching.services.PersonalizationService") as mock_service:
            mock_service.side_effect = Exception("Service unavailable")

            url = reverse("ai_coaching:health-insights")
            response = self.client.get(url)

            self.assertEqual(
                response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            self.assertIn("Failed to generate health insights", response.data["error"])

    def test_health_insights_unauthenticated(self):
        """Test that unauthenticated users cannot access health insights."""
        self.client.force_authenticate(user=None)

        url = reverse("ai_coaching:health-insights")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class PersonalizedMealPlanViewTest(TestCase):
    """Tests for PersonalizedMealPlanView."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        self.personalization_data = PersonalizationData.objects.create(
            user=self.user,
            carnivore_experience_level="intermediate",
            health_goals=["muscle_gain"],
            dietary_restrictions=[],
            preferred_meal_timing="traditional",
        )

    def test_generate_weekly_meal_plan(self):
        """Test generating a weekly meal plan."""
        with patch("apps.ai_coaching.services.MealPlanAIService") as mock_service:
            mock_plan = {
                "week_start": "2024-01-01",
                "week_end": "2024-01-07",
                "daily_plans": [
                    {
                        "date": "2024-01-01",
                        "meals": [
                            {
                                "meal_type": "breakfast",
                                "foods": ["ribeye steak", "eggs"],
                                "calories": 600,
                                "protein": 45,
                            }
                        ],
                    }
                ],
                "weekly_totals": {"calories": 14000, "protein": 1050},
                "recommendations": ["Focus on organ meats for nutrients"],
            }

            mock_service.return_value.generate_weekly_plan.return_value = mock_plan

            url = reverse("ai_coaching:meal-plan-weekly")
            data = {
                "start_date": "2024-01-01",
                "target_calories": 2000,
                "dietary_preferences": ["grass_fed_beef"],
                "meal_frequency": 2,
            }

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            result = response.data

            
            self.assertIn("meal_plan", result)
            self.assertIn("nutritional_analysis", result)
            self.assertIn("ai_recommendations", result)

            meal_plan = result["meal_plan"]
            self.assertIn("daily_plans", meal_plan)
            self.assertIn("weekly_totals", meal_plan)

    def test_generate_daily_meal_plan(self):
        """Test generating a daily meal plan."""
        with patch("apps.ai_coaching.services.MealPlanAIService") as mock_service:
            mock_plan = {
                "date": "2024-01-01",
                "meals": [
                    {
                        "meal_type": "breakfast",
                        "foods": ["ground beef"],
                        "calories": 400,
                        "protein": 30,
                    },
                    {
                        "meal_type": "dinner",
                        "foods": ["salmon"],
                        "calories": 300,
                        "protein": 25,
                    },
                ],
                "daily_totals": {"calories": 700, "protein": 55},
                "recommendations": ["Consider adding organ meats"],
            }

            mock_service.return_value.generate_daily_plan.return_value = mock_plan

            url = reverse("ai_coaching:meal-plan-daily")
            data = {
                "target_date": "2024-01-01",
                "target_calories": 1800,
                "meal_frequency": 2,
                "activity_level": "moderate",
            }

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            result = response.data

            
            self.assertIn("meal_plan", result)
            self.assertIn("nutritional_breakdown", result)
            self.assertIn("optimization_suggestions", result)

    def test_single_meal_suggestion(self):
        """Test getting single meal suggestions."""
        with patch("apps.ai_coaching.services.MealPlanAIService") as mock_service:
            mock_suggestions = [
                {
                    "meal_type": "lunch",
                    "primary_protein": "ribeye steak",
                    "portion_size": "8oz",
                    "cooking_method": "grilled",
                    "estimated_calories": 600,
                    "estimated_protein": 45,
                    "why_recommended": "High protein for muscle building goals",
                }
            ]

            mock_service.return_value.suggest_single_meal.return_value = (
                mock_suggestions
            )

            url = reverse("ai_coaching:meal-suggestion")
            data = {
                "meal_type": "lunch",
                "target_calories": 600,
                "preferences": ["high_protein"],
                "avoid_foods": [],
            }

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            result = response.data

            
            self.assertIn("suggestions", result)
            self.assertIn("reasoning", result)

            suggestions = result["suggestions"]
            self.assertEqual(len(suggestions), 1)
            self.assertEqual(suggestions[0]["meal_type"], "lunch")

    def test_meal_plan_optimization(self):
        """Test meal plan optimization."""
        with patch("apps.ai_coaching.services.MealPlanAIService") as mock_service:
            mock_optimization = {
                "original_plan_analysis": {
                    "calories": 2000,
                    "protein": 150,
                    "issues_identified": ["low_organ_meat"],
                },
                "optimized_suggestions": [
                    {
                        "type": "add_meal",
                        "meal": "liver",
                        "reason": "Increase micronutrient density",
                    }
                ],
                "improvement_score": 0.85,
            }

            mock_service.return_value.optimize_meal_plan.return_value = (
                mock_optimization
            )

            url = reverse("ai_coaching:meal-plan-optimize")
            data = {
                "current_plan": {"meals": [{"food": "beef", "quantity": 200}]},
                "optimization_goals": ["nutrient_density", "cost_efficiency"],
            }

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            result = response.data

            
            self.assertIn("optimization_analysis", result)
            self.assertIn("recommendations", result)
            self.assertIn("improvement_score", result)

    def test_meal_plan_feedback(self):
        """Test submitting meal plan feedback."""
        url = reverse("ai_coaching:meal-plan-feedback")
        data = {
            "plan_id": "test-plan-123",
            "overall_rating": 4,
            "taste_rating": 5,
            "convenience_rating": 3,
            "adherence_rating": 4,
            "comments": "Great suggestions, but need more variety",
            "followed_completely": False,
            "modifications_made": ["replaced salmon with beef"],
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        result = response.data

        
        self.assertIn("feedback_received", result)
        self.assertIn("learning_points", result)
        self.assertIn("future_improvements", result)

    def test_meal_plan_analytics(self):
        """Test getting meal plan analytics."""
        
        for i in range(3):
            AIRecommendation.objects.create(
                user=self.user,
                recommendation_type=RecommendationType.MEAL_PLANNING,
                title=f"Meal Plan {i}",
                content=f"Content {i}",
                confidence_score=Decimal("0.8"),
                priority=AIRecommendation.Priority.MEDIUM,
                status=AIRecommendation.Status.ACTIVE,
            )

        url = reverse("ai_coaching:meal-plan-analytics")
        response = self.client.get(url, {"period": "last_30_days"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        
        self.assertIn("plan_generation_stats", data)
        self.assertIn("user_feedback_analysis", data)
        self.assertIn("adherence_patterns", data)
        self.assertIn("popular_meal_combinations", data)

    def test_meal_plan_invalid_data(self):
        """Test meal plan generation with invalid data."""
        url = reverse("ai_coaching:meal-plan-weekly")
        data = {
            "start_date": "invalid-date",
            "target_calories": -1000,  
            "meal_frequency": 10,  
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class AIChatViewTest(TestCase):
    """Tests for AI Chat views."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

    def test_create_chat_session(self):
        """Test creating a new AI chat session."""
        with patch("apps.ai_coaching.services.AIChatService") as mock_service:
            mock_response = {
                "session_id": "chat-123",
                "response": "Hello! How can I help you with your carnivore journey today?",
                "context": "general_greeting",
                "suggested_followups": [
                    "Tell me about carnivore adaptation",
                    "Help me plan my meals",
                ],
            }

            mock_service.return_value.start_chat_session.return_value = mock_response

            url = reverse("ai_coaching:ai-chat-create")
            data = {
                "initial_message": "Hello, I need help with carnivore diet",
                "chat_type": "general_support",
                "context": {},
            }

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            result = response.data

            
            self.assertIn("session_id", result)
            self.assertIn("response", result)
            self.assertIn("suggested_followups", result)

    def test_continue_chat_conversation(self):
        """Test continuing an existing chat conversation."""
        
        session = AICoachSession.objects.create(
            user=self.user,
            session_type=AICoachSession.SessionType.CHAT,
            start_time=timezone.now(),
            status=AICoachSession.Status.IN_PROGRESS,
        )

        with patch("apps.ai_coaching.services.AIChatService") as mock_service:
            mock_response = {
                "response": "Great question! The carnivore diet focuses on animal products...",
                "confidence": 0.95,
                "sources": ["carnivore_knowledge_base"],
                "suggested_followups": [
                    "What foods should I start with?",
                    "How long does adaptation take?",
                ],
            }

            mock_service.return_value.continue_conversation.return_value = mock_response

            url = reverse(
                "ai_coaching:ai-chat-continue", kwargs={"session_id": session.id}
            )
            data = {
                "message": "What is the carnivore diet?",
                "context": {"previous_topic": "diet_basics"},
            }

            response = self.client.post(url, data, format="json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            result = response.data

            
            self.assertIn("response", result)
            self.assertIn("confidence", result)
            self.assertIn("suggested_followups", result)

    def test_chat_session_history(self):
        """Test getting chat session history."""
        
        for i in range(3):
            AICoachSession.objects.create(
                user=self.user,
                session_type=AICoachSession.SessionType.CHAT,
                start_time=timezone.now() - timedelta(days=i),
                status=AICoachSession.Status.COMPLETED,
                notes=f"Chat session {i}",
            )

        url = reverse("ai_coaching:ai-chat-history")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        
        self.assertIn("results", data)
        results = data["results"]
        self.assertEqual(len(results), 3)

        
        timestamps = [session["start_time"] for session in results]
        self.assertEqual(timestamps, sorted(timestamps, reverse=True))


class AIRecommendationViewTest(TestCase):
    """Tests for AI Recommendation views."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        self.recommendations = [
            AIRecommendation.objects.create(
                user=self.user,
                recommendation_type=RecommendationType.NUTRITION,
                title="Increase Protein Intake",
                content="Consider adding more red meat to your diet",
                confidence_score=Decimal("0.9"),
                priority=AIRecommendation.Priority.HIGH,
                status=AIRecommendation.Status.ACTIVE,
            ),
            AIRecommendation.objects.create(
                user=self.user,
                recommendation_type=RecommendationType.EXERCISE,
                title="Add Strength Training",
                content="Incorporate resistance training 3x per week",
                confidence_score=Decimal("0.8"),
                priority=AIRecommendation.Priority.MEDIUM,
                status=AIRecommendation.Status.PENDING,
            ),
        ]

    def test_list_recommendations(self):
        """Test listing user's AI recommendations."""
        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 2)

        
        rec_data = results[0]
        self.assertIn("id", rec_data)
        self.assertIn("title", rec_data)
        self.assertIn("recommendation_type", rec_data)
        self.assertIn("confidence_score", rec_data)
        self.assertIn("priority", rec_data)

    def test_filter_recommendations_by_type(self):
        """Test filtering recommendations by type."""
        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(
            url, {"recommendation_type": RecommendationType.NUTRITION}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 1)
        self.assertEqual(
            results[0]["recommendation_type"], RecommendationType.NUTRITION
        )

    def test_filter_recommendations_by_status(self):
        """Test filtering recommendations by status."""
        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(url, {"status": AIRecommendation.Status.ACTIVE})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["status"], AIRecommendation.Status.ACTIVE)

    def test_filter_recommendations_by_priority(self):
        """Test filtering recommendations by priority."""
        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(url, {"priority": AIRecommendation.Priority.HIGH})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["priority"], AIRecommendation.Priority.HIGH)

    def test_search_recommendations(self):
        """Test searching recommendations by content."""
        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(url, {"search": "protein"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 1)
        self.assertIn("protein", results[0]["title"].lower())

    def test_recommendation_ordering(self):
        """Test that recommendations are ordered correctly."""
        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(url, {"ordering": "-created"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        timestamps = [rec["created"] for rec in results]
        self.assertEqual(timestamps, sorted(timestamps, reverse=True))

    def test_recommendations_user_isolation(self):
        """Test that users only see their own recommendations."""
        
        other_user = User.objects.create_user(
            username="otheruser", email="<EMAIL>", password="otherpass123"
        )

        AIRecommendation.objects.create(
            user=other_user,
            recommendation_type=RecommendationType.NUTRITION,
            title="Other User Recommendation",
            content="Content for other user",
            confidence_score=Decimal("0.7"),
            priority=AIRecommendation.Priority.LOW,
            status=AIRecommendation.Status.ACTIVE,
        )

        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 2)
        for result in results:
            self.assertEqual(result["user"], str(self.user.id))


class AICoachSessionViewTest(TestCase):
    """Tests for AI Coach Session views."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        now = timezone.now()
        self.sessions = [
            AICoachSession.objects.create(
                user=self.user,
                session_type=AICoachSession.SessionType.GENERAL_COACHING,
                start_time=now - timedelta(hours=2),
                end_time=now - timedelta(hours=1),
                status=AICoachSession.Status.COMPLETED,
                user_satisfaction=5,
                notes="Excellent session on carnivore basics",
            ),
            AICoachSession.objects.create(
                user=self.user,
                session_type=AICoachSession.SessionType.MEAL_PLANNING,
                start_time=now - timedelta(hours=1),
                status=AICoachSession.Status.IN_PROGRESS,
                notes="Working on meal plan optimization",
            ),
        ]

    def test_list_coach_sessions(self):
        """Test listing coaching sessions."""
        url = reverse("ai_coaching:sessions-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 2)

        
        session_data = results[0]
        self.assertIn("id", session_data)
        self.assertIn("session_type", session_data)
        self.assertIn("status", session_data)
        self.assertIn("start_time", session_data)

    def test_create_coach_session(self):
        """Test creating a new coaching session."""
        url = reverse("ai_coaching:sessions-list")
        data = {
            "session_type": AICoachSession.SessionType.NUTRITION_ANALYSIS,
            "goals": ["improve_energy", "weight_loss"],
            "context": {
                "current_diet": "standard_american",
                "experience_level": "beginner",
            },
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        
        session = AICoachSession.objects.get(id=response.data["id"])
        self.assertEqual(session.user, self.user)
        self.assertEqual(
            session.session_type, AICoachSession.SessionType.NUTRITION_ANALYSIS
        )
        self.assertEqual(session.status, AICoachSession.Status.IN_PROGRESS)

    def test_filter_sessions_by_type(self):
        """Test filtering sessions by type."""
        url = reverse("ai_coaching:sessions-list")
        response = self.client.get(
            url, {"session_type": AICoachSession.SessionType.GENERAL_COACHING}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 1)
        self.assertEqual(
            results[0]["session_type"], AICoachSession.SessionType.GENERAL_COACHING
        )

    def test_filter_sessions_by_status(self):
        """Test filtering sessions by status."""
        url = reverse("ai_coaching:sessions-list")
        response = self.client.get(url, {"status": AICoachSession.Status.COMPLETED})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["status"], AICoachSession.Status.COMPLETED)

    def test_session_ordering(self):
        """Test that sessions are ordered by start time."""
        url = reverse("ai_coaching:sessions-list")
        response = self.client.get(url, {"ordering": "-start_time"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        start_times = [session["start_time"] for session in results]
        self.assertEqual(start_times, sorted(start_times, reverse=True))


class AICoachingPermissionsTest(TestCase):
    """Tests for permissions across AI coaching views."""

    def setUp(self):
        self.client = APIClient()
        self.user1 = User.objects.create_user(
            username="user1", email="<EMAIL>", password="pass123"
        )
        self.user2 = User.objects.create_user(
            username="user2", email="<EMAIL>", password="pass123"
        )

        
        self.user1_session = AICoachSession.objects.create(
            user=self.user1,
            session_type=AICoachSession.SessionType.GENERAL_COACHING,
            start_time=timezone.now(),
            status=AICoachSession.Status.IN_PROGRESS,
        )

        self.user1_recommendation = AIRecommendation.objects.create(
            user=self.user1,
            recommendation_type=RecommendationType.NUTRITION,
            title="Test Recommendation",
            content="Test content",
            confidence_score=Decimal("0.8"),
            priority=AIRecommendation.Priority.MEDIUM,
            status=AIRecommendation.Status.ACTIVE,
        )

    def test_cross_user_session_access_denied(self):
        """Test that users cannot access other users' sessions."""
        self.client.force_authenticate(user=self.user2)

        
        url = reverse(
            "ai_coaching:sessions-detail", kwargs={"pk": self.user1_session.pk}
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_cross_user_recommendation_access_denied(self):
        """Test that users cannot access other users' recommendations."""
        self.client.force_authenticate(user=self.user2)

        
        url = reverse("ai_coaching:recommendations-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 0)

    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated users cannot access AI coaching features."""
        
        url = reverse("ai_coaching:health-insights")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        
        url = reverse("ai_coaching:sessions-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class AICoachingValidationTest(TestCase):
    """Tests for validation in AI coaching views."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

    def test_invalid_meal_plan_request(self):
        """Test validation for invalid meal plan requests."""
        url = reverse("ai_coaching:meal-plan-weekly")
        data = {
            "start_date": "invalid-date",
            "target_calories": -1000,  
            "meal_frequency": 20,  
            "dietary_preferences": ["invalid_preference"],
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_chat_session_data(self):
        """Test validation for invalid chat session data."""
        url = reverse("ai_coaching:ai-chat-create")
        data = {
            "initial_message": "",  
            "chat_type": "invalid_type",
            "context": "invalid_context_format",  
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_session_creation(self):
        """Test validation for invalid coaching session creation."""
        url = reverse("ai_coaching:sessions-list")
        data = {
            "session_type": "invalid_type",
            "goals": [],  
            "context": "invalid_format",
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
