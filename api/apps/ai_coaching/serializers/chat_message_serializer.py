"""
Chat Message Serializers

Serializers for handling AI chat message data in API responses.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import ChatMessage, MessageSender, MessageType
from core.abstract.serializers import AbstractSerializer


class ChatMessageListSerializer(AbstractSerializer):
    """
    Serializer for listing chat messages.
    """
    
    sender_display = serializers.CharField(
        source='get_sender_display',
        read_only=True
    )
    message_type_display = serializers.CharField(
        source='get_message_type_display',
        read_only=True
    )
    is_from_user = serializers.BooleanField(read_only=True)
    is_from_ai = serializers.BooleanField(read_only=True)
    has_replies = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = ChatMessage
        fields = AbstractSerializer.Meta.fields + [
            'sender',
            'sender_display',
            'message',
            'message_type',
            'message_type_display',
            'timestamp',
            'confidence_score',
            'is_from_user',
            'is_from_ai',
            'has_replies',
            'is_flagged',
        ]
        read_only_fields = AbstractSerializer.Meta.read_only_fields + [
            'timestamp',
            'confidence_score',
            'is_flagged',
        ]


class ChatMessageDetailSerializer(AbstractSerializer):
    """
    Detailed serializer for chat messages.
    """
    
    sender_display = serializers.CharField(
        source='get_sender_display',
        read_only=True
    )
    message_type_display = serializers.CharField(
        source='get_message_type_display',
        read_only=True
    )
    is_from_user = serializers.BooleanField(read_only=True)
    is_from_ai = serializers.BooleanField(read_only=True)
    has_replies = serializers.BooleanField(read_only=True)
    
    # Parent and replies
    parent_message_id = serializers.UUIDField(
        source='parent_message.id',
        read_only=True
    )
    replies_count = serializers.SerializerMethodField()
    
    # Session information
    session_info = serializers.SerializerMethodField()
    
    class Meta:
        model = ChatMessage
        fields = AbstractSerializer.Meta.fields + [
            'sender',
            'sender_display',
            'message',
            'message_type',
            'message_type_display',
            'timestamp',
            'parent_message_id',
            'metadata',
            'ai_processing_time_ms',
            'confidence_score',
            'is_sensitive',
            'intent_detected',
            'entities_extracted',
            'sentiment_score',
            'is_flagged',
            'flag_reason',
            'ai_model_version',
            'is_from_user',
            'is_from_ai',
            'has_replies',
            'replies_count',
            'session_info',
        ]
        read_only_fields = AbstractSerializer.Meta.read_only_fields + [
            'timestamp',
            'metadata',
            'ai_processing_time_ms',
            'confidence_score',
            'intent_detected',
            'entities_extracted',
            'sentiment_score',
            'is_flagged',
            'flag_reason',
            'ai_model_version',
        ]
    
    def get_replies_count(self, obj) -> int:
        """Get count of replies to this message"""
        return obj.replies.count()
    
    def get_session_info(self, obj) -> dict:
        """Get basic session information"""
        return {
            'id': str(obj.session.id),
            'session_type': obj.session.session_type,
            'session_type_display': obj.session.get_session_type_display(),
        }


class ChatMessageCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new chat messages (user messages).
    """
    
    class Meta:
        model = ChatMessage
        fields = [
            'message',
            'message_type',
        ]
    
    def validate_message(self, value):
        """Validate message content"""
        if not value or not value.strip():
            raise serializers.ValidationError(
                _("Message content cannot be empty")
            )
        
        if len(value) > 5000:  # Reasonable limit
            raise serializers.ValidationError(
                _("Message content is too long (maximum 5000 characters)")
            )
        
        return value.strip()
    
    def validate_message_type(self, value):
        """Validate message type for user messages"""
        # Users can only send certain message types
        allowed_types = [
            MessageType.TEXT,
            MessageType.QUESTION,
            MessageType.FOLLOW_UP,
        ]
        
        if value not in allowed_types:
            raise serializers.ValidationError(
                _("Invalid message type for user messages. Allowed types: {types}").format(
                    types=', '.join(allowed_types)
                )
            )
        
        return value
    
    def create(self, validated_data):
        """Create user message and generate AI response"""
        session = self.context['session']
        
        # Import here to avoid circular imports
        from ..services import AICoachService
        
        ai_coach_service = AICoachService()
        
        # Process user message and get AI response
        result = ai_coach_service.process_user_message(
            session=session,
            message=validated_data['message'],
            message_type=validated_data.get('message_type', MessageType.TEXT)
        )
        
        # Return the user message (AI response is created internally)
        return result.get('ai_message')  # Return AI message for consistency


class ChatMessageReplySerializer(serializers.ModelSerializer):
    """
    Serializer for creating reply messages.
    """
    
    parent_message_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = ChatMessage
        fields = [
            'message',
            'message_type',
            'parent_message_id',
        ]
    
    def validate_parent_message_id(self, value):
        """Validate parent message exists and belongs to session"""
        session = self.context['session']
        
        try:
            parent_message = ChatMessage.objects.get(
                id=value,
                session=session
            )
        except ChatMessage.DoesNotExist:
            raise serializers.ValidationError(
                _("Parent message not found in this session")
            )
        
        self.parent_message = parent_message
        return value
    
    def create(self, validated_data):
        """Create reply message"""
        session = self.context['session']
        validated_data.pop('parent_message_id')  # Remove as it's not a model field
        
        # Import here to avoid circular imports
        from ..services import ChatBotService
        
        chat_bot_service = ChatBotService()
        
        message = chat_bot_service.create_message(
            session=session,
            sender=MessageSender.USER,
            message=validated_data['message'],
            message_type=validated_data.get('message_type', MessageType.FOLLOW_UP),
            parent_message=self.parent_message
        )
        
        return message


class ChatConversationSerializer(serializers.Serializer):
    """
    Serializer for complete conversation data.
    """
    
    session_id = serializers.UUIDField(read_only=True)
    session_type = serializers.CharField(read_only=True)
    session_status = serializers.CharField(read_only=True)
    messages = ChatMessageListSerializer(many=True, read_only=True)
    conversation_summary = serializers.SerializerMethodField()
    
    def get_conversation_summary(self, obj) -> dict:
        """Get conversation summary data"""
        # Import here to avoid circular imports
        from ..services import ChatBotService
        
        chat_bot_service = ChatBotService()
        return chat_bot_service.get_conversation_summary(obj)


class ChatMessageSearchSerializer(serializers.Serializer):
    """
    Serializer for searching chat messages.
    """
    
    query = serializers.CharField(
        required=True,
        min_length=2,
        help_text=_("Search query (minimum 2 characters)")
    )
    
    message_type = serializers.ChoiceField(
        choices=MessageType.choices,
        required=False,
        help_text=_("Filter by message type")
    )
    
    sender = serializers.ChoiceField(
        choices=MessageSender.choices,
        required=False,
        help_text=_("Filter by message sender")
    )
    
    session_id = serializers.UUIDField(
        required=False,
        help_text=_("Filter by specific session")
    )
    
    date_from = serializers.DateTimeField(
        required=False,
        help_text=_("Filter messages from this date")
    )
    
    date_to = serializers.DateTimeField(
        required=False,
        help_text=_("Filter messages to this date")
    )
    
    def validate(self, attrs):
        """Validate search parameters"""
        date_from = attrs.get('date_from')
        date_to = attrs.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise serializers.ValidationError(
                _("Date from cannot be later than date to")
            )
        
        return attrs


class ChatMessageFlagSerializer(serializers.Serializer):
    """
    Serializer for flagging inappropriate messages.
    """
    
    FLAG_REASONS = [
        ('inappropriate_content', _('Inappropriate Content')),
        ('spam', _('Spam')),
        ('harmful_advice', _('Harmful Medical Advice')),
        ('personal_info', _('Contains Personal Information')),
        ('other', _('Other')),
    ]
    
    reason = serializers.ChoiceField(
        choices=FLAG_REASONS,
        help_text=_("Reason for flagging this message")
    )
    
    details = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=500,
        help_text=_("Additional details about the flag")
    )
    
    def save(self, message):
        """Flag the message"""
        validated_data = self.validated_data
        reason = validated_data['reason']
        details = validated_data.get('details', '')
        
        flag_reason = f"{reason}: {details}" if details else reason
        message.flag_message(flag_reason)
        
        return message


class ChatMessageAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for chat message analytics.
    """
    
    total_messages = serializers.IntegerField(read_only=True)
    user_messages = serializers.IntegerField(read_only=True)
    ai_messages = serializers.IntegerField(read_only=True)
    average_response_time_ms = serializers.FloatField(read_only=True)
    most_common_intents = serializers.ListField(read_only=True)
    sentiment_distribution = serializers.DictField(read_only=True)
    flagged_messages_count = serializers.IntegerField(read_only=True)
    conversation_duration_minutes = serializers.IntegerField(read_only=True) 