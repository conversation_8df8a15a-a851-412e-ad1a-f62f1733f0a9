"""
AI Coach Session Serializers

Serializers for handling AI coaching session data in API responses.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import AICoachSession, SessionType, SessionStatus
from core.abstract.serializers import AbstractSerializer


class AICoachSessionListSerializer(AbstractSerializer):
    """
    Serializer for listing AI coach sessions.
    """
    
    session_type_display = serializers.CharField(
        source='get_session_type_display', 
        read_only=True
    )
    status_display = serializers.CharField(
        source='get_status_display', 
        read_only=True
    )
    duration_hours = serializers.FloatField(read_only=True)
    is_active = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = AICoachSession
        fields = AbstractSerializer.Meta.fields + [
            'session_type',
            'session_type_display',
            'status',
            'status_display',
            'start_time',
            'end_time',
            'session_duration_minutes',
            'duration_hours',
            'is_active',
            'user_satisfaction_score',
            'ai_model_version',
        ]
        read_only_fields = AbstractSerializer.Meta.read_only_fields + [
            'start_time',
            'end_time',
            'session_duration_minutes',
            'ai_model_version',
        ]


class AICoachSessionDetailSerializer(AbstractSerializer):
    """
    Detailed serializer for AI coach sessions.
    """
    
    session_type_display = serializers.CharField(
        source='get_session_type_display', 
        read_only=True
    )
    status_display = serializers.CharField(
        source='get_status_display', 
        read_only=True
    )
    duration_hours = serializers.FloatField(read_only=True)
    is_active = serializers.BooleanField(read_only=True)
    
    # Related data
    chat_messages_count = serializers.SerializerMethodField()
    recommendations_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AICoachSession
        fields = AbstractSerializer.Meta.fields + [
            'session_type',
            'session_type_display',
            'status',
            'status_display',
            'start_time',
            'end_time',
            'summary',
            'goals_discussed',
            'recommendations_given',
            'user_satisfaction_score',
            'session_duration_minutes',
            'duration_hours',
            'is_active',
            'ai_model_version',
            'session_metadata',
            'chat_messages_count',
            'recommendations_count',
        ]
        read_only_fields = AbstractSerializer.Meta.read_only_fields + [
            'start_time',
            'end_time',
            'session_duration_minutes',
            'ai_model_version',
            'summary',
            'recommendations_given',
        ]
    
    def get_chat_messages_count(self, obj) -> int:
        """Get count of chat messages in this session"""
        return obj.chat_messages.count()
    
    def get_recommendations_count(self, obj) -> int:
        """Get count of recommendations in this session"""
        return obj.recommendations.count()


class AICoachSessionCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new AI coach sessions.
    """
    
    initial_message = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text=_("Optional initial message to start the session")
    )
    
    class Meta:
        model = AICoachSession
        fields = [
            'session_type',
            'initial_message',
        ]
    
    def validate_session_type(self, value):
        """Validate session type"""
        if value not in [choice[0] for choice in SessionType.choices]:
            raise serializers.ValidationError(
                _("Invalid session type. Choose from: {choices}").format(
                    choices=', '.join([choice[1] for choice in SessionType.choices])
                )
            )
        return value
    
    def create(self, validated_data):
        """Create new coaching session"""
        user = self.context['request'].user
        initial_message = validated_data.pop('initial_message', None)
        
        # Import here to avoid circular imports
        from ..services import AICoachService
        
        ai_coach_service = AICoachService()
        session = ai_coach_service.start_coaching_session(
            user=user,
            session_type=validated_data['session_type'],
            initial_message=initial_message
        )
        
        return session


class AICoachSessionUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating AI coach sessions.
    """
    
    class Meta:
        model = AICoachSession
        fields = [
            'user_satisfaction_score',
            'session_metadata',
        ]
    
    def validate_user_satisfaction_score(self, value):
        """Validate satisfaction score range"""
        if value is not None and (value < 1 or value > 5):
            raise serializers.ValidationError(
                _("Satisfaction score must be between 1 and 5")
            )
        return value


class AICoachSessionEndSerializer(serializers.Serializer):
    """
    Serializer for ending AI coach sessions.
    """
    
    user_feedback = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text=_("Optional feedback about the session")
    )
    
    satisfaction_score = serializers.IntegerField(
        required=False,
        min_value=1,
        max_value=5,
        help_text=_("Session satisfaction rating from 1-5")
    )
    
    def validate(self, attrs):
        """Validate end session data"""
        satisfaction_score = attrs.get('satisfaction_score')
        user_feedback = attrs.get('user_feedback')
        
        # If providing a low satisfaction score, encourage feedback
        if satisfaction_score and satisfaction_score <= 2 and not user_feedback:
            raise serializers.ValidationError(
                _("Please provide feedback when giving a low satisfaction rating")
            )
        
        return attrs
    
    def save(self):
        """End the coaching session"""
        session = self.context['session']
        validated_data = self.validated_data
        
        # Import here to avoid circular imports
        from ..services import AICoachService
        
        ai_coach_service = AICoachService()
        result = ai_coach_service.end_coaching_session(
            session=session,
            user_feedback=validated_data.get('user_feedback'),
            satisfaction_score=validated_data.get('satisfaction_score')
        )
        
        return result


class AICoachSessionStatsSerializer(serializers.Serializer):
    """
    Serializer for user coaching statistics.
    """
    
    total_sessions = serializers.IntegerField(read_only=True)
    completed_sessions = serializers.IntegerField(read_only=True)
    total_coaching_hours = serializers.FloatField(read_only=True)
    average_satisfaction = serializers.FloatField(read_only=True)
    session_types = serializers.ListField(read_only=True)
    recent_sessions = AICoachSessionListSerializer(many=True, read_only=True) 