"""
AI Recommendation Serializers

Serializers for handling AI recommendation data in API responses.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import (
    AIRecommendation, RecommendationType, RecommendationPriority, 
    RecommendationStatus
)
from core.abstract.serializers import AbstractSerializer


class AIRecommendationListSerializer(AbstractSerializer):
    """
    Serializer for listing AI recommendations.
    """
    
    recommendation_type_display = serializers.CharField(
        source='get_recommendation_type_display',
        read_only=True
    )
    priority_display = serializers.Char<PERSON>ield(
        source='get_priority_display',
        read_only=True
    )
    status_display = serializers.Char<PERSON>ield(
        source='get_status_display',
        read_only=True
    )
    is_expired = serializers.BooleanField(read_only=True)
    is_high_confidence = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = AIRecommendation
        fields = AbstractSerializer.Meta.fields + [
            'recommendation_type',
            'recommendation_type_display',
            'title',
            'confidence_score',
            'priority',
            'priority_display',
            'status',
            'status_display',
            'tags',
            'expires_at',
            'viewed_at',
            'accepted_at',
            'completed_at',
            'user_rating',
            'is_expired',
            'is_high_confidence',
        ]
        read_only_fields = AbstractSerializer.Meta.read_only_fields + [
            'confidence_score',
            'expires_at',
            'viewed_at',
            'accepted_at',
            'completed_at',
        ]


class AIRecommendationDetailSerializer(AbstractSerializer):
    """
    Detailed serializer for AI recommendations.
    """
    
    recommendation_type_display = serializers.CharField(
        source='get_recommendation_type_display',
        read_only=True
    )
    priority_display = serializers.CharField(
        source='get_priority_display',
        read_only=True
    )
    status_display = serializers.CharField(
        source='get_status_display',
        read_only=True
    )
    is_expired = serializers.BooleanField(read_only=True)
    is_high_confidence = serializers.BooleanField(read_only=True)
    
    # Session information
    session_info = serializers.SerializerMethodField()
    
    class Meta:
        model = AIRecommendation
        fields = AbstractSerializer.Meta.fields + [
            'recommendation_type',
            'recommendation_type_display',
            'title',
            'content',
            'confidence_score',
            'priority',
            'priority_display',
            'status',
            'status_display',
            'tags',
            'metadata',
            'expected_outcomes',
            'personalization_factors',
            'expires_at',
            'viewed_at',
            'accepted_at',
            'completed_at',
            'user_feedback',
            'user_rating',
            'ai_model_version',
            'is_expired',
            'is_high_confidence',
            'session_info',
        ]
        read_only_fields = AbstractSerializer.Meta.read_only_fields + [
            'confidence_score',
            'metadata',
            'expected_outcomes',
            'personalization_factors',
            'expires_at',
            'viewed_at',
            'accepted_at',
            'completed_at',
            'ai_model_version',
        ]
    
    def get_session_info(self, obj) -> dict:
        """Get session information if recommendation is linked to a session"""
        if obj.session:
            return {
                'id': str(obj.session.id),
                'session_type': obj.session.session_type,
                'session_type_display': obj.session.get_session_type_display(),
                'start_time': obj.session.start_time,
            }
        return None


class AIRecommendationUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating AI recommendations (user actions).
    """
    
    class Meta:
        model = AIRecommendation
        fields = [
            'status',
            'user_feedback',
            'user_rating',
        ]
    
    def validate_status(self, value):
        """Validate status transitions"""
        if self.instance:
            current_status = self.instance.status
            
            # Define valid status transitions
            valid_transitions = {
                RecommendationStatus.PENDING: [
                    RecommendationStatus.VIEWED,
                    RecommendationStatus.ACCEPTED,
                    RecommendationStatus.DECLINED
                ],
                RecommendationStatus.VIEWED: [
                    RecommendationStatus.ACCEPTED,
                    RecommendationStatus.DECLINED
                ],
                RecommendationStatus.ACCEPTED: [
                    RecommendationStatus.COMPLETED,
                    RecommendationStatus.DECLINED
                ],
                RecommendationStatus.DECLINED: [],  # Terminal state
                RecommendationStatus.COMPLETED: []  # Terminal state
            }
            
            if value != current_status and value not in valid_transitions.get(current_status, []):
                raise serializers.ValidationError(
                    _("Invalid status transition from {current} to {new}").format(
                        current=current_status, new=value
                    )
                )
        
        return value
    
    def validate_user_rating(self, value):
        """Validate user rating"""
        if value is not None and (value < 1 or value > 5):
            raise serializers.ValidationError(
                _("User rating must be between 1 and 5")
            )
        return value
    
    def update(self, instance, validated_data):
        """Update recommendation and trigger appropriate actions"""
        new_status = validated_data.get('status')
        
        # Handle status changes with business logic
        if new_status and new_status != instance.status:
            if new_status == RecommendationStatus.VIEWED:
                instance.mark_viewed()
            elif new_status == RecommendationStatus.ACCEPTED:
                instance.mark_accepted()
            elif new_status == RecommendationStatus.COMPLETED:
                instance.mark_completed()
            elif new_status == RecommendationStatus.DECLINED:
                feedback = validated_data.get('user_feedback')
                instance.mark_declined(feedback)
        
        # Update other fields
        for field in ['user_feedback', 'user_rating']:
            if field in validated_data:
                setattr(instance, field, validated_data[field])
        
        instance.save()
        return instance


class AIRecommendationFilterSerializer(serializers.Serializer):
    """
    Serializer for filtering recommendations.
    """
    
    recommendation_type = serializers.ChoiceField(
        choices=RecommendationType.choices,
        required=False,
        help_text=_("Filter by recommendation type")
    )
    
    status = serializers.ChoiceField(
        choices=RecommendationStatus.choices,
        required=False,
        help_text=_("Filter by status")
    )
    
    priority = serializers.ChoiceField(
        choices=RecommendationPriority.choices,
        required=False,
        help_text=_("Filter by priority")
    )
    
    min_confidence = serializers.DecimalField(
        max_digits=3,
        decimal_places=2,
        min_value=0.0,
        max_value=1.0,
        required=False,
        help_text=_("Minimum confidence score")
    )
    
    tags = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text=_("Filter by tags")
    )
    
    include_expired = serializers.BooleanField(
        default=False,
        help_text=_("Include expired recommendations")
    )
    
    session_type = serializers.ChoiceField(
        choices=[],  # Will be populated in __init__
        required=False,
        help_text=_("Filter by originating session type")
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Import here to avoid circular imports
        from ..models import SessionType
        self.fields['session_type'].choices = SessionType.choices


class AIRecommendationActionSerializer(serializers.Serializer):
    """
    Serializer for bulk actions on recommendations.
    """
    
    ACTION_CHOICES = [
        ('mark_viewed', _('Mark as Viewed')),
        ('mark_accepted', _('Mark as Accepted')),
        ('mark_declined', _('Mark as Declined')),
        ('mark_completed', _('Mark as Completed')),
    ]
    
    recommendation_ids = serializers.ListField(
        child=serializers.UUIDField(),
        help_text=_("List of recommendation IDs to perform action on")
    )
    
    action = serializers.ChoiceField(
        choices=ACTION_CHOICES,
        help_text=_("Action to perform on selected recommendations")
    )
    
    feedback = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text=_("Optional feedback for declined recommendations")
    )
    
    def validate_recommendation_ids(self, value):
        """Validate that all recommendation IDs belong to the current user"""
        user = self.context['request'].user
        
        existing_ids = AIRecommendation.objects.filter(
            id__in=value,
            user=user
        ).values_list('id', flat=True)
        
        if len(existing_ids) != len(value):
            missing_ids = set(value) - set(existing_ids)
            raise serializers.ValidationError(
                _("Some recommendation IDs not found or don't belong to you: {ids}").format(
                    ids=', '.join(str(id) for id in missing_ids)
                )
            )
        
        return value
    
    def validate(self, attrs):
        """Validate action requirements"""
        action = attrs.get('action')
        feedback = attrs.get('feedback')
        
        if action == 'mark_declined' and not feedback:
            raise serializers.ValidationError(
                _("Feedback is required when declining recommendations")
            )
        
        return attrs
    
    def save(self):
        """Perform bulk action on recommendations"""
        validated_data = self.validated_data
        user = self.context['request'].user
        
        recommendations = AIRecommendation.objects.filter(
            id__in=validated_data['recommendation_ids'],
            user=user
        )
        
        action = validated_data['action']
        feedback = validated_data.get('feedback')
        
        results = {
            'processed': 0,
            'skipped': 0,
            'errors': []
        }
        
        for recommendation in recommendations:
            try:
                if action == 'mark_viewed':
                    recommendation.mark_viewed()
                elif action == 'mark_accepted':
                    recommendation.mark_accepted()
                elif action == 'mark_declined':
                    recommendation.mark_declined(feedback)
                elif action == 'mark_completed':
                    recommendation.mark_completed()
                
                results['processed'] += 1
                
            except Exception as e:
                results['skipped'] += 1
                results['errors'].append({
                    'recommendation_id': str(recommendation.id),
                    'error': str(e)
                })
        
        return results 