"""
Personalized Meal Plan Serializers

Serializers for handling AI-generated personalized meal plans.
"""
from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from datetime import datetime, timedelta

from ..models import AIRecommendation


class MealPlanRequestSerializer(serializers.Serializer):
    """
    Serializer for meal plan generation requests.
    """
    
    MEAL_COUNT_CHOICES = [
        (1, _('1 meal per day (OMAD)')),
        (2, _('2 meals per day')),
        (3, _('3 meals per day')),
    ]
    
    duration_days = serializers.IntegerField(
        default=7,
        min_value=1,
        max_value=30,
        help_text=_("Number of days to plan for (1-30)")
    )
    
    meals_per_day = serializers.ChoiceField(
        choices=MEAL_COUNT_CHOICES,
        default=2,
        help_text=_("Number of meals per day")
    )
    
    calorie_target = serializers.IntegerField(
        required=False,
        min_value=1000,
        max_value=5000,
        help_text=_("Daily calorie target (optional, will be calculated if not provided)")
    )
    
    protein_preference = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text=_("Preferred protein sources (e.g., ['beef', 'pork', 'fish'])")
    )
    
    cooking_time_limit = serializers.IntegerField(
        required=False,
        min_value=5,
        max_value=180,
        help_text=_("Maximum cooking time per meal in minutes")
    )
    
    avoid_foods = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text=_("Foods to avoid")
    )
    
    health_goals = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text=_("Health goals to optimize for")
    )
    
    budget_level = serializers.ChoiceField(
        choices=[
            ('low', _('Budget-friendly')),
            ('medium', _('Moderate budget')),
            ('high', _('Premium options')),
        ],
        default='medium',
        help_text=_("Budget level for meal planning")
    )
    
    include_organ_meats = serializers.BooleanField(
        default=False,
        help_text=_("Include organ meats for nutrient density")
    )
    
    start_date = serializers.DateField(
        required=False,
        help_text=_("Start date for meal plan (defaults to today)")
    )
    
    def validate_protein_preference(self, value):
        """Validate protein preferences"""
        if value:
            valid_proteins = ['beef', 'pork', 'chicken', 'fish', 'lamb', 'turkey', 'eggs']
            invalid_proteins = [p for p in value if p.lower() not in valid_proteins]
            
            if invalid_proteins:
                raise serializers.ValidationError(
                    _("Invalid protein types: {proteins}. Valid options: {valid}").format(
                        proteins=', '.join(invalid_proteins),
                        valid=', '.join(valid_proteins)
                    )
                )
        
        return [p.lower() for p in value] if value else []
    
    def validate_start_date(self, value):
        """Validate start date is not in the past"""
        if value and value < datetime.now().date():
            raise serializers.ValidationError(
                _("Start date cannot be in the past")
            )
        return value
    
    def validate(self, attrs):
        """Cross-field validation"""
        duration_days = attrs.get('duration_days', 7)
        meals_per_day = attrs.get('meals_per_day', 2)
        
        # Reasonable limits
        total_meals = duration_days * meals_per_day
        if total_meals > 90:  # 30 days * 3 meals
            raise serializers.ValidationError(
                _("Total meals ({total}) exceeds maximum of 90").format(total=total_meals)
            )
        
        return attrs


class MealPlanGenerationResultSerializer(serializers.Serializer):
    """
    Serializer for meal plan generation results.
    """
    
    meal_plans = serializers.ListField(
        child=serializers.DictField(),
        read_only=True,
        help_text=_("Generated meal plan recommendations")
    )
    
    nutrition_summary = serializers.DictField(
        read_only=True,
        help_text=_("Overall nutrition summary")
    )
    
    shopping_list = serializers.ListField(
        child=serializers.DictField(),
        read_only=True,
        help_text=_("Shopping list for the meal plan")
    )
    
    preparation_tips = serializers.ListField(
        child=serializers.CharField(),
        read_only=True,
        help_text=_("Meal preparation tips")
    )
    
    total_estimated_cost = serializers.DecimalField(
        max_digits=8,
        decimal_places=2,
        read_only=True,
        help_text=_("Estimated total cost")
    )


class SingleMealSuggestionSerializer(serializers.Serializer):
    """
    Serializer for single meal suggestions.
    """
    
    MEAL_TYPE_CHOICES = [
        ('breakfast', _('Breakfast')),
        ('lunch', _('Lunch')),
        ('dinner', _('Dinner')),
        ('snack', _('Snack')),
        ('any', _('Any meal type')),
    ]
    
    meal_type = serializers.ChoiceField(
        choices=MEAL_TYPE_CHOICES,
        default='any',
        help_text=_("Type of meal to suggest")
    )
    
    protein_preference = serializers.CharField(
        required=False,
        help_text=_("Preferred protein source")
    )
    
    cooking_time_limit = serializers.IntegerField(
        required=False,
        min_value=5,
        max_value=180,
        help_text=_("Maximum cooking time in minutes")
    )
    
    serving_size = serializers.IntegerField(
        default=1,
        min_value=1,
        max_value=8,
        help_text=_("Number of servings")
    )
    
    difficulty_level = serializers.ChoiceField(
        choices=[
            ('easy', _('Easy')),
            ('medium', _('Medium')),
            ('hard', _('Advanced')),
        ],
        default='easy',
        help_text=_("Preferred cooking difficulty")
    )
    
    equipment_available = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        help_text=_("Available cooking equipment")
    )


class MealPlanOptimizationSerializer(serializers.Serializer):
    """
    Serializer for meal plan optimization requests.
    """
    
    current_meal_plan_ids = serializers.ListField(
        child=serializers.UUIDField(),
        help_text=_("IDs of current meal plan recommendations to optimize")
    )
    
    optimization_goals = serializers.ListField(
        child=serializers.CharField(),
        help_text=_("Goals to optimize for (e.g., 'weight_loss', 'muscle_gain', 'energy')")
    )
    
    constraints = serializers.DictField(
        required=False,
        help_text=_("Additional constraints for optimization")
    )
    
    def validate_current_meal_plan_ids(self, value):
        """Validate meal plan IDs belong to the user"""
        user = self.context['request'].user
        
        existing_ids = AIRecommendation.objects.filter(
            id__in=value,
            user=user,
            recommendation_type='meal_plan'
        ).values_list('id', flat=True)
        
        if len(existing_ids) != len(value):
            missing_ids = set(value) - set(existing_ids)
            raise serializers.ValidationError(
                _("Some meal plan IDs not found: {ids}").format(
                    ids=', '.join(str(id) for id in missing_ids)
                )
            )
        
        return value
    
    def validate_optimization_goals(self, value):
        """Validate optimization goals"""
        valid_goals = [
            'weight_loss', 'weight_gain', 'muscle_gain', 'fat_loss',
            'energy_increase', 'inflammation_reduction', 'digestion_improvement',
            'cost_reduction', 'time_saving', 'nutrient_density'
        ]
        
        invalid_goals = [g for g in value if g not in valid_goals]
        if invalid_goals:
            raise serializers.ValidationError(
                _("Invalid optimization goals: {goals}. Valid options: {valid}").format(
                    goals=', '.join(invalid_goals),
                    valid=', '.join(valid_goals)
                )
            )
        
        return value


class MealPlanFeedbackSerializer(serializers.Serializer):
    """
    Serializer for meal plan feedback.
    """
    
    meal_plan_id = serializers.UUIDField(
        help_text=_("ID of the meal plan recommendation")
    )
    
    overall_rating = serializers.IntegerField(
        min_value=1,
        max_value=5,
        help_text=_("Overall rating (1-5)")
    )
    
    taste_rating = serializers.IntegerField(
        required=False,
        min_value=1,
        max_value=5,
        help_text=_("Taste rating (1-5)")
    )
    
    difficulty_rating = serializers.IntegerField(
        required=False,
        min_value=1,
        max_value=5,
        help_text=_("Cooking difficulty rating (1-5)")
    )
    
    time_accuracy = serializers.BooleanField(
        required=False,
        help_text=_("Was the cooking time estimate accurate?")
    )
    
    would_make_again = serializers.BooleanField(
        required=False,
        help_text=_("Would you make this meal again?")
    )
    
    modifications_made = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text=_("Any modifications you made to the recipe")
    )
    
    feedback_text = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text=_("Additional feedback about the meal plan")
    )
    
    def validate_meal_plan_id(self, value):
        """Validate meal plan belongs to user"""
        user = self.context['request'].user
        
        try:
            meal_plan = AIRecommendation.objects.get(
                id=value,
                user=user,
                recommendation_type='meal_plan'
            )
        except AIRecommendation.DoesNotExist:
            raise serializers.ValidationError(
                _("Meal plan not found or doesn't belong to you")
            )
        
        self.meal_plan = meal_plan
        return value
    
    def save(self):
        """Save feedback and learn from it"""
        validated_data = self.validated_data
        
        # Update recommendation with feedback
        self.meal_plan.user_rating = validated_data['overall_rating']
        self.meal_plan.user_feedback = validated_data.get('feedback_text', '')
        self.meal_plan.save()
        
        # Learn from feedback for future recommendations
        from ..services import PersonalizationService
        
        personalization_service = PersonalizationService()
        
        feedback_type = 'positive' if validated_data['overall_rating'] >= 4 else 'negative'
        
        personalization_service.learn_from_feedback(
            user=self.meal_plan.user,
            recommendation_type='meal_plan',
            feedback_type=feedback_type,
            feedback_data=validated_data,
            rating=validated_data['overall_rating']
        )
        
        return validated_data


class MealPlanAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for meal plan analytics.
    """
    
    total_meal_plans = serializers.IntegerField(read_only=True)
    completed_meal_plans = serializers.IntegerField(read_only=True)
    average_rating = serializers.FloatField(read_only=True)
    most_preferred_proteins = serializers.ListField(read_only=True)
    average_cooking_time = serializers.FloatField(read_only=True)
    nutrition_trends = serializers.DictField(read_only=True)
    cost_analysis = serializers.DictField(read_only=True)
    success_rate = serializers.FloatField(read_only=True) 