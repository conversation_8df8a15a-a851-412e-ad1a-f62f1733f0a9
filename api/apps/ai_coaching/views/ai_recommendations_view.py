"""
AI Recommendations Views

Views for managing AI-generated recommendations.
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON><PERSON>er, OrderingFilter
from django.utils import timezone
from django.db.models import Q

from ..models import AIRecommendation, RecommendationType, RecommendationStatus
from ..serializers import (
    AIRecommendationListSerializer,
    AIRecommendationDetailSerializer,
    AIRecommendationUpdateSerializer,
    AIRecommendationFilterSerializer,
    AIRecommendationActionSerializer,
)
from ..services import RecommendationEngineService


class AIRecommendationViewSet(ModelViewSet):
    """
    ViewSet for managing AI recommendations.
    
    Provides:
    - List user's recommendations with filtering
    - Retrieve recommendation details
    - Update recommendation status and feedback
    - Bulk actions on recommendations
    - Generate new recommendations
    """
    
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = [
        'recommendation_type', 
        'status', 
        'priority',
        'session__session_type'
    ]
    search_fields = ['title', 'content', 'tags']
    ordering_fields = ['created', 'confidence_score', 'priority', 'expires_at']
    ordering = ['-created']
    
    def get_queryset(self):
        """Get recommendations for the current user"""
        queryset = AIRecommendation.objects.filter(
            user=self.request.user
        ).select_related('user', 'session')
        
        # Filter by request parameters
        include_expired = self.request.query_params.get('include_expired', 'false').lower() == 'true'
        if not include_expired:
            queryset = queryset.filter(
                Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
            )
        
        return queryset
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return AIRecommendationListSerializer
        elif self.action in ['update', 'partial_update']:
            return AIRecommendationUpdateSerializer
        elif self.action == 'bulk_action':
            return AIRecommendationActionSerializer
        else:
            return AIRecommendationDetailSerializer
    
    def create(self, request, *args, **kwargs):
        """
        Generate new recommendations for the user.
        
        This endpoint triggers the AI recommendation engine
        to generate personalized recommendations.
        """
        # Parse request parameters
        recommendation_types = request.data.get('types', [])
        limit = min(int(request.data.get('limit', 5)), 20)  # Max 20
        context = request.data.get('context', {})
        
        # Generate recommendations
        recommendation_service = RecommendationEngineService()
        recommendations = recommendation_service.generate_contextual_recommendations(
            user=request.user,
            recommendation_types=recommendation_types if recommendation_types else None,
            context=context,
            limit=limit
        )
        
        if not recommendations:
            return Response(
                {'message': 'No recommendations could be generated at this time'},
                status=status.HTTP_204_NO_CONTENT
            )
        
        serializer = AIRecommendationListSerializer(
            recommendations,
            many=True,
            context={'request': request}
        )
        
        return Response(
            {
                'message': f'Generated {len(recommendations)} recommendations',
                'recommendations': serializer.data
            },
            status=status.HTTP_201_CREATED
        )
    
    @action(detail=True, methods=['post'])
    def mark_viewed(self, request, pk=None):
        """Mark recommendation as viewed"""
        recommendation = self.get_object()
        recommendation.mark_viewed()
        
        serializer = self.get_serializer(recommendation)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def accept(self, request, pk=None):
        """Accept a recommendation"""
        recommendation = self.get_object()
        recommendation.mark_accepted()
        
        serializer = self.get_serializer(recommendation)
        return Response({
            'message': 'Recommendation accepted',
            'recommendation': serializer.data
        })
    
    @action(detail=True, methods=['post'])
    def decline(self, request, pk=None):
        """Decline a recommendation with optional feedback"""
        recommendation = self.get_object()
        feedback = request.data.get('feedback', '')
        
        recommendation.mark_declined(feedback)
        
        serializer = self.get_serializer(recommendation)
        return Response({
            'message': 'Recommendation declined',
            'recommendation': serializer.data
        })
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark recommendation as completed"""
        recommendation = self.get_object()
        
        if recommendation.status != RecommendationStatus.ACCEPTED:
            return Response(
                {'error': 'Only accepted recommendations can be marked as completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        recommendation.mark_completed()
        
        # Optional: collect completion feedback
        feedback = request.data.get('feedback', '')
        rating = request.data.get('rating')
        
        if feedback:
            recommendation.user_feedback = feedback
        if rating and 1 <= int(rating) <= 5:
            recommendation.user_rating = rating
        
        if feedback or rating:
            recommendation.save(update_fields=['user_feedback', 'user_rating'])
        
        serializer = self.get_serializer(recommendation)
        return Response({
            'message': 'Recommendation marked as completed',
            'recommendation': serializer.data
        })
    
    @action(detail=False, methods=['post'])
    def bulk_action(self, request):
        """
        Perform bulk actions on multiple recommendations.
        
        Supports: mark_viewed, mark_accepted, mark_declined, mark_completed
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        result = serializer.save()
        
        return Response({
            'message': f'Bulk action completed',
            'results': result
        })
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """
        Get recommendations grouped by type.
        """
        recommendations = self.get_queryset()
        
        grouped = {}
        for rec_type in RecommendationType.choices:
            type_recs = recommendations.filter(
                recommendation_type=rec_type[0]
            )[:5]  # Limit per type
            
            if type_recs.exists():
                serializer = AIRecommendationListSerializer(
                    type_recs,
                    many=True,
                    context={'request': request}
                )
                grouped[rec_type[1]] = serializer.data
        
        return Response(grouped)
    
    @action(detail=False, methods=['get'])
    def high_priority(self, request):
        """Get high priority recommendations"""
        high_priority_recs = self.get_queryset().filter(
            priority='high'
        ).order_by('-created')[:10]
        
        serializer = AIRecommendationListSerializer(
            high_priority_recs,
            many=True,
            context={'request': request}
        )
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending recommendations"""
        pending_recs = self.get_queryset().filter(
            status=RecommendationStatus.PENDING
        ).order_by('-created')
        
        serializer = AIRecommendationListSerializer(
            pending_recs,
            many=True,
            context={'request': request}
        )
        
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """
        Get recommendation analytics for the user.
        """
        recommendations = self.get_queryset()
        
        analytics = {
            'total_recommendations': recommendations.count(),
            'by_status': {},
            'by_type': {},
            'by_priority': {},
            'acceptance_rate': 0,
            'completion_rate': 0,
            'average_rating': 0,
        }
        
        # Status distribution
        for status_choice in RecommendationStatus.choices:
            count = recommendations.filter(status=status_choice[0]).count()
            analytics['by_status'][status_choice[1]] = count
        
        # Type distribution
        for type_choice in RecommendationType.choices:
            count = recommendations.filter(recommendation_type=type_choice[0]).count()
            if count > 0:
                analytics['by_type'][type_choice[1]] = count
        
        # Priority distribution
        from ..models import RecommendationPriority
        for priority_choice in RecommendationPriority.choices:
            count = recommendations.filter(priority=priority_choice[0]).count()
            analytics['by_priority'][priority_choice[1]] = count
        
        # Rates
        total = recommendations.count()
        if total > 0:
            accepted = recommendations.filter(
                status__in=[RecommendationStatus.ACCEPTED, RecommendationStatus.COMPLETED]
            ).count()
            completed = recommendations.filter(status=RecommendationStatus.COMPLETED).count()
            
            analytics['acceptance_rate'] = round((accepted / total) * 100, 2)
            analytics['completion_rate'] = round((completed / total) * 100, 2)
        
        # Average rating
        rated_recs = recommendations.filter(user_rating__isnull=False)
        if rated_recs.exists():
            from django.db.models import Avg
            avg_rating = rated_recs.aggregate(avg=Avg('user_rating'))['avg']
            analytics['average_rating'] = round(avg_rating, 2)
        
        return Response(analytics)


class AIRecommendationListView(generics.ListAPIView):
    """
    Simple list view for recommendations with filtering.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = AIRecommendationListSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['recommendation_type', 'status', 'priority']
    search_fields = ['title', 'content']
    ordering = ['-created']
    
    def get_queryset(self):
        """Get recommendations for the current user"""
        return AIRecommendation.objects.filter(
            user=self.request.user
        ).select_related('session') 