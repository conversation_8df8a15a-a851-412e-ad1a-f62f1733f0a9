"""
Personalized Meal Plan Views

Views for AI-powered meal plan generation and management.
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from decimal import Decimal

from ..models import AIRecommendation, RecommendationType
from ..serializers import (
    MealPlanRequestSerializer,
    MealPlanGenerationResultSerializer,
    SingleMealSuggestionSerializer,
    MealPlanOptimizationSerializer,
    MealPlanFeedbackSerializer,
    MealPlanAnalyticsSerializer,
)
from ..services import MealPlanAIService, RecommendationEngineService


class PersonalizedMealPlanView(APIView):
    """
    API view for generating personalized meal plans using AI.
    
    Provides:
    - Generate weekly meal plans
    - Generate daily meal plans
    - Single meal suggestions
    - Meal plan optimization
    - Meal plan feedback collection
    - Meal plan analytics
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, format=None):
        """
        Generate a personalized meal plan.
        """
        serializer = MealPlanRequestSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        validated_data = serializer.validated_data
        
        # Initialize meal plan service
        meal_plan_service = MealPlanAIService()
        
        try:
            if validated_data['duration_days'] >= 7:
                # Generate weekly meal plan
                meal_plans = meal_plan_service.generate_weekly_meal_plan(
                    user=request.user,
                    preferences={
                        'protein_preference': validated_data.get('protein_preference', []),
                        'cooking_time_limit': validated_data.get('cooking_time_limit'),
                        'budget_level': validated_data.get('budget_level', 'medium'),
                        'include_organ_meats': validated_data.get('include_organ_meats', False),
                    },
                    dietary_restrictions=validated_data.get('avoid_foods', []),
                    calorie_target=validated_data.get('calorie_target'),
                    meal_count_per_day=validated_data['meals_per_day']
                )
            else:
                # Generate daily meal plans
                meal_plans = []
                for day in range(validated_data['duration_days']):
                    date = timezone.now().date()
                    if validated_data.get('start_date'):
                        date = validated_data['start_date']
                    date = date + timezone.timedelta(days=day)
                    
                    daily_plan = meal_plan_service.generate_daily_meal_plan(
                        user=request.user,
                        date=timezone.datetime.combine(date, timezone.datetime.min.time()),
                        preferences={
                            'protein_preference': validated_data.get('protein_preference', []),
                            'cooking_time_limit': validated_data.get('cooking_time_limit'),
                            'budget_level': validated_data.get('budget_level', 'medium'),
                        },
                        meal_count=validated_data['meals_per_day']
                    )
                    meal_plans.append(daily_plan)
            
            # Generate shopping list and nutrition summary
            shopping_list = self._generate_shopping_list(meal_plans)
            nutrition_summary = self._calculate_nutrition_summary(meal_plans)
            preparation_tips = self._generate_preparation_tips(meal_plans, validated_data)
            estimated_cost = self._estimate_total_cost(meal_plans, validated_data.get('budget_level', 'medium'))
            
            # Format response
            result_data = {
                'meal_plans': [
                    {
                        'id': str(plan.id),
                        'title': plan.title,
                        'content': plan.content,
                        'metadata': plan.metadata,
                        'confidence_score': float(plan.confidence_score),
                        'tags': plan.tags
                    }
                    for plan in meal_plans
                ],
                'nutrition_summary': nutrition_summary,
                'shopping_list': shopping_list,
                'preparation_tips': preparation_tips,
                'total_estimated_cost': estimated_cost
            }
            
            result_serializer = MealPlanGenerationResultSerializer(result_data)
            
            return Response(
                {
                    'message': f'Generated meal plan for {validated_data["duration_days"]} days',
                    'meal_plan': result_serializer.data
                },
                status=status.HTTP_201_CREATED
            )
            
        except Exception as e:
            return Response(
                {'error': f'Failed to generate meal plan: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SingleMealSuggestionView(APIView):
    """
    API view for generating single meal suggestions.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, format=None):
        """
        Generate single meal suggestions.
        """
        serializer = SingleMealSuggestionSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        validated_data = serializer.validated_data
        
        # Initialize meal plan service
        meal_plan_service = MealPlanAIService()
        
        try:
            suggestions = meal_plan_service.generate_meal_suggestions(
                user=request.user,
                meal_type=validated_data.get('meal_type', 'any'),
                protein_preference=validated_data.get('protein_preference'),
                cooking_time_limit=validated_data.get('cooking_time_limit'),
                limit=5
            )
            
            suggestion_data = [
                {
                    'id': str(suggestion.id),
                    'title': suggestion.title,
                    'content': suggestion.content,
                    'metadata': suggestion.metadata,
                    'confidence_score': float(suggestion.confidence_score),
                    'tags': suggestion.tags
                }
                for suggestion in suggestions
            ]
            
            return Response(
                {
                    'message': f'Generated {len(suggestions)} meal suggestions',
                    'suggestions': suggestion_data
                },
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {'error': f'Failed to generate meal suggestions: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class MealPlanOptimizationView(APIView):
    """
    API view for optimizing existing meal plans.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, format=None):
        """
        Optimize existing meal plans based on health goals.
        """
        serializer = MealPlanOptimizationSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        validated_data = serializer.validated_data
        
        # Initialize meal plan service
        meal_plan_service = MealPlanAIService()
        
        try:
            # Get current meal plans
            current_plans = AIRecommendation.objects.filter(
                id__in=validated_data['current_meal_plan_ids'],
                user=request.user,
                recommendation_type=RecommendationType.MEAL_PLAN
            )
            
            if not current_plans.exists():
                return Response(
                    {'error': 'No valid meal plans found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Convert to meal plan data
            current_meal_plan_data = {
                'plans': [
                    {
                        'id': str(plan.id),
                        'title': plan.title,
                        'content': plan.content,
                        'metadata': plan.metadata
                    }
                    for plan in current_plans
                ]
            }
            
            # Optimize meal plan
            optimization_result = meal_plan_service.optimize_meal_plan_for_goals(
                user=request.user,
                health_goals=validated_data['optimization_goals'],
                current_meal_plan=current_meal_plan_data
            )
            
            return Response(
                {
                    'message': 'Meal plan optimization completed',
                    'optimization_result': optimization_result
                },
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            return Response(
                {'error': f'Failed to optimize meal plan: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class MealPlanFeedbackView(APIView):
    """
    API view for collecting meal plan feedback.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, format=None):
        """
        Submit feedback for a meal plan.
        """
        serializer = MealPlanFeedbackSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        # Save feedback and learn from it
        feedback_data = serializer.save()
        
        return Response(
            {
                'message': 'Feedback submitted successfully',
                'feedback': feedback_data
            },
            status=status.HTTP_200_OK
        )


class MealPlanAnalyticsView(APIView):
    """
    API view for meal plan analytics.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, format=None):
        """
        Get meal plan analytics for the user.
        """
        try:
            # Get user's meal plan recommendations
            meal_plans = AIRecommendation.objects.filter(
                user=request.user,
                recommendation_type=RecommendationType.MEAL_PLAN
            )
            
            # Calculate analytics
            analytics = self._calculate_meal_plan_analytics(meal_plans)
            
            serializer = MealPlanAnalyticsSerializer(analytics)
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to get analytics: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _calculate_meal_plan_analytics(self, meal_plans):
        """Calculate comprehensive meal plan analytics"""
        from django.db.models import Avg, Count
        from ..models import RecommendationStatus
        
        total_plans = meal_plans.count()
        completed_plans = meal_plans.filter(
            status=RecommendationStatus.COMPLETED
        ).count()
        
        # Average rating
        rated_plans = meal_plans.filter(user_rating__isnull=False)
        avg_rating = 0
        if rated_plans.exists():
            avg_rating = rated_plans.aggregate(avg=Avg('user_rating'))['avg'] or 0
        
        # Most preferred proteins (from metadata)
        protein_preferences = {}
        for plan in meal_plans:
            if plan.metadata and 'meals' in plan.metadata:
                for meal in plan.metadata['meals']:
                    protein = meal.get('protein_type')
                    if protein:
                        protein_preferences[protein] = protein_preferences.get(protein, 0) + 1
        
        most_preferred = sorted(
            protein_preferences.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # Average cooking time
        cooking_times = []
        for plan in meal_plans:
            if plan.metadata and 'meals' in plan.metadata:
                for meal in plan.metadata['meals']:
                    cook_time = meal.get('cooking_time')
                    if cook_time:
                        cooking_times.append(cook_time)
        
        avg_cooking_time = sum(cooking_times) / len(cooking_times) if cooking_times else 0
        
        # Nutrition trends (simplified)
        nutrition_trends = {
            'average_calories': 0,
            'average_protein': 0,
            'average_fat': 0
        }
        
        calorie_values = []
        protein_values = []
        fat_values = []
        
        for plan in meal_plans:
            if plan.metadata:
                calories = plan.metadata.get('total_calories')
                protein = plan.metadata.get('total_protein')
                fat = plan.metadata.get('total_fat')
                
                if calories:
                    calorie_values.append(calories)
                if protein:
                    protein_values.append(protein)
                if fat:
                    fat_values.append(fat)
        
        if calorie_values:
            nutrition_trends['average_calories'] = sum(calorie_values) / len(calorie_values)
        if protein_values:
            nutrition_trends['average_protein'] = sum(protein_values) / len(protein_values)
        if fat_values:
            nutrition_trends['average_fat'] = sum(fat_values) / len(fat_values)
        
        # Cost analysis (simplified)
        cost_analysis = {
            'average_cost_per_plan': 0,
            'cost_trend': 'stable'
        }
        
        # Success rate
        success_rate = (completed_plans / total_plans * 100) if total_plans > 0 else 0
        
        return {
            'total_meal_plans': total_plans,
            'completed_meal_plans': completed_plans,
            'average_rating': round(avg_rating, 2),
            'most_preferred_proteins': [
                {'protein': protein, 'count': count}
                for protein, count in most_preferred
            ],
            'average_cooking_time': round(avg_cooking_time, 1),
            'nutrition_trends': nutrition_trends,
            'cost_analysis': cost_analysis,
            'success_rate': round(success_rate, 2)
        }
    
    def _generate_shopping_list(self, meal_plans):
        """Generate shopping list from meal plans"""
        shopping_items = {}
        
        for plan in meal_plans:
            if plan.metadata and 'meals' in plan.metadata:
                for meal in plan.metadata['meals']:
                    protein_type = meal.get('protein_type')
                    portion_oz = meal.get('portion_oz', 4)
                    
                    if protein_type:
                        if protein_type not in shopping_items:
                            shopping_items[protein_type] = {
                                'category': 'protein',
                                'total_amount': 0,
                                'unit': 'oz'
                            }
                        shopping_items[protein_type]['total_amount'] += portion_oz
        
        # Convert to list format
        shopping_list = []
        for item, details in shopping_items.items():
            shopping_list.append({
                'item': item.title(),
                'amount': details['total_amount'],
                'unit': details['unit'],
                'category': details['category']
            })
        
        return shopping_list
    
    def _calculate_nutrition_summary(self, meal_plans):
        """Calculate overall nutrition summary"""
        total_calories = 0
        total_protein = 0
        total_fat = 0
        meal_count = 0
        
        for plan in meal_plans:
            if plan.metadata:
                calories = plan.metadata.get('total_calories', 0)
                protein = plan.metadata.get('total_protein', 0)
                fat = plan.metadata.get('total_fat', 0)
                
                total_calories += calories
                total_protein += protein
                total_fat += fat
                meal_count += plan.metadata.get('meal_count', 1)
        
        days = len(meal_plans)
        
        return {
            'total_days': days,
            'total_meals': meal_count,
            'average_calories_per_day': total_calories / days if days > 0 else 0,
            'average_protein_per_day': total_protein / days if days > 0 else 0,
            'average_fat_per_day': total_fat / days if days > 0 else 0,
            'total_calories': total_calories,
            'total_protein': round(total_protein, 1),
            'total_fat': round(total_fat, 1),
            'carbs': 0  # Carnivore diet
        }
    
    def _generate_preparation_tips(self, meal_plans, request_data):
        """Generate meal preparation tips"""
        tips = [
            "Prepare proteins in advance and store in the refrigerator",
            "Season meats with salt 40 minutes before cooking for better flavor",
            "Use cast iron pans for better heat retention and flavor",
        ]
        
        # Add specific tips based on request data
        if request_data.get('cooking_time_limit') and request_data['cooking_time_limit'] < 30:
            tips.append("For quick meals, opt for thinner cuts of meat that cook faster")
        
        if request_data.get('include_organ_meats'):
            tips.append("Start with small amounts of organ meats to gradually adapt your taste")
        
        return tips
    
    def _estimate_total_cost(self, meal_plans, budget_level):
        """Estimate total cost for meal plans"""
        base_cost_per_oz = {
            'low': 1.5,
            'medium': 2.5,
            'high': 4.0
        }
        
        cost_multiplier = base_cost_per_oz.get(budget_level, 2.5)
        total_oz = 0
        
        for plan in meal_plans:
            if plan.metadata and 'meals' in plan.metadata:
                for meal in plan.metadata['meals']:
                    total_oz += meal.get('portion_oz', 4)
        
        return Decimal(str(total_oz * cost_multiplier)) 