"""
AI Coach Session Views

Views for managing AI coaching sessions.
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter

from ..models import AICoachSession, SessionStatus
from ..serializers import (
    AICoachSessionListSerializer,
    AICoachSessionDetailSerializer,
    AICoachSessionCreateSerializer,
    AICoachSessionUpdateSerializer,
    AICoachSessionEndSerializer,
    AICoachSessionStatsSerializer,
)
from ..services import AICoachService


class AICoachSessionViewSet(ModelViewSet):
    """
    ViewSet for managing AI coaching sessions.
    
    Provides:
    - List user's coaching sessions
    - Create new coaching sessions
    - Retrieve session details
    - Update session metadata
    - End coaching sessions
    - Get user coaching statistics
    """
    
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['session_type', 'status']
    search_fields = ['summary', 'goals_discussed']
    ordering_fields = ['start_time', 'end_time', 'session_duration_minutes']
    ordering = ['-start_time']
    
    def get_queryset(self):
        """Get sessions for the current user"""
        return AICoachSession.objects.filter(
            user=self.request.user
        ).select_related('user').prefetch_related(
            'chat_messages',
            'recommendations'
        )
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return AICoachSessionListSerializer
        elif self.action == 'create':
            return AICoachSessionCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return AICoachSessionUpdateSerializer
        elif self.action == 'end_session':
            return AICoachSessionEndSerializer
        elif self.action == 'stats':
            return AICoachSessionStatsSerializer
        else:
            return AICoachSessionDetailSerializer
    
    def create(self, request, *args, **kwargs):
        """
        Start a new AI coaching session.
        
        The session will be automatically created with the current user
        and an initial AI greeting will be generated.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        session = serializer.save()
        
        # Return detailed session data
        detail_serializer = AICoachSessionDetailSerializer(
            session, 
            context={'request': request}
        )
        
        return Response(
            detail_serializer.data,
            status=status.HTTP_201_CREATED
        )
    
    @action(detail=True, methods=['post'])
    def end_session(self, request, pk=None):
        """
        End an active coaching session.
        
        Accepts optional feedback and satisfaction score.
        """
        session = self.get_object()
        
        if session.status != SessionStatus.ACTIVE:
            return Response(
                {'error': 'Session is not active'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(
            data=request.data,
            context={'session': session}
        )
        serializer.is_valid(raise_exception=True)
        
        result = serializer.save()
        
        return Response({
            'message': 'Session ended successfully',
            'session_summary': result
        })
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Get user's coaching statistics.
        
        Returns comprehensive statistics about the user's
        coaching history and patterns.
        """
        ai_coach_service = AICoachService()
        stats = ai_coach_service.get_user_coaching_stats(request.user)
        
        serializer = self.get_serializer(stats)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def active_session(self, request):
        """
        Get the current active session if any.
        """
        try:
            active_session = self.get_queryset().get(
                status=SessionStatus.ACTIVE
            )
            serializer = AICoachSessionDetailSerializer(
                active_session,
                context={'request': request}
            )
            return Response(serializer.data)
            
        except AICoachSession.DoesNotExist:
            return Response(
                {'message': 'No active session found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """
        Get all messages for a specific session.
        """
        session = self.get_object()
        messages = session.chat_messages.all().order_by('timestamp')
        
        from ..serializers import ChatMessageListSerializer
        serializer = ChatMessageListSerializer(
            messages,
            many=True,
            context={'request': request}
        )
        
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def recommendations(self, request, pk=None):
        """
        Get all recommendations generated during this session.
        """
        session = self.get_object()
        recommendations = session.recommendations.all().order_by('-created')
        
        from ..serializers import AIRecommendationListSerializer
        serializer = AIRecommendationListSerializer(
            recommendations,
            many=True,
            context={'request': request}
        )
        
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def rate_session(self, request, pk=None):
        """
        Rate a completed session.
        """
        session = self.get_object()
        
        if session.status != SessionStatus.COMPLETED:
            return Response(
                {'error': 'Can only rate completed sessions'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        rating = request.data.get('rating')
        feedback = request.data.get('feedback', '')
        
        if not rating or not (1 <= int(rating) <= 5):
            return Response(
                {'error': 'Rating must be between 1 and 5'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        session.user_satisfaction_score = rating
        session.save(update_fields=['user_satisfaction_score'])
        
        # Learn from feedback
        if feedback:
            from ..services import PersonalizationService
            personalization_service = PersonalizationService()
            
            personalization_service.learn_from_feedback(
                user=request.user,
                recommendation_type='session',
                feedback_type='positive' if int(rating) >= 4 else 'negative',
                feedback_data={'text': feedback, 'session_type': session.session_type},
                rating=int(rating)
            )
        
        return Response({
            'message': 'Session rated successfully',
            'rating': rating
        })


class AICoachSessionListCreateView(generics.ListCreateAPIView):
    """
    Alternative view for listing and creating sessions.
    Simpler than the ViewSet for basic operations.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['session_type', 'status']
    ordering = ['-start_time']
    
    def get_queryset(self):
        """Get sessions for the current user"""
        return AICoachSession.objects.filter(
            user=self.request.user
        ).select_related('user')
    
    def get_serializer_class(self):
        """Return appropriate serializer"""
        if self.request.method == 'POST':
            return AICoachSessionCreateSerializer
        return AICoachSessionListSerializer 