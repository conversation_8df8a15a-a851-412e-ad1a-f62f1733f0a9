"""
AI Chat Views

Views for handling AI chat interactions and conversations.
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django.db.models import Q

from ..models import ChatMessage, AICoachSession, SessionStatus, MessageSender
from ..serializers import (
    ChatMessageListSerializer,
    ChatMessageDetailSerializer,
    ChatMessageCreateSerializer,
    ChatMessageReplySerializer,
    ChatConversationSerializer,
    ChatMessageSearchSerializer,
    ChatMessageFlagSerializer,
    ChatMessageAnalyticsSerializer,
)
from ..services import ChatBotService


class AIChatViewSet(ModelViewSet):
    """
    ViewSet for AI chat interactions.
    
    Provides:
    - Send messages to AI coach
    - Get conversation history
    - Reply to specific messages
    - Search chat messages
    - Flag inappropriate content
    - Get conversation analytics
    """
    
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['sender', 'message_type', 'is_flagged']
    search_fields = ['message', 'intent_detected']
    ordering_fields = ['timestamp']
    ordering = ['timestamp']
    
    def get_queryset(self):
        """Get chat messages for the current user's sessions"""
        return ChatMessage.objects.filter(
            session__user=self.request.user
        ).select_related('session', 'parent_message')
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return ChatMessageCreateSerializer
        elif self.action == 'reply':
            return ChatMessageReplySerializer
        elif self.action == 'conversation':
            return ChatConversationSerializer
        elif self.action == 'search':
            return ChatMessageSearchSerializer
        elif self.action == 'flag':
            return ChatMessageFlagSerializer
        elif self.action == 'analytics':
            return ChatMessageAnalyticsSerializer
        elif self.action == 'list':
            return ChatMessageListSerializer
        else:
            return ChatMessageDetailSerializer
    
    def create(self, request, *args, **kwargs):
        """
        Send a message to the AI coach.
        
        Requires an active session or session_id parameter.
        """
        # Get or validate session
        session_id = request.data.get('session_id')
        if session_id:
            try:
                session = AICoachSession.objects.get(
                    id=session_id,
                    user=request.user
                )
            except AICoachSession.DoesNotExist:
                return Response(
                    {'error': 'Session not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # Get active session
            try:
                session = AICoachSession.objects.get(
                    user=request.user,
                    status=SessionStatus.ACTIVE
                )
            except AICoachSession.DoesNotExist:
                return Response(
                    {'error': 'No active session found. Please start a coaching session first.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        if session.status != SessionStatus.ACTIVE:
            return Response(
                {'error': 'Session is not active'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create message and get AI response
        serializer = self.get_serializer(
            data=request.data,
            context={'session': session, 'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        ai_message = serializer.save()
        
        if ai_message:
            response_serializer = ChatMessageDetailSerializer(
                ai_message,
                context={'request': request}
            )
            
            return Response(
                {
                    'message': 'Message sent successfully',
                    'ai_response': response_serializer.data
                },
                status=status.HTTP_201_CREATED
            )
        else:
            return Response(
                {'error': 'Failed to generate AI response'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def reply(self, request, pk=None):
        """
        Reply to a specific message.
        """
        parent_message = self.get_object()
        session = parent_message.session
        
        if session.user != request.user:
            return Response(
                {'error': 'Not authorized'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if session.status != SessionStatus.ACTIVE:
            return Response(
                {'error': 'Session is not active'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(
            data=request.data,
            context={'session': session, 'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        reply_message = serializer.save()
        
        response_serializer = ChatMessageDetailSerializer(
            reply_message,
            context={'request': request}
        )
        
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=False, methods=['get'])
    def conversation(self, request):
        """
        Get complete conversation for a session.
        """
        session_id = request.query_params.get('session_id')
        if not session_id:
            return Response(
                {'error': 'session_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            session = AICoachSession.objects.get(
                id=session_id,
                user=request.user
            )
        except AICoachSession.DoesNotExist:
            return Response(
                {'error': 'Session not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = self.get_serializer(session)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def search(self, request):
        """
        Search chat messages.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        query_data = serializer.validated_data
        
        # Build search query
        queryset = self.get_queryset()
        
        # Text search
        search_query = query_data['query']
        queryset = queryset.filter(
            Q(message__icontains=search_query) |
            Q(intent_detected__icontains=search_query)
        )
        
        # Apply filters
        if query_data.get('message_type'):
            queryset = queryset.filter(message_type=query_data['message_type'])
        
        if query_data.get('sender'):
            queryset = queryset.filter(sender=query_data['sender'])
        
        if query_data.get('session_id'):
            queryset = queryset.filter(session_id=query_data['session_id'])
        
        if query_data.get('date_from'):
            queryset = queryset.filter(timestamp__gte=query_data['date_from'])
        
        if query_data.get('date_to'):
            queryset = queryset.filter(timestamp__lte=query_data['date_to'])
        
        # Limit results
        queryset = queryset.order_by('-timestamp')[:50]
        
        result_serializer = ChatMessageListSerializer(
            queryset,
            many=True,
            context={'request': request}
        )
        
        return Response({
            'query': search_query,
            'results_count': len(result_serializer.data),
            'results': result_serializer.data
        })
    
    @action(detail=True, methods=['post'])
    def flag(self, request, pk=None):
        """
        Flag a message as inappropriate.
        """
        message = self.get_object()
        
        if message.session.user != request.user:
            return Response(
                {'error': 'Not authorized'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        flagged_message = serializer.save(message)
        
        response_serializer = ChatMessageDetailSerializer(
            flagged_message,
            context={'request': request}
        )
        
        return Response({
            'message': 'Message flagged successfully',
            'flagged_message': response_serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """
        Get chat analytics for the user.
        """
        messages = self.get_queryset()
        
        # Calculate analytics
        total_messages = messages.count()
        user_messages = messages.filter(sender=MessageSender.USER).count()
        ai_messages = messages.filter(sender=MessageSender.AI).count()
        
        # Average response time
        ai_msgs_with_time = messages.filter(
            sender=MessageSender.AI,
            ai_processing_time_ms__isnull=False
        )
        
        avg_response_time = 0
        if ai_msgs_with_time.exists():
            from django.db.models import Avg
            avg_response_time = ai_msgs_with_time.aggregate(
                avg=Avg('ai_processing_time_ms')
            )['avg'] or 0
        
        # Most common intents
        intent_messages = messages.filter(
            intent_detected__isnull=False
        ).exclude(intent_detected='')
        
        intent_counts = {}
        for msg in intent_messages:
            intent = msg.intent_detected
            intent_counts[intent] = intent_counts.get(intent, 0) + 1
        
        most_common_intents = sorted(
            intent_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # Sentiment distribution
        sentiment_messages = messages.filter(
            sender=MessageSender.USER,
            sentiment_score__isnull=False
        )
        
        sentiment_dist = {'positive': 0, 'neutral': 0, 'negative': 0}
        for msg in sentiment_messages:
            score = float(msg.sentiment_score)
            if score > 0.3:
                sentiment_dist['positive'] += 1
            elif score < -0.3:
                sentiment_dist['negative'] += 1
            else:
                sentiment_dist['neutral'] += 1
        
        # Flagged messages
        flagged_count = messages.filter(is_flagged=True).count()
        
        # Conversation duration (approximate)
        if total_messages > 0:
            first_msg = messages.order_by('timestamp').first()
            last_msg = messages.order_by('-timestamp').first()
            
            if first_msg and last_msg:
                duration = last_msg.timestamp - first_msg.timestamp
                conversation_duration_minutes = int(duration.total_seconds() / 60)
            else:
                conversation_duration_minutes = 0
        else:
            conversation_duration_minutes = 0
        
        analytics = {
            'total_messages': total_messages,
            'user_messages': user_messages,
            'ai_messages': ai_messages,
            'average_response_time_ms': round(avg_response_time, 2),
            'most_common_intents': [
                {'intent': intent, 'count': count}
                for intent, count in most_common_intents
            ],
            'sentiment_distribution': sentiment_dist,
            'flagged_messages_count': flagged_count,
            'conversation_duration_minutes': conversation_duration_minutes
        }
        
        serializer = self.get_serializer(analytics)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def recent(self, request):
        """
        Get recent messages across all sessions.
        """
        recent_messages = self.get_queryset().order_by('-timestamp')[:20]
        
        serializer = ChatMessageListSerializer(
            recent_messages,
            many=True,
            context={'request': request}
        )
        
        return Response(serializer.data)


class AIChatCreateView(generics.CreateAPIView):
    """
    Simple view for sending messages to AI coach.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ChatMessageCreateSerializer
    
    def create(self, request, *args, **kwargs):
        """Send message to AI coach"""
        # Get active session
        try:
            session = AICoachSession.objects.get(
                user=request.user,
                status=SessionStatus.ACTIVE
            )
        except AICoachSession.DoesNotExist:
            return Response(
                {'error': 'No active session found. Please start a coaching session first.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(
            data=request.data,
            context={'session': session, 'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        ai_message = serializer.save()
        
        if ai_message:
            return Response(
                ChatMessageDetailSerializer(
                    ai_message,
                    context={'request': request}
                ).data,
                status=status.HTTP_201_CREATED
            )
        else:
            return Response(
                {'error': 'Failed to generate AI response'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 