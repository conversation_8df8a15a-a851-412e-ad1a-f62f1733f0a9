"""
Health Insights Views

Views for AI-powered health analysis and insights generation.
"""
from rest_framework import generics, status, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from ..models import AIRecommendation, AICoachSession, PersonalizationData
from ..services import RecommendationEngineService, PersonalizationService


class HealthInsightsView(APIView):
    """
    API view for generating AI-powered health insights and analysis.
    
    Provides:
    - Overall health assessment
    - Progress analysis
    - Carnivore diet insights
    - Personalized health recommendations
    - Trend analysis
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, format=None):
        """
        Get comprehensive health insights for the user.
        """
        try:
            insights = self._generate_health_insights(request.user)
            return Response(insights, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to generate health insights: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request, format=None):
        """
        Generate targeted health insights based on specific parameters.
        """
        focus_areas = request.data.get('focus_areas', [])
        time_period = request.data.get('time_period', 30)  # days
        include_recommendations = request.data.get('include_recommendations', True)
        
        try:
            insights = self._generate_targeted_insights(
                user=request.user,
                focus_areas=focus_areas,
                time_period=time_period,
                include_recommendations=include_recommendations
            )
            
            return Response(insights, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to generate targeted insights: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _generate_health_insights(self, user):
        """Generate comprehensive health insights for the user"""
        
        # Get user data
        personalization_service = PersonalizationService()
        user_profile = personalization_service.get_user_profile(user)
        
        # Get recent activity data
        recent_sessions = AICoachSession.objects.filter(
            user=user,
            start_time__gte=timezone.now() - timedelta(days=30)
        ).order_by('-start_time')
        
        recent_recommendations = AIRecommendation.objects.filter(
            user=user,
            created__gte=timezone.now() - timedelta(days=30)
        ).order_by('-created')
        
        # Generate insights
        insights = {
            'overview': self._generate_overview_insights(user, user_profile),
            'carnivore_journey': self._generate_carnivore_insights(user, user_profile),
            'progress_analysis': self._generate_progress_insights(user, recent_sessions),
            'recommendation_patterns': self._generate_recommendation_insights(recent_recommendations),
            'personalization_score': self._calculate_personalization_score(user_profile),
            'health_trends': self._generate_health_trends(user),
            'actionable_insights': self._generate_actionable_insights(user, user_profile),
            'generated_at': timezone.now().isoformat()
        }
        
        return insights
    
    def _generate_targeted_insights(self, user, focus_areas, time_period, include_recommendations):
        """Generate insights focused on specific areas"""
        
        # Filter data by time period
        cutoff_date = timezone.now() - timedelta(days=time_period)
        
        sessions = AICoachSession.objects.filter(
            user=user,
            start_time__gte=cutoff_date
        )
        
        recommendations = AIRecommendation.objects.filter(
            user=user,
            created__gte=cutoff_date
        )
        
        insights = {
            'time_period_days': time_period,
            'focus_areas': focus_areas,
            'insights': {}
        }
        
        # Generate insights for each focus area
        for area in focus_areas:
            if area == 'nutrition':
                insights['insights']['nutrition'] = self._generate_nutrition_insights(
                    user, recommendations, sessions
                )
            elif area == 'exercise':
                insights['insights']['exercise'] = self._generate_exercise_insights(
                    user, recommendations, sessions
                )
            elif area == 'adaptation':
                insights['insights']['adaptation'] = self._generate_adaptation_insights(
                    user, sessions
                )
            elif area == 'goals':
                insights['insights']['goals'] = self._generate_goals_insights(
                    user, recommendations, sessions
                )
        
        # Add recommendations if requested
        if include_recommendations:
            recommendation_service = RecommendationEngineService()
            new_recommendations = recommendation_service.generate_health_tips(
                user=user,
                focus_areas=focus_areas,
                limit=5
            )
            
            insights['new_recommendations'] = [
                {
                    'id': str(rec.id),
                    'title': rec.title,
                    'content': rec.content,
                    'confidence_score': float(rec.confidence_score),
                    'priority': rec.priority
                }
                for rec in new_recommendations
            ]
        
        return insights
    
    def _generate_overview_insights(self, user, user_profile):
        """Generate overview health insights"""
        
        # Basic user stats
        total_sessions = AICoachSession.objects.filter(user=user).count()
        total_recommendations = AIRecommendation.objects.filter(user=user).count()
        
        # Account age
        account_age_days = (timezone.now().date() - user.date_joined.date()).days
        
        # Activity level assessment
        recent_activity = AICoachSession.objects.filter(
            user=user,
            start_time__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        activity_level = 'low'
        if recent_activity >= 3:
            activity_level = 'high'
        elif recent_activity >= 1:
            activity_level = 'moderate'
        
        return {
            'account_age_days': account_age_days,
            'total_coaching_sessions': total_sessions,
            'total_recommendations_received': total_recommendations,
            'current_activity_level': activity_level,
            'health_goals_count': len(user_profile.get('goals', [])),
            'personalization_data_points': len(user_profile.get('preferences', {})),
            'summary': self._generate_overview_summary(
                account_age_days, total_sessions, activity_level
            )
        }
    
    def _generate_carnivore_insights(self, user, user_profile):
        """Generate carnivore diet specific insights"""
        
        # Get carnivore-related recommendations
        carnivore_recs = AIRecommendation.objects.filter(
            user=user,
            recommendation_type='carnivore_advice'
        )
        
        # Adaptation status based on user profile
        carnivore_experience = user_profile.get('carnivore_experience', 'beginner')
        
        # Common challenges and progress
        adaptation_challenges = self._assess_adaptation_challenges(user, carnivore_recs)
        
        return {
            'experience_level': carnivore_experience,
            'carnivore_recommendations_received': carnivore_recs.count(),
            'adaptation_status': self._assess_adaptation_status(user, user_profile),
            'common_challenges': adaptation_challenges,
            'success_indicators': self._identify_success_indicators(user, carnivore_recs),
            'next_steps': self._suggest_carnivore_next_steps(carnivore_experience, adaptation_challenges)
        }
    
    def _generate_progress_insights(self, user, recent_sessions):
        """Generate progress analysis insights"""
        
        if not recent_sessions.exists():
            return {
                'message': 'No recent activity to analyze',
                'suggestions': ['Start a coaching session to begin tracking progress']
            }
        
        # Session frequency analysis
        session_frequency = self._calculate_session_frequency(recent_sessions)
        
        # Satisfaction trends
        satisfaction_trend = self._analyze_satisfaction_trend(recent_sessions)
        
        # Engagement patterns
        engagement_analysis = self._analyze_engagement_patterns(recent_sessions)
        
        return {
            'session_frequency': session_frequency,
            'satisfaction_trend': satisfaction_trend,
            'engagement_analysis': engagement_analysis,
            'progress_summary': self._generate_progress_summary(
                session_frequency, satisfaction_trend, engagement_analysis
            )
        }
    
    def _generate_recommendation_insights(self, recent_recommendations):
        """Analyze recommendation patterns and effectiveness"""
        
        if not recent_recommendations.exists():
            return {'message': 'No recent recommendations to analyze'}
        
        # Recommendation type distribution
        type_distribution = {}
        for rec in recent_recommendations:
            rec_type = rec.get_recommendation_type_display()
            type_distribution[rec_type] = type_distribution.get(rec_type, 0) + 1
        
        # Acceptance and completion rates
        from ..models import RecommendationStatus
        
        total_recs = recent_recommendations.count()
        accepted_recs = recent_recommendations.filter(
            status__in=[RecommendationStatus.ACCEPTED, RecommendationStatus.COMPLETED]
        ).count()
        completed_recs = recent_recommendations.filter(
            status=RecommendationStatus.COMPLETED
        ).count()
        
        acceptance_rate = (accepted_recs / total_recs * 100) if total_recs > 0 else 0
        completion_rate = (completed_recs / total_recs * 100) if total_recs > 0 else 0
        
        # Average confidence and ratings
        confidence_scores = [
            float(rec.confidence_score) for rec in recent_recommendations 
            if rec.confidence_score
        ]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        rated_recs = recent_recommendations.filter(user_rating__isnull=False)
        avg_rating = 0
        if rated_recs.exists():
            ratings = [rec.user_rating for rec in rated_recs]
            avg_rating = sum(ratings) / len(ratings)
        
        return {
            'total_recommendations': total_recs,
            'type_distribution': type_distribution,
            'acceptance_rate': round(acceptance_rate, 1),
            'completion_rate': round(completion_rate, 1),
            'average_confidence': round(avg_confidence, 2),
            'average_user_rating': round(avg_rating, 1),
            'effectiveness_assessment': self._assess_recommendation_effectiveness(
                acceptance_rate, completion_rate, avg_rating
            )
        }
    
    def _calculate_personalization_score(self, user_profile):
        """Calculate how well the system knows the user"""
        
        score = 0
        max_score = 100
        
        # Data completeness (40 points)
        preferences_count = len(user_profile.get('preferences', {}))
        score += min(40, preferences_count * 4)  # 10 preferences = 40 points
        
        # Goal clarity (20 points)
        goals_count = len(user_profile.get('goals', []))
        score += min(20, goals_count * 5)  # 4 goals = 20 points
        
        # Behavioral data (20 points)
        behaviors_count = len(user_profile.get('behaviors', {}))
        score += min(20, behaviors_count * 4)  # 5 behaviors = 20 points
        
        # Feedback patterns (20 points)
        feedback_count = len(user_profile.get('feedback_patterns', {}))
        score += min(20, feedback_count * 4)  # 5 feedback patterns = 20 points
        
        return {
            'score': min(score, max_score),
            'level': self._get_personalization_level(score),
            'areas_for_improvement': self._identify_personalization_gaps(user_profile)
        }
    
    def _generate_health_trends(self, user):
        """Analyze health trends over time"""
        
        # This would be more sophisticated with actual health data
        # For now, using session and recommendation data as proxies
        
        weeks_data = []
        for week in range(4):  # Last 4 weeks
            start_date = timezone.now() - timedelta(weeks=week+1)
            end_date = timezone.now() - timedelta(weeks=week)
            
            week_sessions = AICoachSession.objects.filter(
                user=user,
                start_time__range=[start_date, end_date]
            )
            
            week_recs = AIRecommendation.objects.filter(
                user=user,
                created__range=[start_date, end_date]
            )
            
            # Calculate satisfaction for the week
            satisfied_sessions = week_sessions.filter(
                user_satisfaction_score__gte=4
            )
            satisfaction_rate = 0
            if week_sessions.count() > 0:
                satisfaction_rate = satisfied_sessions.count() / week_sessions.count() * 100
            
            weeks_data.append({
                'week': f'Week {4-week}',
                'sessions': week_sessions.count(),
                'recommendations': week_recs.count(),
                'satisfaction_rate': round(satisfaction_rate, 1)
            })
        
        return {
            'weekly_data': weeks_data,
            'trends_summary': self._analyze_trend_patterns(weeks_data)
        }
    
    def _generate_actionable_insights(self, user, user_profile):
        """Generate specific actionable insights"""
        
        insights = []
        
        # Check for missing goals
        if not user_profile.get('goals'):
            insights.append({
                'type': 'goal_setting',
                'title': 'Set Your Health Goals',
                'description': 'Define clear health goals to get more personalized recommendations',
                'action': 'Add health goals in your profile',
                'priority': 'high'
            })
        
        # Check for low activity
        recent_sessions = AICoachSession.objects.filter(
            user=user,
            start_time__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        if recent_sessions == 0:
            insights.append({
                'type': 'engagement',
                'title': 'Start Regular Coaching Sessions',
                'description': 'Regular interaction helps improve recommendations',
                'action': 'Schedule weekly coaching sessions',
                'priority': 'medium'
            })
        
        # Check for unused recommendations
        pending_recs = AIRecommendation.objects.filter(
            user=user,
            status='pending',
            created__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        if pending_recs > 5:
            insights.append({
                'type': 'recommendations',
                'title': 'Review Pending Recommendations',
                'description': f'You have {pending_recs} unreviewed recommendations',
                'action': 'Review and act on recommendations',
                'priority': 'medium'
            })
        
        return insights
    
    # Helper methods for analysis
    
    def _generate_overview_summary(self, account_age, sessions, activity_level):
        """Generate a summary of the user's overview"""
        
        if account_age < 7:
            return "Welcome to your health journey! You're just getting started."
        elif sessions < 5:
            return "You're building healthy habits. Keep up the regular engagement!"
        elif activity_level == 'high':
            return "Great engagement! You're actively working on your health goals."
        else:
            return "Consistent progress. Consider increasing your coaching frequency."
    
    def _assess_adaptation_status(self, user, user_profile):
        """Assess carnivore diet adaptation status"""
        
        experience = user_profile.get('carnivore_experience', 'beginner')
        
        if experience == 'beginner':
            return 'early_adaptation'
        elif experience == 'intermediate':
            return 'adapting_well'
        else:
            return 'well_adapted'
    
    def _assess_adaptation_challenges(self, user, carnivore_recs):
        """Identify common adaptation challenges"""
        
        # This would analyze user messages and feedback in a real implementation
        common_challenges = [
            'initial_fatigue',
            'digestive_adjustment',
            'social_situations',
            'meal_planning'
        ]
        
        # For now, return based on experience level
        return common_challenges[:2]  # First 2 challenges
    
    def _identify_success_indicators(self, user, carnivore_recs):
        """Identify signs of successful adaptation"""
        
        return [
            'stable_energy_levels',
            'improved_satiety',
            'simplified_meal_planning',
            'reduced_cravings'
        ]
    
    def _suggest_carnivore_next_steps(self, experience, challenges):
        """Suggest next steps for carnivore journey"""
        
        if experience == 'beginner':
            return [
                'Focus on meal timing and portion sizes',
                'Track how you feel during adaptation',
                'Stay hydrated with electrolytes'
            ]
        elif experience == 'intermediate':
            return [
                'Experiment with different cuts of meat',
                'Consider adding organ meats',
                'Fine-tune meal frequency'
            ]
        else:
            return [
                'Optimize for specific health goals',
                'Consider seasonal adjustments',
                'Share experience with community'
            ]
    
    def _calculate_session_frequency(self, sessions):
        """Calculate session frequency patterns"""
        
        if not sessions.exists():
            return {'frequency': 'none', 'pattern': 'irregular'}
        
        days_with_sessions = set()
        for session in sessions:
            days_with_sessions.add(session.start_time.date())
        
        frequency = len(days_with_sessions) / 30  # Per day over 30 days
        
        if frequency >= 0.5:
            return {'frequency': 'high', 'sessions_per_week': round(frequency * 7, 1)}
        elif frequency >= 0.2:
            return {'frequency': 'moderate', 'sessions_per_week': round(frequency * 7, 1)}
        else:
            return {'frequency': 'low', 'sessions_per_week': round(frequency * 7, 1)}
    
    def _analyze_satisfaction_trend(self, sessions):
        """Analyze user satisfaction trends"""
        
        rated_sessions = sessions.filter(user_satisfaction_score__isnull=False)
        
        if not rated_sessions.exists():
            return {'trend': 'no_data', 'message': 'No satisfaction ratings available'}
        
        scores = [s.user_satisfaction_score for s in rated_sessions.order_by('start_time')]
        
        if len(scores) < 2:
            return {'trend': 'insufficient_data', 'average': scores[0]}
        
        # Simple trend analysis
        recent_avg = sum(scores[-3:]) / len(scores[-3:])  # Last 3 scores
        overall_avg = sum(scores) / len(scores)
        
        if recent_avg > overall_avg + 0.5:
            trend = 'improving'
        elif recent_avg < overall_avg - 0.5:
            trend = 'declining'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'recent_average': round(recent_avg, 1),
            'overall_average': round(overall_avg, 1),
            'total_ratings': len(scores)
        }
    
    def _analyze_engagement_patterns(self, sessions):
        """Analyze user engagement patterns"""
        
        if not sessions.exists():
            return {'pattern': 'no_engagement'}
        
        # Analyze session durations
        durations = [s.session_duration_minutes for s in sessions if s.session_duration_minutes]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        # Analyze session types
        session_types = {}
        for session in sessions:
            session_type = session.get_session_type_display()
            session_types[session_type] = session_types.get(session_type, 0) + 1
        
        most_common_type = max(session_types.items(), key=lambda x: x[1])[0] if session_types else 'None'
        
        return {
            'average_session_duration': round(avg_duration, 1),
            'total_sessions': sessions.count(),
            'most_common_session_type': most_common_type,
            'session_type_distribution': session_types
        }
    
    def _generate_progress_summary(self, frequency, satisfaction, engagement):
        """Generate progress summary"""
        
        summaries = []
        
        if frequency['frequency'] == 'high':
            summaries.append("High engagement with regular sessions")
        elif frequency['frequency'] == 'low':
            summaries.append("Consider increasing session frequency")
        
        if satisfaction.get('trend') == 'improving':
            summaries.append("Satisfaction is improving over time")
        elif satisfaction.get('trend') == 'declining':
            summaries.append("May need to adjust coaching approach")
        
        return ' | '.join(summaries) if summaries else "Continue building consistent habits"
    
    def _assess_recommendation_effectiveness(self, acceptance_rate, completion_rate, avg_rating):
        """Assess how effective recommendations are"""
        
        if acceptance_rate >= 70 and completion_rate >= 50 and avg_rating >= 4:
            return 'highly_effective'
        elif acceptance_rate >= 50 and completion_rate >= 30 and avg_rating >= 3:
            return 'moderately_effective'
        else:
            return 'needs_improvement'
    
    def _get_personalization_level(self, score):
        """Get personalization level based on score"""
        
        if score >= 80:
            return 'excellent'
        elif score >= 60:
            return 'good'
        elif score >= 40:
            return 'fair'
        else:
            return 'basic'
    
    def _identify_personalization_gaps(self, user_profile):
        """Identify areas where personalization can be improved"""
        
        gaps = []
        
        if len(user_profile.get('preferences', {})) < 5:
            gaps.append('More food and lifestyle preferences needed')
        
        if len(user_profile.get('goals', [])) < 2:
            gaps.append('Additional health goals would help')
        
        if not user_profile.get('behaviors'):
            gaps.append('Behavioral pattern data is limited')
        
        return gaps
    
    def _analyze_trend_patterns(self, weeks_data):
        """Analyze patterns in weekly trend data"""
        
        if len(weeks_data) < 2:
            return "Insufficient data for trend analysis"
        
        # Compare first and last week
        first_week = weeks_data[0]
        last_week = weeks_data[-1]
        
        session_trend = "stable"
        if last_week['sessions'] > first_week['sessions']:
            session_trend = "increasing"
        elif last_week['sessions'] < first_week['sessions']:
            session_trend = "decreasing"
        
        satisfaction_trend = "stable"
        if last_week['satisfaction_rate'] > first_week['satisfaction_rate'] + 10:
            satisfaction_trend = "improving"
        elif last_week['satisfaction_rate'] < first_week['satisfaction_rate'] - 10:
            satisfaction_trend = "declining"
        
        return f"Sessions: {session_trend}, Satisfaction: {satisfaction_trend}"
    
    def _generate_nutrition_insights(self, user, recommendations, sessions):
        """Generate nutrition-specific insights"""
        
        nutrition_recs = recommendations.filter(
            recommendation_type__in=['meal_plan', 'recipe', 'carnivore_advice']
        )
        
        return {
            'nutrition_recommendations_count': nutrition_recs.count(),
            'most_common_advice': 'Focus on quality animal products',
            'adherence_patterns': 'Good consistency with meal timing',
            'suggestions': [
                'Continue emphasizing nutrient-dense options',
                'Consider seasonal meat variety'
            ]
        }
    
    def _generate_exercise_insights(self, user, recommendations, sessions):
        """Generate exercise-specific insights"""
        
        exercise_recs = recommendations.filter(recommendation_type='exercise')
        
        return {
            'exercise_recommendations_count': exercise_recs.count(),
            'focus_areas': ['strength training', 'recovery'],
            'suggestions': [
                'Maintain consistent strength training',
                'Prioritize recovery and sleep'
            ]
        }
    
    def _generate_adaptation_insights(self, user, sessions):
        """Generate carnivore adaptation insights"""
        
        return {
            'adaptation_stage': 'well_adapted',
            'key_milestones': [
                'Stable energy levels achieved',
                'Digestive system adapted',
                'Craving patterns normalized'
            ],
            'next_focus': 'Optimization for specific goals'
        }
    
    def _generate_goals_insights(self, user, recommendations, sessions):
        """Generate goal-related insights"""
        
        return {
            'goals_progress': 'on_track',
            'achieved_milestones': 2,
            'current_focus': 'Body composition improvement',
            'success_indicators': [
                'Consistent energy levels',
                'Improved satiety signals',
                'Better sleep quality'
            ]
        } 