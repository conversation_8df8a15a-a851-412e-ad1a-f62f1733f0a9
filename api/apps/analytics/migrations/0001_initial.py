# Generated by Django 4.2.23 on 2025-06-16 17:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="ABTestExperiment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("hypothesis", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("running", "Running"),
                            ("paused", "Paused"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateTimeField(blank=True, null=True)),
                ("end_date", models.DateTimeField(blank=True, null=True)),
                (
                    "target_audience_criteria",
                    models.J<PERSON>NField(blank=True, default=dict),
                ),
                ("sample_size", models.PositiveIntegerField(default=1000)),
                ("traffic_allocation", models.FloatField(default=100.0)),
                ("primary_metric", models.CharField(max_length=255)),
                ("secondary_metrics", models.JSONField(blank=True, default=list)),
                ("minimum_effect_size", models.FloatField(default=5.0)),
                ("confidence_level", models.FloatField(default=95.0)),
                ("equal_variant_split", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("exclude_outliers", models.BooleanField(default=True)),
                ("outlier_threshold", models.FloatField(default=2.0)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_ab_experiments",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AnalyticsSnapshot",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateTimeField()),
                (
                    "granularity",
                    models.CharField(
                        choices=[
                            ("hourly", "Hourly"),
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                        ],
                        max_length=20,
                    ),
                ),
                ("total_users", models.PositiveIntegerField(default=0)),
                ("active_users", models.PositiveIntegerField(default=0)),
                ("new_users", models.PositiveIntegerField(default=0)),
                ("returning_users", models.PositiveIntegerField(default=0)),
                ("total_content", models.PositiveIntegerField(default=0)),
                ("new_content", models.PositiveIntegerField(default=0)),
                ("content_views", models.PositiveIntegerField(default=0)),
                ("unique_content_views", models.PositiveIntegerField(default=0)),
                ("total_sessions", models.PositiveIntegerField(default=0)),
                ("avg_session_duration", models.FloatField(default=0.0)),
                ("page_views", models.PositiveIntegerField(default=0)),
                ("unique_page_views", models.PositiveIntegerField(default=0)),
                ("bounce_rate", models.FloatField(default=0.0)),
                ("total_interactions", models.PositiveIntegerField(default=0)),
                ("likes", models.PositiveIntegerField(default=0)),
                ("shares", models.PositiveIntegerField(default=0)),
                ("comments", models.PositiveIntegerField(default=0)),
                ("bookmarks", models.PositiveIntegerField(default=0)),
                ("avg_page_load_time", models.FloatField(default=0.0)),
                ("error_rate", models.FloatField(default=0.0)),
                ("search_queries", models.PositiveIntegerField(default=0)),
                ("search_success_rate", models.FloatField(default=0.0)),
                ("health_score", models.FloatField(default=0.0)),
                ("toxicity_level", models.FloatField(default=0.0)),
                ("moderation_actions", models.PositiveIntegerField(default=0)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "analytics_snapshots",
                "ordering": ["-date"],
                "indexes": [
                    models.Index(
                        fields=["date", "granularity"],
                        name="analytics_s_date_0846c3_idx",
                    ),
                    models.Index(
                        fields=["granularity", "date"],
                        name="analytics_s_granula_03a5a7_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="analytics_s_created_a5b61a_idx"
                    ),
                ],
                "unique_together": {("date", "granularity")},
            },
        ),
        migrations.CreateModel(
            name="ABTestVariant",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                ("configuration", models.JSONField(blank=True, default=dict)),
                ("is_control", models.BooleanField(default=False)),
                ("traffic_percentage", models.FloatField(default=50.0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "experiment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="variants",
                        to="analytics.abtestexperiment",
                    ),
                ),
            ],
            options={
                "db_table": "analytics_ab_variants",
                "ordering": ["is_control", "created_at"],
            },
        ),
        migrations.CreateModel(
            name="ABTestResult",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("insufficient_data", "Insufficient Data"),
                            ("no_significant_difference", "No Significant Difference"),
                            ("significant_winner", "Significant Winner"),
                            ("significant_loser", "Significant Loser"),
                            ("inconclusive", "Inconclusive"),
                        ],
                        max_length=30,
                    ),
                ),
                ("confidence_level", models.FloatField()),
                ("statistical_power", models.FloatField(blank=True, null=True)),
                ("sample_sizes", models.JSONField(blank=True, default=dict)),
                ("conversion_rates", models.JSONField(blank=True, default=dict)),
                ("lift_analysis", models.JSONField(blank=True, default=dict)),
                ("recommendation", models.TextField()),
                ("insights", models.JSONField(blank=True, default=list)),
                ("next_steps", models.TextField(blank=True)),
                ("analysis_date", models.DateTimeField(auto_now_add=True)),
                (
                    "secondary_metric_results",
                    models.JSONField(blank=True, default=dict),
                ),
                ("segment_analysis", models.JSONField(blank=True, default=dict)),
                (
                    "experiment",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="result",
                        to="analytics.abtestexperiment",
                    ),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "winning_variant",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="won_experiments",
                        to="analytics.abtestvariant",
                    ),
                ),
            ],
            options={
                "db_table": "analytics_ab_results",
                "ordering": ["-analysis_date"],
            },
        ),
        migrations.CreateModel(
            name="ABTestAssignment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("session_id", models.CharField(blank=True, max_length=255, null=True)),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assignment_method",
                    models.CharField(default="random", max_length=50),
                ),
                ("matched_criteria", models.JSONField(blank=True, default=dict)),
                (
                    "experiment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="analytics.abtestexperiment",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "variant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="analytics.abtestvariant",
                    ),
                ),
            ],
            options={
                "db_table": "analytics_ab_assignments",
                "ordering": ["-assigned_at"],
            },
        ),
        migrations.CreateModel(
            name="UserEvent",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("session_id", models.CharField(max_length=255)),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("page_view", "Page View"),
                            ("click", "Click"),
                            ("search", "Search"),
                            ("content_view", "Content View"),
                            ("content_create", "Content Create"),
                            ("content_edit", "Content Edit"),
                            ("content_delete", "Content Delete"),
                            ("interaction", "Interaction"),
                            ("authentication", "Authentication"),
                            ("error", "Error"),
                            ("conversion", "Conversion"),
                            ("feature_usage", "Feature Usage"),
                        ],
                        max_length=50,
                    ),
                ),
                ("event_name", models.CharField(max_length=255)),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("properties", models.JSONField(blank=True, default=dict)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("user_agent", models.TextField(blank=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("referrer", models.URLField(blank=True)),
                ("page_url", models.URLField(blank=True)),
                ("page_load_time", models.FloatField(blank=True, null=True)),
                ("duration", models.FloatField(blank=True, null=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_user_events",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="analytics_u_user_id_3f64c0_idx",
                    ),
                    models.Index(
                        fields=["event_type", "timestamp"],
                        name="analytics_u_event_t_042b04_idx",
                    ),
                    models.Index(
                        fields=["session_id", "timestamp"],
                        name="analytics_u_session_bc430e_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="analytics_u_timesta_e148cd_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="UserEngagementMetrics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date", models.DateField()),
                ("sessions_count", models.PositiveIntegerField(default=0)),
                ("total_session_duration", models.FloatField(default=0.0)),
                ("avg_session_duration", models.FloatField(default=0.0)),
                ("bounce_rate", models.FloatField(default=0.0)),
                ("content_views", models.PositiveIntegerField(default=0)),
                ("unique_content_views", models.PositiveIntegerField(default=0)),
                ("content_created", models.PositiveIntegerField(default=0)),
                ("comments_made", models.PositiveIntegerField(default=0)),
                ("likes_given", models.PositiveIntegerField(default=0)),
                ("shares_made", models.PositiveIntegerField(default=0)),
                ("bookmarks_created", models.PositiveIntegerField(default=0)),
                ("features_used", models.JSONField(blank=True, default=dict)),
                ("search_queries", models.PositiveIntegerField(default=0)),
                ("engagement_score", models.FloatField(default=0.0)),
                ("quality_score", models.FloatField(default=0.0)),
                ("helpfulness_score", models.FloatField(default=0.0)),
                ("reputation_gained", models.IntegerField(default=0)),
                ("badges_earned", models.PositiveIntegerField(default=0)),
                ("achievements_unlocked", models.PositiveIntegerField(default=0)),
                ("activity_hours", models.JSONField(blank=True, default=dict)),
                ("preferred_content_types", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_user_engagement",
                "ordering": ["-date"],
                "indexes": [
                    models.Index(
                        fields=["user", "date"], name="analytics_u_user_id_c54e23_idx"
                    ),
                    models.Index(fields=["date"], name="analytics_u_date_76e879_idx"),
                    models.Index(
                        fields=["engagement_score"],
                        name="analytics_u_engagem_ff64ba_idx",
                    ),
                ],
                "unique_together": {("user", "date")},
            },
        ),
        migrations.CreateModel(
            name="SessionAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("session_id", models.CharField(max_length=255, unique=True)),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                ("duration", models.FloatField(blank=True, null=True)),
                ("device_type", models.CharField(blank=True, max_length=50)),
                ("browser", models.CharField(blank=True, max_length=100)),
                ("os", models.CharField(blank=True, max_length=100)),
                ("screen_resolution", models.CharField(blank=True, max_length=50)),
                ("country", models.CharField(blank=True, max_length=100)),
                ("region", models.CharField(blank=True, max_length=100)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("landing_page", models.URLField(blank=True)),
                ("exit_page", models.URLField(blank=True)),
                ("referrer", models.URLField(blank=True)),
                ("utm_source", models.CharField(blank=True, max_length=255)),
                ("utm_medium", models.CharField(blank=True, max_length=255)),
                ("utm_campaign", models.CharField(blank=True, max_length=255)),
                ("page_views", models.PositiveIntegerField(default=0)),
                ("unique_page_views", models.PositiveIntegerField(default=0)),
                ("events_triggered", models.PositiveIntegerField(default=0)),
                ("interactions_count", models.PositiveIntegerField(default=0)),
                ("goals_completed", models.JSONField(blank=True, default=list)),
                (
                    "conversion_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                ("bounce_rate", models.FloatField(blank=True, null=True)),
                ("engagement_score", models.FloatField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_session_analytics",
                "ordering": ["-started_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "started_at"],
                        name="analytics_s_user_id_865f55_idx",
                    ),
                    models.Index(
                        fields=["started_at"], name="analytics_s_started_65f1f1_idx"
                    ),
                    models.Index(
                        fields=["ended_at"], name="analytics_s_ended_a_92c0a4_idx"
                    ),
                    models.Index(
                        fields=["device_type"], name="analytics_s_device__951adb_idx"
                    ),
                    models.Index(
                        fields=["country"], name="analytics_s_country_126f2c_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ContentPerformanceMetrics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("object_id", models.PositiveIntegerField()),
                ("date", models.DateField()),
                ("total_views", models.PositiveIntegerField(default=0)),
                ("unique_views", models.PositiveIntegerField(default=0)),
                ("avg_view_duration", models.FloatField(default=0.0)),
                ("bounce_rate", models.FloatField(default=0.0)),
                ("scroll_depth", models.FloatField(default=0.0)),
                ("likes", models.PositiveIntegerField(default=0)),
                ("dislikes", models.PositiveIntegerField(default=0)),
                ("comments", models.PositiveIntegerField(default=0)),
                ("shares", models.PositiveIntegerField(default=0)),
                ("bookmarks", models.PositiveIntegerField(default=0)),
                ("organic_reach", models.PositiveIntegerField(default=0)),
                ("referral_traffic", models.PositiveIntegerField(default=0)),
                ("search_traffic", models.PositiveIntegerField(default=0)),
                ("social_traffic", models.PositiveIntegerField(default=0)),
                ("readability_score", models.FloatField(default=0.0)),
                ("sentiment_score", models.FloatField(default=0.0)),
                ("expertise_rating", models.FloatField(default=0.0)),
                ("helpfulness_rating", models.FloatField(default=0.0)),
                ("engagement_rate", models.FloatField(default=0.0)),
                ("viral_coefficient", models.FloatField(default=0.0)),
                ("performance_score", models.FloatField(default=0.0)),
                ("trending_score", models.FloatField(default=0.0)),
                ("click_through_rate", models.FloatField(default=0.0)),
                ("conversion_rate", models.FloatField(default=0.0)),
                ("goal_completions", models.PositiveIntegerField(default=0)),
                ("geographic_distribution", models.JSONField(blank=True, default=dict)),
                ("audience_demographics", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "db_table": "analytics_content_performance",
                "ordering": ["-date", "-performance_score"],
                "indexes": [
                    models.Index(
                        fields=["content_type", "object_id", "date"],
                        name="analytics_c_content_d2d743_idx",
                    ),
                    models.Index(fields=["date"], name="analytics_c_date_de15d9_idx"),
                    models.Index(
                        fields=["performance_score"],
                        name="analytics_c_perform_1099eb_idx",
                    ),
                    models.Index(
                        fields=["trending_score"], name="analytics_c_trendin_dd4ae7_idx"
                    ),
                ],
                "unique_together": {("content_type", "object_id", "date")},
            },
        ),
        migrations.CreateModel(
            name="ContentInteraction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("session_id", models.CharField(max_length=255)),
                ("object_id", models.PositiveIntegerField()),
                (
                    "interaction_type",
                    models.CharField(
                        choices=[
                            ("view", "View"),
                            ("like", "Like"),
                            ("dislike", "Dislike"),
                            ("share", "Share"),
                            ("bookmark", "Bookmark"),
                            ("comment", "Comment"),
                            ("reply", "Reply"),
                            ("follow", "Follow"),
                            ("unfollow", "Unfollow"),
                            ("report", "Report"),
                            ("download", "Download"),
                            ("print", "Print"),
                        ],
                        max_length=50,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("time_spent", models.FloatField(blank=True, null=True)),
                ("scroll_depth", models.FloatField(blank=True, null=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_content_interactions",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="analytics_c_user_id_110f20_idx",
                    ),
                    models.Index(
                        fields=["content_type", "object_id", "timestamp"],
                        name="analytics_c_content_7e93ad_idx",
                    ),
                    models.Index(
                        fields=["interaction_type", "timestamp"],
                        name="analytics_c_interac_7df347_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="analytics_c_timesta_f05f29_idx"
                    ),
                ],
                "unique_together": {
                    ("user", "content_type", "object_id", "interaction_type")
                },
            },
        ),
        migrations.AddIndex(
            model_name="abtestvariant",
            index=models.Index(
                fields=["experiment", "is_control"],
                name="analytics_a_experim_59f230_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="abtestresult",
            index=models.Index(
                fields=["experiment", "analysis_date"],
                name="analytics_a_experim_700f16_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="abtestresult",
            index=models.Index(fields=["status"], name="analytics_a_status_223fb3_idx"),
        ),
        migrations.AddIndex(
            model_name="abtestresult",
            index=models.Index(
                fields=["winning_variant"], name="analytics_a_winning_684abc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="abtestexperiment",
            index=models.Index(
                fields=["status", "start_date"], name="analytics_a_status_78b62a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="abtestexperiment",
            index=models.Index(
                fields=["created_by", "created_at"],
                name="analytics_a_created_28c472_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="abtestexperiment",
            index=models.Index(
                fields=["start_date", "end_date"], name="analytics_a_start_d_6a24e6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="abtestassignment",
            index=models.Index(
                fields=["experiment", "variant"], name="analytics_a_experim_e991ec_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="abtestassignment",
            index=models.Index(
                fields=["user", "experiment"], name="analytics_a_user_id_9bc98d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="abtestassignment",
            index=models.Index(
                fields=["session_id", "experiment"],
                name="analytics_a_session_cfc6f5_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="abtestassignment",
            index=models.Index(
                fields=["assigned_at"], name="analytics_a_assigne_e418e0_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="abtestassignment",
            unique_together={("user", "experiment"), ("session_id", "experiment")},
        ),
    ]
