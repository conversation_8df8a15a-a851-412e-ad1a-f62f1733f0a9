"""
Analytics URLs

URL configuration for analytics endpoints including dashboard data,
user engagement metrics, content performance, and A/B testing.
"""

from django.urls import path

from .views.analytics_overview_view import AnalyticsOverviewView
from .views.user_engagement_view import UserEngagementView
from .views.content_performance_view import ContentPerformanceView
from .views.community_health_view import CommunityHealthView
from .views.realtime_analytics_view import RealTimeAnalyticsView
from .views.analytics_export_view import AnalyticsExportView

from .views.ab_test_experiment_list_view import ABTestExperimentListView
from .views.ab_test_assignment_view import ABTestAssignmentView
from .views.ab_test_results_view import ABTestResultsView

from .views.event_tracking_view import EventTrackingView
from .views.user_event_list_view import UserEventListView
from .views.content_interaction_view import ContentInteractionView
from .views.session_analytics_view import SessionAnalyticsView

app_name = "analytics"

urlpatterns = [
    
    path("dashboard/", AnalyticsOverviewView.as_view(), name="analytics-dashboard"),
    path("overview/", AnalyticsOverviewView.as_view(), name="analytics-overview"),
    
    path("engagement/", UserEngagementView.as_view(), name="user-engagement"),
    path(
        "engagement/metrics/",
        UserEngagementView.as_view(),
        name="user-engagement-metrics",
    ),
    
    path(
        "content/performance/",
        ContentPerformanceView.as_view(),
        name="content-performance",
    ),
    path("content/metrics/", ContentPerformanceView.as_view(), name="content-metrics"),
    
    path("community/health/", CommunityHealthView.as_view(), name="community-health"),
    path("health/", CommunityHealthView.as_view(), name="forum-health"),
    
    path("realtime/", RealTimeAnalyticsView.as_view(), name="realtime-analytics"),
    path("live/", RealTimeAnalyticsView.as_view(), name="live-analytics"),
    
    path("export/", AnalyticsExportView.as_view(), name="analytics-export"),
    
    path("events/", EventTrackingView.as_view(), name="event-tracking"),
    path("events/track/", EventTrackingView.as_view(), name="track-event"),
    path("events/list/", UserEventListView.as_view(), name="user-events"),
    
    path(
        "interactions/", ContentInteractionView.as_view(), name="content-interactions"
    ),
    path(
        "interactions/track/",
        ContentInteractionView.as_view(),
        name="track-interaction",
    ),
    
    path("sessions/", SessionAnalyticsView.as_view(), name="session-analytics"),
    path("sessions/track/", SessionAnalyticsView.as_view(), name="track-session"),
    
    path("ab-test/assign/", ABTestAssignmentView.as_view(), name="abtest-assignment"),
    path("ab-test/results/", ABTestResultsView.as_view(), name="abtest-results"),
    path("experiments/", ABTestExperimentListView.as_view(), name="abtest-experiments"),
    
    path("user-engagement/", UserEngagementView.as_view(), name="user-engagement-alt"),
    path(
        "content-performance/",
        ContentPerformanceView.as_view(),
        name="content-performance-alt",
    ),
    path(
        "community-health/", CommunityHealthView.as_view(), name="community-health-alt"
    ),
    path("real-time/", RealTimeAnalyticsView.as_view(), name="realtime-alt"),
]
