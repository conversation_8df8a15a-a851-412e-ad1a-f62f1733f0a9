from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
import uuid
from decimal import Decimal

User = get_user_model()


class AnalyticsSnapshot(models.Model):
    """Store periodic snapshots of analytics data"""
    
    GRANULARITY_CHOICES = [
        ('hourly', 'Hourly'),
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    date = models.DateTimeField()
    granularity = models.CharField(max_length=20, choices=GRANULARITY_CHOICES)
    
    # User metrics
    total_users = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    new_users = models.PositiveIntegerField(default=0)
    returning_users = models.PositiveIntegerField(default=0)
    
    # Content metrics
    total_content = models.PositiveIntegerField(default=0)
    new_content = models.PositiveIntegerField(default=0)
    content_views = models.PositiveIntegerField(default=0)
    unique_content_views = models.PositiveIntegerField(default=0)
    
    # Engagement metrics
    total_sessions = models.PositiveIntegerField(default=0)
    avg_session_duration = models.FloatField(default=0.0)
    page_views = models.PositiveIntegerField(default=0)
    unique_page_views = models.PositiveIntegerField(default=0)
    bounce_rate = models.FloatField(default=0.0)
    
    # Interaction metrics
    total_interactions = models.PositiveIntegerField(default=0)
    likes = models.PositiveIntegerField(default=0)
    shares = models.PositiveIntegerField(default=0)
    comments = models.PositiveIntegerField(default=0)
    bookmarks = models.PositiveIntegerField(default=0)
    
    # Performance metrics
    avg_page_load_time = models.FloatField(default=0.0)
    error_rate = models.FloatField(default=0.0)
    search_queries = models.PositiveIntegerField(default=0)
    search_success_rate = models.FloatField(default=0.0)
    
    # Community health
    health_score = models.FloatField(default=0.0)
    toxicity_level = models.FloatField(default=0.0)
    moderation_actions = models.PositiveIntegerField(default=0)
    
    # Additional data
    metadata = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'analytics_snapshots'
        indexes = [
            models.Index(fields=['date', 'granularity']),
            models.Index(fields=['granularity', 'date']),
            models.Index(fields=['created_at']),
        ]
        unique_together = [['date', 'granularity']]
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.granularity.title()} snapshot for {self.date.date()}"


class UserEngagementMetrics(models.Model):
    """Store user-specific engagement metrics"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    date = models.DateField()
    
    # Session metrics
    sessions_count = models.PositiveIntegerField(default=0)
    total_session_duration = models.FloatField(default=0.0)
    avg_session_duration = models.FloatField(default=0.0)
    bounce_rate = models.FloatField(default=0.0)
    
    # Content interaction
    content_views = models.PositiveIntegerField(default=0)
    unique_content_views = models.PositiveIntegerField(default=0)
    content_created = models.PositiveIntegerField(default=0)
    comments_made = models.PositiveIntegerField(default=0)
    likes_given = models.PositiveIntegerField(default=0)
    shares_made = models.PositiveIntegerField(default=0)
    bookmarks_created = models.PositiveIntegerField(default=0)
    
    # Feature usage
    features_used = models.JSONField(default=dict, blank=True)  # {feature_name: usage_count}
    search_queries = models.PositiveIntegerField(default=0)
    
    # Engagement quality
    engagement_score = models.FloatField(default=0.0)
    quality_score = models.FloatField(default=0.0)
    helpfulness_score = models.FloatField(default=0.0)
    
    # Progression metrics
    reputation_gained = models.IntegerField(default=0)
    badges_earned = models.PositiveIntegerField(default=0)
    achievements_unlocked = models.PositiveIntegerField(default=0)
    
    # Behavioral patterns
    activity_hours = models.JSONField(default=dict, blank=True)  # {hour: activity_count}
    preferred_content_types = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'analytics_user_engagement'
        indexes = [
            models.Index(fields=['user', 'date']),
            models.Index(fields=['date']),
            models.Index(fields=['engagement_score']),
        ]
        unique_together = [['user', 'date']]
        ordering = ['-date']
    
    def __str__(self):
        return f"Engagement metrics for {self.user.username} on {self.date}"
    
    def calculate_engagement_score(self) -> float:
        """Calculate comprehensive engagement score"""
        
        # Time engagement (0-30 points)
        time_score = min(self.avg_session_duration / 300, 1.0) * 30  # 5+ minutes = max points
        
        # Content engagement (0-25 points)
        content_score = min(
            (self.content_views + self.comments_made + self.likes_given) / 20, 1.0
        ) * 25
        
        # Creation activity (0-20 points)
        creation_score = min(self.content_created / 5, 1.0) * 20  # 5+ pieces = max points
        
        # Social engagement (0-15 points)
        social_score = min((self.shares_made + self.bookmarks_created) / 10, 1.0) * 15
        
        # Quality multiplier (0.5 - 1.5)
        quality_multiplier = 0.5 + (self.quality_score / 100)
        
        total_score = (time_score + content_score + creation_score + social_score) * quality_multiplier
        return round(min(total_score, 100), 2)


class ContentPerformanceMetrics(models.Model):
    """Store content-specific performance metrics"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Content reference
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    date = models.DateField()
    
    # View metrics
    total_views = models.PositiveIntegerField(default=0)
    unique_views = models.PositiveIntegerField(default=0)
    avg_view_duration = models.FloatField(default=0.0)
    bounce_rate = models.FloatField(default=0.0)
    scroll_depth = models.FloatField(default=0.0)
    
    # Engagement metrics
    likes = models.PositiveIntegerField(default=0)
    dislikes = models.PositiveIntegerField(default=0)
    comments = models.PositiveIntegerField(default=0)
    shares = models.PositiveIntegerField(default=0)
    bookmarks = models.PositiveIntegerField(default=0)
    
    # Reach metrics
    organic_reach = models.PositiveIntegerField(default=0)
    referral_traffic = models.PositiveIntegerField(default=0)
    search_traffic = models.PositiveIntegerField(default=0)
    social_traffic = models.PositiveIntegerField(default=0)
    
    # Quality indicators
    readability_score = models.FloatField(default=0.0)
    sentiment_score = models.FloatField(default=0.0)
    expertise_rating = models.FloatField(default=0.0)
    helpfulness_rating = models.FloatField(default=0.0)
    
    # Performance scores
    engagement_rate = models.FloatField(default=0.0)
    viral_coefficient = models.FloatField(default=0.0)
    performance_score = models.FloatField(default=0.0)
    trending_score = models.FloatField(default=0.0)
    
    # Conversion metrics
    click_through_rate = models.FloatField(default=0.0)
    conversion_rate = models.FloatField(default=0.0)
    goal_completions = models.PositiveIntegerField(default=0)
    
    # Geographic and demographic data
    geographic_distribution = models.JSONField(default=dict, blank=True)
    audience_demographics = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'analytics_content_performance'
        indexes = [
            models.Index(fields=['content_type', 'object_id', 'date']),
            models.Index(fields=['date']),
            models.Index(fields=['performance_score']),
            models.Index(fields=['trending_score']),
        ]
        unique_together = [['content_type', 'object_id', 'date']]
        ordering = ['-date', '-performance_score']
    
    def __str__(self):
        return f"Performance metrics for {self.content_object} on {self.date}"
    
    def calculate_performance_score(self) -> float:
        """Calculate comprehensive content performance score"""
        
        if self.total_views == 0:
            return 0.0
        
        # Engagement rate (0-40 points)
        total_engagements = self.likes + self.comments + self.shares + self.bookmarks
        engagement_rate = (total_engagements / self.total_views) * 100
        engagement_score = min(engagement_rate / 10, 1.0) * 40  # 10%+ engagement = max points
        
        # Quality score (0-30 points)
        quality_score = (
            self.readability_score * 0.3 +
            self.sentiment_score * 0.2 +
            self.expertise_rating * 0.3 +
            self.helpfulness_rating * 0.2
        ) * 0.3
        
        # Reach score (0-20 points)
        unique_view_rate = (self.unique_views / self.total_views) * 100
        reach_score = min(unique_view_rate / 80, 1.0) * 20  # 80%+ unique views = max points
        
        # Retention score (0-10 points)
        retention_score = (100 - self.bounce_rate) / 10
        retention_score = min(retention_score, 10)
        
        total_score = engagement_score + quality_score + reach_score + retention_score
        return round(min(total_score, 100), 2)
    
    def calculate_trending_score(self) -> float:
        """Calculate trending potential score"""
        
        # Recent engagement velocity
        recent_engagement_rate = self.engagement_rate
        
        # Social amplification
        social_score = (self.shares * 3 + self.likes) / max(self.total_views, 1) * 100
        
        # Viral coefficient contribution
        viral_contribution = self.viral_coefficient * 20
        
        # Time decay factor (content gets less trendy over time)
        days_old = (timezone.now().date() - self.date).days
        time_decay = max(0, 1 - (days_old / 30))  # Decay over 30 days
        
        trending_score = (recent_engagement_rate + social_score + viral_contribution) * time_decay
        return round(min(trending_score, 100), 2)
    
    def save(self, *args, **kwargs):
        # Calculate scores before saving
        self.performance_score = self.calculate_performance_score()
        self.trending_score = self.calculate_trending_score()
        
        # Calculate engagement rate
        if self.total_views > 0:
            total_engagements = self.likes + self.comments + self.shares + self.bookmarks
            self.engagement_rate = (total_engagements / self.total_views) * 100
        
        super().save(*args, **kwargs) 