from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid
import json

User = get_user_model()


class ABTestExperiment(models.Model):
    """A/B Test experiment configuration"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('running', 'Running'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField()
    hypothesis = models.TextField()
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Timing
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    
    # Targeting
    target_audience_criteria = models.JSONField(default=dict, blank=True)
    sample_size = models.PositiveIntegerField(default=1000)
    traffic_allocation = models.FloatField(default=100.0)  # Percentage of traffic to include
    
    # Success metrics
    primary_metric = models.CharField(max_length=255)
    secondary_metrics = models.JSONField(default=list, blank=True)
    minimum_effect_size = models.FloatField(default=5.0)  # Minimum % improvement to detect
    confidence_level = models.FloatField(default=95.0)  # Statistical confidence level
    
    # Experiment configuration
    equal_variant_split = models.BooleanField(default=True)
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Analysis settings
    exclude_outliers = models.BooleanField(default=True)
    outlier_threshold = models.FloatField(default=2.0)  # Standard deviations
    
    class Meta:
        db_table = 'analytics_ab_experiments'
        indexes = [
            models.Index(fields=['status', 'start_date']),
            models.Index(fields=['created_by', 'created_at']),
            models.Index(fields=['start_date', 'end_date']),
        ]
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.status})"
    
    @property
    def is_active(self):
        return self.status == 'running' and self.start_date <= timezone.now()
    
    @property
    def duration_days(self):
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days
        return None
    
    def get_variant_allocation(self):
        """Get traffic allocation for each variant"""
        variants = self.variants.all()
        if not variants:
            return {}
        
        if self.equal_variant_split:
            allocation_per_variant = 100.0 / len(variants)
            return {variant.id: allocation_per_variant for variant in variants}
        else:
            return {variant.id: variant.traffic_percentage for variant in variants}


class ABTestVariant(models.Model):
    """Individual variant in an A/B test"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    experiment = models.ForeignKey(ABTestExperiment, on_delete=models.CASCADE, related_name='variants')
    
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    
    # Configuration
    configuration = models.JSONField(default=dict, blank=True)
    is_control = models.BooleanField(default=False)
    traffic_percentage = models.FloatField(default=50.0)  # Used if not equal split
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'analytics_ab_variants'
        indexes = [
            models.Index(fields=['experiment', 'is_control']),
        ]
        ordering = ['is_control', 'created_at']  # Control variant first
    
    def __str__(self):
        control_tag = " (Control)" if self.is_control else ""
        return f"{self.name}{control_tag}"


class ABTestAssignment(models.Model):
    """Track user assignments to A/B test variants"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    experiment = models.ForeignKey(ABTestExperiment, on_delete=models.CASCADE)
    variant = models.ForeignKey(ABTestVariant, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=255, null=True, blank=True)
    
    # Assignment details
    assigned_at = models.DateTimeField(auto_now_add=True)
    assignment_method = models.CharField(max_length=50, default='random')  # random, targeted, etc.
    
    # Targeting criteria that matched
    matched_criteria = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'analytics_ab_assignments'
        indexes = [
            models.Index(fields=['experiment', 'variant']),
            models.Index(fields=['user', 'experiment']),
            models.Index(fields=['session_id', 'experiment']),
            models.Index(fields=['assigned_at']),
        ]
        # Ensure one assignment per user per experiment
        unique_together = [
            ['user', 'experiment'],
            ['session_id', 'experiment'],
        ]
        ordering = ['-assigned_at']
    
    def __str__(self):
        user_id = self.user.username if self.user else self.session_id
        return f"{user_id} assigned to {self.variant.name} in {self.experiment.name}"


class ABTestResult(models.Model):
    """Store A/B test results and statistical analysis"""
    
    RESULT_STATUS = [
        ('insufficient_data', 'Insufficient Data'),
        ('no_significant_difference', 'No Significant Difference'),
        ('significant_winner', 'Significant Winner'),
        ('significant_loser', 'Significant Loser'),
        ('inconclusive', 'Inconclusive'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    experiment = models.OneToOneField(ABTestExperiment, on_delete=models.CASCADE, related_name='result')
    
    # Result summary
    status = models.CharField(max_length=30, choices=RESULT_STATUS)
    confidence_level = models.FloatField()
    statistical_power = models.FloatField(null=True, blank=True)
    
    # Winner information
    winning_variant = models.ForeignKey(
        ABTestVariant, 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='won_experiments'
    )
    
    # Statistical data
    sample_sizes = models.JSONField(default=dict, blank=True)  # {variant_id: sample_size}
    conversion_rates = models.JSONField(default=dict, blank=True)  # {variant_id: {rate, ci_lower, ci_upper}}
    lift_analysis = models.JSONField(default=dict, blank=True)  # {variant_id: {relative_lift, absolute_lift, p_value}}
    
    # Recommendations
    recommendation = models.TextField()
    insights = models.JSONField(default=list, blank=True)
    next_steps = models.TextField(blank=True)
    
    # Metadata
    analysis_date = models.DateTimeField(auto_now_add=True)
    generated_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    
    # Additional metrics
    secondary_metric_results = models.JSONField(default=dict, blank=True)
    segment_analysis = models.JSONField(default=dict, blank=True)
    
    class Meta:
        db_table = 'analytics_ab_results'
        indexes = [
            models.Index(fields=['experiment', 'analysis_date']),
            models.Index(fields=['status']),
            models.Index(fields=['winning_variant']),
        ]
        ordering = ['-analysis_date']
    
    def __str__(self):
        return f"Results for {self.experiment.name}: {self.get_status_display()}"
    
    def get_control_variant(self):
        """Get the control variant for this experiment"""
        return self.experiment.variants.filter(is_control=True).first()
    
    def get_treatment_variants(self):
        """Get all treatment (non-control) variants"""
        return self.experiment.variants.filter(is_control=False)
    
    def calculate_statistical_significance(self, variant_a_id, variant_b_id):
        """Calculate statistical significance between two variants"""
        # This would implement proper statistical tests (t-test, chi-square, etc.)
        # For now, returning placeholder logic
        
        if variant_a_id not in self.conversion_rates or variant_b_id not in self.conversion_rates:
            return False, 1.0
        
        rate_a = self.conversion_rates[variant_a_id]['rate']
        rate_b = self.conversion_rates[variant_b_id]['rate']
        
        # Simplified significance test (would need proper implementation)
        difference = abs(rate_a - rate_b)
        if difference > 2.0:  # 2% difference threshold
            return True, 0.05  # p-value
        
        return False, 0.5
    
    def generate_insights(self):
        """Generate insights based on the results"""
        insights = []
        
        control = self.get_control_variant()
        if not control:
            return insights
        
        control_rate = self.conversion_rates.get(str(control.id), {}).get('rate', 0)
        
        for variant in self.get_treatment_variants():
            variant_rate = self.conversion_rates.get(str(variant.id), {}).get('rate', 0)
            lift = ((variant_rate - control_rate) / control_rate * 100) if control_rate > 0 else 0
            
            if lift > 5:
                insights.append(f"{variant.name} shows {lift:.1f}% improvement over control")
            elif lift < -5:
                insights.append(f"{variant.name} shows {abs(lift):.1f}% decline from control")
            else:
                insights.append(f"{variant.name} performs similarly to control")
        
        return insights 