from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
import uuid
from typing import Dict, Any

User = get_user_model()


class UserEvent(models.Model):
    """Track all user events for analytics"""
    
    EVENT_TYPES = [
        ('page_view', 'Page View'),
        ('click', 'Click'),
        ('search', 'Search'),
        ('content_view', 'Content View'),
        ('content_create', 'Content Create'),
        ('content_edit', 'Content Edit'),
        ('content_delete', 'Content Delete'),
        ('interaction', 'Interaction'),
        ('authentication', 'Authentication'),
        ('error', 'Error'),
        ('conversion', 'Conversion'),
        ('feature_usage', 'Feature Usage'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=255)
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    event_name = models.CharField(max_length=255)
    
    # Generic foreign key for related objects
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Event data
    properties = models.JSONField(default=dict, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Context information
    user_agent = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    referrer = models.URLField(blank=True)
    page_url = models.URLField(blank=True)
    
    # Performance metrics
    page_load_time = models.FloatField(null=True, blank=True)
    duration = models.FloatField(null=True, blank=True)  # How long the event lasted
    
    class Meta:
        db_table = 'analytics_user_events'
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['session_id', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.event_type}: {self.event_name} by {self.user or 'Anonymous'}"


class ContentInteraction(models.Model):
    """Track interactions with specific content pieces"""
    
    INTERACTION_TYPES = [
        ('view', 'View'),
        ('like', 'Like'),
        ('dislike', 'Dislike'),
        ('share', 'Share'),
        ('bookmark', 'Bookmark'),
        ('comment', 'Comment'),
        ('reply', 'Reply'),
        ('follow', 'Follow'),
        ('unfollow', 'Unfollow'),
        ('report', 'Report'),
        ('download', 'Download'),
        ('print', 'Print'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=255)
    
    # Content being interacted with
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    interaction_type = models.CharField(max_length=50, choices=INTERACTION_TYPES)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Additional interaction data
    metadata = models.JSONField(default=dict, blank=True)
    
    # Engagement metrics
    time_spent = models.FloatField(null=True, blank=True)  # Time spent on content
    scroll_depth = models.FloatField(null=True, blank=True)  # Percentage of content scrolled
    
    class Meta:
        db_table = 'analytics_content_interactions'
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['content_type', 'object_id', 'timestamp']),
            models.Index(fields=['interaction_type', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]
        ordering = ['-timestamp']
        
        # Prevent duplicate interactions for some types
        unique_together = [
            ['user', 'content_type', 'object_id', 'interaction_type']
        ]
    
    def __str__(self):
        return f"{self.interaction_type} on {self.content_object} by {self.user or 'Anonymous'}"


class SessionAnalytics(models.Model):
    """Track user session analytics"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=255, unique=True)
    
    # Session timing
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    duration = models.FloatField(null=True, blank=True)  # Duration in seconds
    
    # Session details
    device_type = models.CharField(max_length=50, blank=True)
    browser = models.CharField(max_length=100, blank=True)
    os = models.CharField(max_length=100, blank=True)
    screen_resolution = models.CharField(max_length=50, blank=True)
    
    # Geographic data
    country = models.CharField(max_length=100, blank=True)
    region = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100, blank=True)
    
    # Entry/exit data
    landing_page = models.URLField(blank=True)
    exit_page = models.URLField(blank=True)
    referrer = models.URLField(blank=True)
    utm_source = models.CharField(max_length=255, blank=True)
    utm_medium = models.CharField(max_length=255, blank=True)
    utm_campaign = models.CharField(max_length=255, blank=True)
    
    # Engagement metrics
    page_views = models.PositiveIntegerField(default=0)
    unique_page_views = models.PositiveIntegerField(default=0)
    events_triggered = models.PositiveIntegerField(default=0)
    interactions_count = models.PositiveIntegerField(default=0)
    
    # Conversion tracking
    goals_completed = models.JSONField(default=list, blank=True)
    conversion_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Quality metrics
    bounce_rate = models.FloatField(null=True, blank=True)
    engagement_score = models.FloatField(null=True, blank=True)
    
    class Meta:
        db_table = 'analytics_session_analytics'
        indexes = [
            models.Index(fields=['user', 'started_at']),
            models.Index(fields=['started_at']),
            models.Index(fields=['ended_at']),
            models.Index(fields=['device_type']),
            models.Index(fields=['country']),
        ]
        ordering = ['-started_at']
    
    def __str__(self):
        return f"Session {self.session_id} by {self.user or 'Anonymous'}"
    
    @property
    def is_active(self):
        return self.ended_at is None
    
    def calculate_engagement_score(self) -> float:
        """Calculate engagement score based on session metrics"""
        if not self.duration or self.duration == 0:
            return 0.0
        
        # Base score from time spent
        time_score = min(self.duration / 300, 1.0) * 40  # Max 40 points for 5+ minutes
        
        # Score from page views
        page_score = min(self.page_views / 10, 1.0) * 20  # Max 20 points for 10+ pages
        
        # Score from interactions
        interaction_score = min(self.interactions_count / 5, 1.0) * 20  # Max 20 points for 5+ interactions
        
        # Score from goals
        goal_score = len(self.goals_completed) * 5  # 5 points per goal, max 20
        goal_score = min(goal_score, 20)
        
        total_score = time_score + page_score + interaction_score + goal_score
        return round(total_score, 2)
    
    def save(self, *args, **kwargs):
        # Calculate engagement score before saving
        if self.ended_at and self.duration:
            self.engagement_score = self.calculate_engagement_score()
        
        super().save(*args, **kwargs) 