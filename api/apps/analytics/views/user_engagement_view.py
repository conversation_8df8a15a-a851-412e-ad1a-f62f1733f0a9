"""
User Engagement View

User engagement metrics and analysis.
"""

from datetime import timedelta
from django.contrib.auth import get_user_model
from django.db.models import Count, Avg
from django.db import OperationalError
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.forum.posts.models import Post
from apps.forum.reactions.models import PostReaction


UserEvent = None
SessionAnalytics = None

try:
    from ..models import UserEvent, SessionAnalytics
except (ImportError, OperationalError):
    pass

User = get_user_model()


class UserEngagementView(RetrieveAPIView):
    """
    User engagement metrics and analysis
    """

    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        """Get user engagement metrics"""
        try:
            user_id = request.GET.get("user_id", str(request.user.id))
            timeframe = request.GET.get("timeframe", "30d")

            
            days = {"24h": 1, "7d": 7, "30d": 30, "90d": 90}.get(timeframe, 30)
            start_date = timezone.now() - timedelta(days=days)

            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response(
                    {"error": "User not found"}, status=status.HTTP_404_NOT_FOUND
                )

            
            total_sessions = 0
            avg_session_duration = 0

            if SessionAnalytics:
                try:
                    sessions = SessionAnalytics.objects.filter(
                        user=user, session_start__gte=start_date
                    )
                    total_sessions = sessions.count()
                    avg_session_duration = (
                        sessions.aggregate(avg_duration=Avg("session_duration"))[
                            "avg_duration"
                        ]
                        or 0
                    )
                except (OperationalError, AttributeError):
                    
                    pass

            
            posts_created = 0
            reactions_given = 0

            try:
                posts_created = Post.objects.filter(
                    author=user, created__gte=start_date
                ).count()

                reactions_given = PostReaction.objects.filter(
                    user=user, created__gte=start_date
                ).count()
            except (OperationalError, AttributeError):
                
                pass

            
            engagement_score = min(
                100,
                ((posts_created * 10) + (reactions_given * 2) + (total_sessions * 5))
                / max(1, days),
            )

            
            activity_heatmap = []

            if UserEvent:
                try:
                    events = UserEvent.objects.filter(
                        user=user, timestamp__gte=start_date
                    )

                    for hour in range(24):
                        for day in range(7):
                            activity_count = events.filter(
                                timestamp__hour=hour, timestamp__week_day=day + 1
                            ).count()

                            activity_heatmap.append(
                                {
                                    "hour": hour,
                                    "day_of_week": day,
                                    "activity_count": activity_count,
                                }
                            )
                except (OperationalError, AttributeError):
                    
                    for hour in range(24):
                        for day in range(7):
                            activity_heatmap.append(
                                {"hour": hour, "day_of_week": day, "activity_count": 0}
                            )
            else:
                
                for hour in range(24):
                    for day in range(7):
                        activity_heatmap.append(
                            {"hour": hour, "day_of_week": day, "activity_count": 0}
                        )

            data = {
                "user_id": user_id,
                "timeframe": timeframe,
                "session_metrics": {
                    "total_sessions": total_sessions,
                    "average_session_duration": round(avg_session_duration, 2),
                    "bounce_rate": 0,
                    "pages_per_session": 0,
                    "return_visitor_rate": 0,
                },
                "content_interaction": {
                    "posts_viewed": 0,
                    "posts_created": posts_created,
                    "comments_made": 0,
                    "reactions_given": reactions_given,
                    "bookmarks_created": 0,
                    "shares_made": 0,
                },
                "engagement_score": round(engagement_score, 2),
                "activity_heatmap": activity_heatmap,
                "feature_usage": {},
                "progression_metrics": {
                    "reputation_gained": 0,
                    "badges_earned": 0,
                    "achievements_unlocked": 0,
                    "skill_improvements": [],
                },
                "generated_at": timezone.now().isoformat(),
            }

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "Failed to fetch user engagement metrics", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
