"""
A/B Test Experiment List View

Provides endpoints for listing A/B test experiments.
"""

from rest_framework import status
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone


class ABTestExperimentListView(ListAPIView):
    """
    List all A/B test experiments
    """

    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """List all A/B test experiments"""
        try:
            data = [
                {
                    "id": "experiment_1",
                    "name": "Test Experiment",
                    "description": "Sample A/B test experiment",
                    "status": "draft",
                    "created": timezone.now().isoformat(),
                }
            ]

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "Failed to fetch A/B test experiments", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
