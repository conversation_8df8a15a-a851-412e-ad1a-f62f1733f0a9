"""
User Event List View

List and filter user events.
"""

from datetime import timedelta
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

User = get_user_model()


class UserEventListView(ListAPIView):
    """
    List and filter user events
    """

    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """Get filtered user events"""
        try:
            user_id = request.GET.get("user_id")
            event_types = request.GET.getlist("event_type")
            start_date = request.GET.get("start_date")
            end_date = request.GET.get("end_date")
            limit = int(request.GET.get("limit", 100))

            
            all_events = [
                {
                    "event_id": f"event_{i}",
                    "event_type": ["page_view", "click", "form_submit"][i % 3],
                    "user_id": str(user_id or request.user.id),
                    "properties": {
                        "page": f"/page/{i}",
                        "element": f"button_{i}",
                        "value": i,
                    },
                    "timestamp": (
                        timezone.now() - timedelta(minutes=i * 15)
                    ).isoformat(),
                    "session_id": f"session_{i//10}",
                }
                for i in range(50)
            ]

            
            filtered_events = all_events

            if user_id:
                filtered_events = [
                    e for e in filtered_events if e["user_id"] == str(user_id)
                ]

            if event_types:
                filtered_events = [
                    e for e in filtered_events if e["event_type"] in event_types
                ]

            
            filtered_events = filtered_events[:limit]

            
            event_summary = {}
            for event in filtered_events:
                event_type = event["event_type"]
                event_summary[event_type] = event_summary.get(event_type, 0) + 1

            return Response(
                {
                    "events": filtered_events,
                    "total_count": len(filtered_events),
                    "event_summary": event_summary,
                    "filters_applied": {
                        "user_id": user_id,
                        "event_types": event_types,
                        "start_date": start_date,
                        "end_date": end_date,
                        "limit": limit,
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"error": "Failed to fetch user events", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
