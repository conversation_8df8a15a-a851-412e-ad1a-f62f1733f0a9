"""
Real-time Analytics View

Real-time analytics and metrics.
"""

from datetime import timedelta
from django.contrib.auth import get_user_model
from django.db.models import Count
from django.db import OperationalError
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


UserEvent = None
SessionAnalytics = None

try:
    from ..models import UserEvent, SessionAnalytics
except (ImportError, OperationalError):
    pass

User = get_user_model()


class RealTimeAnalyticsView(RetrieveAPIView):
    """
    Real-time analytics and metrics
    """

    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        """Get real-time analytics data"""
        try:
            
            five_minutes_ago = timezone.now() - timedelta(minutes=5)

            
            active_users = 0
            try:
                active_users = User.objects.filter(
                    last_login__gte=five_minutes_ago
                ).count()
            except (OperationalError, AttributeError):
                
                pass

            
            current_sessions = 0
            if SessionAnalytics:
                try:
                    current_sessions = SessionAnalytics.objects.filter(
                        session_start__gte=five_minutes_ago, session_end__isnull=True
                    ).count()
                except (OperationalError, AttributeError):
                    
                    pass

            
            recent_events = 0
            events_per_minute = 0
            if UserEvent:
                try:
                    recent_events = UserEvent.objects.filter(
                        timestamp__gte=five_minutes_ago
                    ).count()
                    events_per_minute = round(recent_events / 5, 2)
                except (OperationalError, AttributeError):
                    
                    pass

            
            one_hour_ago = timezone.now() - timedelta(hours=1)
            top_pages = []

            
            recent_event_types = []
            if UserEvent:
                try:
                    recent_event_types = (
                        UserEvent.objects.filter(timestamp__gte=five_minutes_ago)
                        .values("event_type")
                        .annotate(count=Count("event_type"))
                        .order_by("-count")[:5]
                    )
                except (OperationalError, AttributeError):
                    
                    pass

            data = {
                "active_users": active_users,
                "current_sessions": current_sessions,
                "events_per_minute": events_per_minute,
                "top_pages": top_pages,
                "recent_events": [
                    {
                        "event": event.get("event_type", "unknown"),
                        "count": event.get("count", 0),
                    }
                    for event in recent_event_types
                ],
                "generated_at": timezone.now().isoformat(),
            }

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "Failed to fetch real-time analytics", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
