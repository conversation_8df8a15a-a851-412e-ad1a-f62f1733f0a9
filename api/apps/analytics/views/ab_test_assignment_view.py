"""
A/B Test Assignment View

Handle user assignment to A/B test variants.
"""

from rest_framework import status
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils import timezone


class ABTestAssignmentView(CreateAPIView):
    """
    Handle user assignment to A/B test variants
    """

    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """Assign user to A/B test variant"""
        try:
            experiment_id = request.data.get("experiment_id")

            if not experiment_id:
                return Response(
                    {"error": "experiment_id is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            data = {
                "variant_id": "test_variant_id",
                "assigned_at": timezone.now().isoformat(),
            }

            return Response(data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": "Failed to assign user to A/B test", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
