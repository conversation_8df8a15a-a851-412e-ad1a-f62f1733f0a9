"""
Community Health View

Community health metrics and indicators.
"""

from datetime import timedelta
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.forum.posts.models import Post
from apps.forum.topics.models import Topic
from apps.forum.reactions.models import PostReaction

User = get_user_model()


class CommunityHealthView(RetrieveAPIView):
    """
    Community health metrics and indicators
    """

    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        """Get community health metrics"""
        try:
            timeframe = request.GET.get("timeframe", "30d")

            
            days = {"24h": 1, "7d": 7, "30d": 30, "90d": 90}.get(timeframe, 30)
            start_date = timezone.now() - timedelta(days=days)

            
            total_users = User.objects.count()
            active_users = User.objects.filter(last_login__gte=start_date).count()
            new_users = User.objects.filter(created__gte=start_date).count()

            
            total_content = Post.objects.count() + Topic.objects.count()
            new_content = (
                Post.objects.filter(created__gte=start_date).count()
                + Topic.objects.filter(created__gte=start_date).count()
            )

            
            total_reactions = PostReaction.objects.filter(
                created__gte=start_date
            ).count()

            
            user_activity_score = min(25, (active_users / max(1, total_users)) * 100)
            content_creation_score = min(25, (new_content / max(1, days)) * 5)
            engagement_score = min(25, (total_reactions / max(1, new_content)) * 10)
            growth_score = min(25, (new_users / max(1, total_users)) * 100)

            health_score = (
                user_activity_score
                + content_creation_score
                + engagement_score
                + growth_score
            )

            data = {
                "timeframe": timeframe,
                "user_metrics": {
                    "total_users": total_users,
                    "active_users": active_users,
                    "new_users": new_users,
                    "returning_users": 0,
                    "user_retention_rate": 0,
                    "churn_rate": 0,
                },
                "content_metrics": {
                    "total_content": total_content,
                    "new_content": new_content,
                    "content_quality_score": 0,
                    "moderation_rate": 0,
                    "spam_detection_rate": 0,
                },
                "engagement_metrics": {
                    "average_session_duration": 0,
                    "posts_per_user": round(total_content / max(1, total_users), 2),
                    "comments_per_post": 0,
                    "reaction_rate": round(total_reactions / max(1, new_content), 2),
                    "share_rate": 0,
                },
                "community_sentiment": {
                    "overall_sentiment": 0,
                    "toxicity_level": 0,
                    "helpfulness_score": 0,
                    "collaboration_index": 0,
                },
                "growth_metrics": {
                    "user_growth_rate": round(
                        (new_users / max(1, total_users)) * 100, 2
                    ),
                    "content_growth_rate": round(
                        (new_content / max(1, total_content)) * 100, 2
                    ),
                    "engagement_growth_rate": 0,
                    "viral_coefficient": 0,
                },
                "health_score": round(health_score, 2),
                "generated_at": timezone.now().isoformat(),
            }

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "Failed to fetch community health metrics", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
