"""
Event Tracking View

Track user events and interactions.
"""

from datetime import <PERSON><PERSON><PERSON>
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import CreateAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

User = get_user_model()


class EventTrackingView(CreateAPIView, ListAPIView):
    """
    Track user events and interactions
    """

    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """Track a user event"""
        try:
            event_type = request.data.get("event_type")
            properties = request.data.get("properties", {})
            user_id = request.data.get("user_id", request.user.id)

            if not event_type:
                return Response(
                    {"error": "event_type is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            
            event_data = {
                "event_id": f"event_{timezone.now().timestamp()}",
                "event_type": event_type,
                "user_id": str(user_id),
                "properties": properties,
                "timestamp": timezone.now().isoformat(),
                "status": "recorded",
            }

            return Response(event_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": "Failed to track event", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def list(self, request, *args, **kwargs):
        """Get user events"""
        try:
            user_id = request.GET.get("user_id", request.user.id)
            event_type = request.GET.get("event_type")
            timeframe = request.GET.get("timeframe", "7d")

            
            days = {"24h": 1, "7d": 7, "30d": 30, "90d": 90}.get(timeframe, 7)
            start_date = timezone.now() - timedelta(days=days)

            
            events = [
                {
                    "event_id": f"event_{i}",
                    "event_type": "page_view",
                    "user_id": str(user_id),
                    "properties": {"page": f"/page/{i}"},
                    "timestamp": (
                        timezone.now() - timedelta(minutes=i * 30)
                    ).isoformat(),
                }
                for i in range(10)
            ]

            if event_type:
                events = [e for e in events if e["event_type"] == event_type]

            return Response(
                {"events": events, "total_count": len(events), "timeframe": timeframe},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"error": "Failed to fetch events", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
