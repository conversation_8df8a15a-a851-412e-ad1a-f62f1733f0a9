"""
Content Interaction View

Track and retrieve content interactions.
"""

from datetime import <PERSON><PERSON><PERSON>
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import CreateAPI<PERSON>iew, ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


class ContentInteractionView(CreateAPIView, ListAPIView):
    """
    Track and retrieve content interactions
    """

    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """Track content interaction"""
        try:
            content_id = request.data.get("content_id")
            content_type = request.data.get("content_type")
            interaction_type = request.data.get("interaction_type")
            duration = request.data.get("duration", 0)
            metadata = request.data.get("metadata", {})

            if not all([content_id, content_type, interaction_type]):
                return Response(
                    {
                        "error": "content_id, content_type, and interaction_type are required"
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            interaction_data = {
                "interaction_id": f"interaction_{timezone.now().timestamp()}",
                "content_id": content_id,
                "content_type": content_type,
                "interaction_type": interaction_type,
                "user_id": str(request.user.id),
                "duration": duration,
                "metadata": metadata,
                "timestamp": timezone.now().isoformat(),
            }

            return Response(interaction_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": "Failed to track content interaction", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def list(self, request, *args, **kwargs):
        """Get content interactions"""
        try:
            content_id = request.GET.get("content_id")
            content_type = request.GET.get("content_type")
            interaction_type = request.GET.get("interaction_type")
            timeframe = request.GET.get("timeframe", "7d")

            
            days = {"24h": 1, "7d": 7, "30d": 30, "90d": 90}.get(timeframe, 7)
            start_date = timezone.now() - timedelta(days=days)

            
            interactions = []
            for i in range(20):
                interaction = {
                    "interaction_id": f"interaction_{i}",
                    "content_id": content_id or f"content_{i%5}",
                    "content_type": content_type or "post",
                    "interaction_type": interaction_type
                    or ["view", "like", "share"][i % 3],
                    "user_id": f"user_{i%10}",
                    "duration": (i + 1) * 30,
                    "timestamp": (
                        timezone.now() - timedelta(minutes=i * 20)
                    ).isoformat(),
                }
                interactions.append(interaction)

            
            if content_id:
                interactions = [
                    i for i in interactions if i["content_id"] == content_id
                ]
            if content_type:
                interactions = [
                    i for i in interactions if i["content_type"] == content_type
                ]
            if interaction_type:
                interactions = [
                    i for i in interactions if i["interaction_type"] == interaction_type
                ]

            
            total_interactions = len(interactions)
            unique_users = len(set(i["user_id"] for i in interactions))
            avg_duration = sum(i["duration"] for i in interactions) / max(
                1, total_interactions
            )

            interaction_types = {}
            for interaction in interactions:
                itype = interaction["interaction_type"]
                interaction_types[itype] = interaction_types.get(itype, 0) + 1

            return Response(
                {
                    "interactions": interactions,
                    "summary": {
                        "total_interactions": total_interactions,
                        "unique_users": unique_users,
                        "average_duration": round(avg_duration, 2),
                        "interaction_types": interaction_types,
                    },
                    "timeframe": timeframe,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"error": "Failed to fetch content interactions", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
