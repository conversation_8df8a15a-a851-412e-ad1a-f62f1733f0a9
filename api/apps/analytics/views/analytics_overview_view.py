"""
Analytics Overview View

Analytics dashboard overview with key metrics.
"""

from datetime import timedelta
from typing import Dict, Any

from django.contrib.auth import get_user_model
from django.db.models import Count, Avg, Q, Sum, F
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.forum.posts.models import Post
from apps.forum.topics.models import Topic
from apps.forum.reactions.models import PostReaction
from apps.forum.users.models import UserReputation
from ..models import (
    UserEvent,
    ContentInteraction,
    SessionAnalytics,
    AnalyticsSnapshot,
    UserEngagementMetrics,
    ContentPerformanceMetrics,
)

User = get_user_model()


class AnalyticsOverviewView(RetrieveAPIView):
    """
    Analytics dashboard overview with key metrics
    """

    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        """Get analytics overview for dashboard"""
        try:
            timeframe = request.GET.get("timeframe", "30d")

            
            days = {"24h": 1, "7d": 7, "30d": 30, "90d": 90}.get(timeframe, 30)
            start_date = timezone.now() - timedelta(days=days)

            
            total_users = User.objects.count()
            active_users = User.objects.filter(last_login__gte=start_date).count()
            new_users = User.objects.filter(created__gte=start_date).count()

            
            total_posts = Post.objects.count()
            new_posts = Post.objects.filter(created__gte=start_date).count()
            total_topics = Topic.objects.count()
            new_topics = Topic.objects.filter(created__gte=start_date).count()

            
            total_reactions = PostReaction.objects.count()
            new_reactions = PostReaction.objects.filter(created__gte=start_date).count()

            
            engagement_rate = 0
            if active_users > 0:
                engagement_rate = (new_reactions / active_users) * 100

            
            retention_users = User.objects.filter(
                last_login__gte=start_date,
                last_login__lt=timezone.now() - timedelta(days=days // 2),
            ).count()

            retention_rate = 0
            if active_users > 0:
                retention_rate = (retention_users / active_users) * 100

            data = {
                "timeframe": timeframe,
                "user_metrics": {
                    "total_users": total_users,
                    "active_users": active_users,
                    "new_users": new_users,
                    "retention_rate": round(retention_rate, 2),
                },
                "content_metrics": {
                    "total_posts": total_posts,
                    "new_posts": new_posts,
                    "total_topics": total_topics,
                    "new_topics": new_topics,
                    "content_per_user": (
                        round(total_posts / total_users, 2) if total_users > 0 else 0
                    ),
                },
                "engagement_metrics": {
                    "total_reactions": total_reactions,
                    "new_reactions": new_reactions,
                    "engagement_rate": round(engagement_rate, 2),
                    "avg_posts_per_user": (
                        round(new_posts / active_users, 2) if active_users > 0 else 0
                    ),
                },
                "growth_metrics": {
                    "user_growth_rate": (
                        round((new_users / total_users) * 100, 2)
                        if total_users > 0
                        else 0
                    ),
                    "content_growth_rate": (
                        round((new_posts / total_posts) * 100, 2)
                        if total_posts > 0
                        else 0
                    ),
                    "engagement_growth_rate": (
                        round((new_reactions / total_reactions) * 100, 2)
                        if total_reactions > 0
                        else 0
                    ),
                },
                "generated_at": timezone.now().isoformat(),
            }

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": "Failed to fetch analytics overview", "detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
