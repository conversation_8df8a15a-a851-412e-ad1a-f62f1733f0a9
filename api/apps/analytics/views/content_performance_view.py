"""
Content Performance View

Content performance analytics.
"""

from datetime import timed<PERSON><PERSON>
from django.db import OperationalError
from django.utils import timezone
from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.forum.posts.models import Post
from apps.forum.topics.models import Topic
from apps.forum.reactions.models import PostReaction


ContentInteraction = None

try:
    from ..models import ContentInteraction
except (ImportError, OperationalError):
    pass


class ContentPerformanceView(RetrieveAPIView):
    """
    Content performance analytics
    """

    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        """Get content performance metrics"""
        try:
            content_id = request.GET.get("content_id")
            content_type = request.GET.get("content_type", "post")
            timeframe = request.GET.get("timeframe", "30d")

            if not content_id:
                return Response(
                    {"error": "content_id parameter is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            
            days = {"24h": 1, "7d": 7, "30d": 30, "90d": 90}.get(timeframe, 30)
            start_date = timezone.now() - timedelta(days=days)

            
            if content_type == "post":
                try:
                    content = Post.objects.get(id=content_id)
                except Post.DoesNotExist:
                    return Response(
                        {"error": "Post not found"}, status=status.HTTP_404_NOT_FOUND
                    )
                except (ValueError, OperationalError):
                    
                    return Response(
                        {"error": "Post not found"}, status=status.HTTP_404_NOT_FOUND
                    )
            elif content_type == "topic":
                try:
                    content = Topic.objects.get(id=content_id)
                except Topic.DoesNotExist:
                    return Response(
                        {"error": "Topic not found"}, status=status.HTTP_404_NOT_FOUND
                    )
                except (ValueError, OperationalError):
                    
                    return Response(
                        {"error": "Topic not found"}, status=status.HTTP_404_NOT_FOUND
                    )
            else:
                return Response(
                    {"error": "Invalid content_type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            
            total_views = 0
            unique_views = 0

            if ContentInteraction:
                try:
                    interactions = ContentInteraction.objects.filter(
                        content_id=content_id,
                        content_type=content_type,
                        timestamp__gte=start_date,
                    )

                    
                    total_views = interactions.filter(interaction_type="view").count()
                    unique_views = (
                        interactions.filter(interaction_type="view")
                        .values("user")
                        .distinct()
                        .count()
                    )
                except (OperationalError, AttributeError):
                    
                    pass

            
            reaction_counts = {}
            try:
                reactions = PostReaction.objects.filter(
                    post_id=content_id, created__gte=start_date
                )

                for reaction in reactions:
                    reaction_type = reaction.emoji_native
                    reaction_counts[reaction_type] = (
                        reaction_counts.get(reaction_type, 0) + 1
                    )
            except (OperationalError, AttributeError, ValueError):
                
                pass

            
            performance_score = min(
                100,
                (
                    (total_views * 0.1)
                    + (unique_views * 0.5)
                    + (sum(reaction_counts.values()) * 2)
                ),
            )

            data = {
                "content_id": content_id,
                "content_type": content_type,
                "timeframe": timeframe,
                "view_metrics": {
                    "total_views": total_views,
                    "unique_views": unique_views,
                    "view_duration": 0,
                    "bounce_rate": 0,
                    "scroll_depth": 0,
                },
                "engagement_metrics": {
                    "reactions": reaction_counts,
                    "comments": 0,
                    "shares": 0,
                    "bookmarks": 0,
                    "mentions": 0,
                },
                "reach_metrics": {
                    "organic_reach": unique_views,
                    "viral_coefficient": 0,
                    "referral_sources": {},
                    "geographic_distribution": {},
                },
                "conversion_metrics": {
                    "click_through_rate": 0,
                    "conversion_rate": 0,
                    "goal_completions": 0,
                },
                "quality_indicators": {
                    "readability_score": 0,
                    "sentiment_score": 0,
                    "expertise_rating": 0,
                    "helpfulness_rating": 0,
                },
                "performance_score": round(performance_score, 2),
                "trending_potential": 0,
                "generated_at": timezone.now().isoformat(),
            }

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {
                    "error": "Failed to fetch content performance metrics",
                    "detail": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
