"""
Tests for A/B Test Views
"""

import json
from unittest.mock import patch, Mock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.accounts.user.tests.factories import UserFactory

User = get_user_model()


class ABTestViewsTestCase(TestCase):
    """Test cases for A/B Test Views"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)

    def test_ab_test_experiment_list_success(self):
        """Test successful A/B test experiment list retrieval"""
        url = reverse("analytics:abtest-experiments")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIsInstance(data, list)
        if data:  
            experiment = data[0]
            self.assertIn("id", experiment)
            self.assertIn("name", experiment)
            self.assertIn("description", experiment)
            self.assertIn("status", experiment)
            self.assertIn("created", experiment)

    def test_ab_test_assignment_success(self):
        """Test successful A/B test assignment"""
        url = reverse("analytics:abtest-assignment")
        payload = {"experiment_id": "test_experiment_123"}

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()

        self.assertIn("variant_id", data)
        self.assertIn("assigned_at", data)

    def test_ab_test_assignment_missing_experiment_id(self):
        """Test A/B test assignment without experiment_id"""
        url = reverse("analytics:abtest-assignment")
        payload = {}

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "experiment_id is required")

    def test_ab_test_results_success(self):
        """Test successful A/B test results retrieval"""
        url = reverse("analytics:abtest-results")
        response = self.client.get(url, {"experiment_id": "test_experiment_123"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        
        self.assertIn("experiment_id", data)
        self.assertIn("status", data)
        self.assertIn("confidence_level", data)
        self.assertIn("statistical_power", data)
        self.assertIn("sample_size_per_variant", data)
        self.assertIn("conversion_rates", data)
        self.assertIn("lift_analysis", data)
        self.assertIn("recommendation", data)
        self.assertIn("generated_at", data)

    def test_ab_test_results_missing_experiment_id(self):
        """Test A/B test results without experiment_id"""
        url = reverse("analytics:abtest-results")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "experiment_id parameter is required")

    def test_ab_test_unauthorized_access(self):
        """Test A/B test views without authentication"""
        self.client.force_authenticate(user=None)

        
        url = reverse("analytics:abtest-experiments")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        
        url = reverse("analytics:abtest-assignment")
        response = self.client.post(url, {})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        
        url = reverse("analytics:abtest-results")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch("apps.analytics.views.ab_test_experiment_list_view.timezone.now")
    def test_ab_test_views_error_handling(self, mock_timezone):
        """Test A/B test views error handling"""
        mock_timezone.side_effect = Exception("Time error")

        
        url = reverse("analytics:abtest-experiments")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)

        
        url = reverse("analytics:abtest-assignment")
        response = self.client.post(url, {"experiment_id": "test"}, format="json")
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)

        
        url = reverse("analytics:abtest-results")
        response = self.client.get(url, {"experiment_id": "test"})
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
