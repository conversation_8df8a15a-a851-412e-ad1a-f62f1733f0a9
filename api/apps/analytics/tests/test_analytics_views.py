"""
Tests for Analytics Views
"""

import json
from unittest.mock import patch, Mock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.accounts.user.tests.factories import UserFactory

User = get_user_model()



class MockUserEvent:
    objects = Mock()

    @classmethod
    def setup_mock(cls):
        cls.objects.filter.return_value.count.return_value = 0
        cls.objects.filter.return_value.filter.return_value.count.return_value = 0
        cls.objects.filter.return_value.values.return_value.annotate.return_value.order_by.return_value = (
            []
        )


class MockSessionAnalytics:
    objects = Mock()

    @classmethod
    def setup_mock(cls):
        cls.objects.filter.return_value.count.return_value = 0
        cls.objects.filter.return_value.aggregate.return_value = {"avg_duration": 0}


class MockContentInteraction:
    objects = Mock()

    @classmethod
    def setup_mock(cls):
        mock_qs = Mock()
        mock_qs.filter.return_value.count.return_value = 0
        mock_qs.filter.return_value.values.return_value.distinct.return_value.count.return_value = (
            0
        )
        cls.objects.filter.return_value = mock_qs


class AnalyticsViewsTestCase(TestCase):
    """Test cases for Analytics Views"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)

    @patch("apps.analytics.views.user_engagement_view.UserEvent", MockUserEvent)
    @patch(
        "apps.analytics.views.user_engagement_view.SessionAnalytics",
        MockSessionAnalytics,
    )
    def test_user_engagement_view_success(self):
        """Test successful user engagement metrics retrieval"""
        MockUserEvent.setup_mock()
        MockSessionAnalytics.setup_mock()

        url = reverse("analytics:user-engagement")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("user_id", data)
        self.assertIn("timeframe", data)
        self.assertIn("session_metrics", data)
        self.assertIn("content_interaction", data)
        self.assertIn("engagement_score", data)
        self.assertIn("activity_heatmap", data)
        self.assertIn("generated_at", data)

    @patch("apps.analytics.views.user_engagement_view.UserEvent", MockUserEvent)
    @patch(
        "apps.analytics.views.user_engagement_view.SessionAnalytics",
        MockSessionAnalytics,
    )
    def test_user_engagement_view_with_specific_user(self):
        """Test user engagement metrics for specific user"""
        MockUserEvent.setup_mock()
        MockSessionAnalytics.setup_mock()

        other_user = UserFactory()
        url = reverse("analytics:user-engagement")
        response = self.client.get(url, {"user_id": str(other_user.id)})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data["user_id"], str(other_user.id))

    @patch("apps.analytics.views.user_engagement_view.UserEvent", MockUserEvent)
    @patch(
        "apps.analytics.views.user_engagement_view.SessionAnalytics",
        MockSessionAnalytics,
    )
    def test_user_engagement_view_nonexistent_user(self):
        """Test user engagement metrics for nonexistent user"""
        MockUserEvent.setup_mock()
        MockSessionAnalytics.setup_mock()

        url = reverse("analytics:user-engagement")
        response = self.client.get(url, {"user_id": "99999"})

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "User not found")

    @patch(
        "apps.analytics.views.content_performance_view.ContentInteraction",
        MockContentInteraction,
    )
    @patch("apps.analytics.views.content_performance_view.Post.objects.get")
    def test_content_performance_view_success(self, mock_post_get):
        """Test successful content performance metrics retrieval"""
        MockContentInteraction.setup_mock()

        
        mock_post = Mock()
        mock_post.id = "post_123"
        mock_post_get.return_value = mock_post

        url = reverse("analytics:content-performance")
        response = self.client.get(
            url, {"content_id": "post_123", "content_type": "post"}
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("content_id", data)
        self.assertIn("content_type", data)
        self.assertIn("view_metrics", data)
        self.assertIn("engagement_metrics", data)
        self.assertIn("reach_metrics", data)
        self.assertIn("performance_score", data)

    def test_content_performance_view_missing_content_id(self):
        """Test content performance without content_id"""
        url = reverse("analytics:content-performance")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "content_id parameter is required")

    def test_content_performance_view_invalid_content_type(self):
        """Test content performance with invalid content_type"""
        url = reverse("analytics:content-performance")
        response = self.client.get(
            url, {"content_id": "content_123", "content_type": "invalid"}
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "Invalid content_type")

    @patch("apps.analytics.views.content_performance_view.Post.objects.get")
    def test_content_performance_view_nonexistent_post(self, mock_get):
        """Test content performance with nonexistent post"""
        from apps.forum.posts.models import Post

        mock_get.side_effect = Post.DoesNotExist

        url = reverse("analytics:content-performance")
        response = self.client.get(
            url, {"content_id": "nonexistent_post", "content_type": "post"}
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "Post not found")

    def test_community_health_view_success(self):
        """Test successful community health metrics retrieval"""
        url = reverse("analytics:community-health")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("timeframe", data)
        self.assertIn("user_metrics", data)
        self.assertIn("content_metrics", data)
        self.assertIn("engagement_metrics", data)
        self.assertIn("community_sentiment", data)
        self.assertIn("growth_metrics", data)
        self.assertIn("health_score", data)

    def test_community_health_view_with_timeframe(self):
        """Test community health metrics with different timeframes"""
        url = reverse("analytics:community-health")
        timeframes = ["24h", "7d", "30d", "90d"]

        for timeframe in timeframes:
            response = self.client.get(url, {"timeframe": timeframe})

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            data = response.json()
            self.assertEqual(data["timeframe"], timeframe)

    @patch("apps.analytics.views.realtime_analytics_view.UserEvent", MockUserEvent)
    @patch(
        "apps.analytics.views.realtime_analytics_view.SessionAnalytics",
        MockSessionAnalytics,
    )
    def test_realtime_analytics_view_success(self):
        """Test successful real-time analytics retrieval"""
        MockUserEvent.setup_mock()
        MockSessionAnalytics.setup_mock()

        url = reverse("analytics:realtime-analytics")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("active_users", data)
        self.assertIn("current_sessions", data)
        self.assertIn("events_per_minute", data)
        self.assertIn("top_pages", data)
        self.assertIn("recent_events", data)
        self.assertIn("generated_at", data)

    def test_analytics_export_view_success(self):
        """Test successful analytics export initiation"""
        url = reverse("analytics:analytics-export")
        payload = {
            "export_type": "json",
            "metrics": ["user_engagement", "content_performance"],
            "timeframe": "30d",
        }

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        data = response.json()

        self.assertIn("export_id", data)
        self.assertIn("status", data)
        self.assertIn("estimated_completion", data)
        self.assertIn("metrics_included", data)
        self.assertEqual(data["status"], "processing")

    def test_analytics_export_view_invalid_format(self):
        """Test analytics export with invalid format"""
        url = reverse("analytics:analytics-export")
        payload = {
            "export_type": "invalid_format",
            "metrics": ["user_engagement"],
            "timeframe": "30d",
        }

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)
        self.assertIn("Invalid export_type", data["error"])

    def test_analytics_export_view_valid_formats(self):
        """Test analytics export with all valid formats"""
        url = reverse("analytics:analytics-export")
        valid_formats = [
            "json",
            "csv",
            "excel",
        ]  

        for export_format in valid_formats:
            payload = {
                "export_type": export_format,
                "metrics": ["user_engagement"],
                "timeframe": "30d",
            }

            response = self.client.post(url, payload, format="json")

            self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
            data = response.json()
            self.assertEqual(data["status"], "processing")

    def test_analytics_views_unauthorized_access(self):
        """Test analytics views require authentication"""
        self.client.force_authenticate(user=None)

        urls = [
            reverse("analytics:user-engagement"),
            reverse("analytics:content-performance"),  
            reverse("analytics:community-health"),
            reverse("analytics:realtime-analytics"),
            reverse("analytics:analytics-export"),
        ]

        for url in urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_analytics_views_error_handling(self):
        """Test analytics views handle errors gracefully"""
        
        with patch("django.contrib.auth.get_user_model") as mock_user_model:
            mock_user_model.return_value.objects.filter.side_effect = Exception(
                "Database error"
            )

            url = reverse("analytics:realtime-analytics")
            response = self.client.get(url)

            
            self.assertIn(
                response.status_code,
                [status.HTTP_200_OK, status.HTTP_500_INTERNAL_SERVER_ERROR],
            )

    def test_analytics_views_metric_calculations(self):
        """Test analytics views perform correct metric calculations"""
        url = reverse("analytics:analytics-overview")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        
        self.assertGreaterEqual(data["user_metrics"]["total_users"], 0)
        self.assertGreaterEqual(data["engagement_metrics"]["engagement_rate"], 0)

    def test_analytics_views_timeframe_handling(self):
        """Test analytics views handle different timeframes correctly"""
        url = reverse("analytics:analytics-overview")
        timeframes = ["24h", "7d", "30d", "90d"]

        for timeframe in timeframes:
            response = self.client.get(url, {"timeframe": timeframe})

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            data = response.json()
            self.assertEqual(data["timeframe"], timeframe)
