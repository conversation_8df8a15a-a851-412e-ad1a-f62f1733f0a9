"""
Tests for Event Tracking Views
"""

import json
from unittest.mock import patch, Mock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from apps.accounts.user.tests.factories import UserFactory

User = get_user_model()


class EventTrackingViewsTestCase(TestCase):
    """Test cases for Event Tracking Views"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)

    def test_event_tracking_create_success(self):
        """Test successful event tracking creation"""
        url = reverse("analytics:event-tracking")
        payload = {
            "event_type": "page_view",
            "properties": {"page": "/test-page"},
            "user_id": str(self.user.id),
        }

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()

        self.assertIn("event_id", data)
        self.assertIn("event_type", data)
        self.assertIn("user_id", data)
        self.assertIn("properties", data)
        self.assertIn("timestamp", data)
        self.assertEqual(data["status"], "recorded")

    def test_event_tracking_missing_event_type(self):
        """Test event tracking without event_type"""
        url = reverse("analytics:event-tracking")
        payload = {"properties": {"page": "/test-page"}}

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "event_type is required")

    def test_event_tracking_list_success(self):
        """Test successful event tracking list retrieval"""
        url = reverse("analytics:event-tracking")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("events", data)
        self.assertIn("total_count", data)
        self.assertIn("timeframe", data)
        self.assertIsInstance(data["events"], list)

    def test_event_tracking_list_with_filters(self):
        """Test event tracking list with filters"""
        url = reverse("analytics:event-tracking")
        params = {
            "user_id": str(self.user.id),
            "event_type": "click",
            "timeframe": "7d",
        }

        response = self.client.get(url, params)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data["timeframe"], "7d")

    def test_user_event_list_success(self):
        """Test successful user event list retrieval"""
        url = reverse("analytics:user-events")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("events", data)
        self.assertIn("total_count", data)
        self.assertIn("event_summary", data)
        self.assertIn("filters_applied", data)

    def test_user_event_list_with_filters(self):
        """Test user event list with multiple filters"""
        url = reverse("analytics:user-events")
        params = {
            "user_id": str(self.user.id),
            "event_type": ["page_view", "click"],
            "limit": 50,
        }

        response = self.client.get(url, params)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        filters = data["filters_applied"]
        self.assertEqual(filters["user_id"], str(self.user.id))
        self.assertEqual(filters["limit"], 50)

    def test_content_interaction_create_success(self):
        """Test successful content interaction creation"""
        url = reverse("analytics:content-interactions")
        payload = {
            "content_id": "post_123",
            "content_type": "post",
            "interaction_type": "view",
            "duration": 30,
            "metadata": {"scroll_depth": 80},
        }

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()

        self.assertIn("interaction_id", data)
        self.assertIn("content_id", data)
        self.assertIn("content_type", data)
        self.assertIn("interaction_type", data)
        self.assertIn("user_id", data)

    def test_content_interaction_missing_required_fields(self):
        """Test content interaction without required fields"""
        url = reverse("analytics:content-interactions")
        payload = {
            "content_id": "post_123"
        }  

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)

    def test_content_interaction_list_success(self):
        """Test successful content interaction list retrieval"""
        url = reverse("analytics:content-interactions")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("interactions", data)
        self.assertIn("summary", data)
        self.assertIn("timeframe", data)

        summary = data["summary"]
        self.assertIn("total_interactions", summary)
        self.assertIn("unique_users", summary)
        self.assertIn("average_duration", summary)

    def test_session_analytics_create_success(self):
        """Test successful session analytics creation"""
        url = reverse("analytics:session-analytics")
        payload = {"session_id": "session_123", "action": "start", "page": "/dashboard"}

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()

        self.assertIn("session_id", data)
        self.assertIn("user_id", data)
        self.assertIn("action", data)
        self.assertIn("timestamp", data)

    def test_session_analytics_missing_session_id(self):
        """Test session analytics without session_id"""
        url = reverse("analytics:session-analytics")
        payload = {"action": "start"}

        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "session_id is required")

    def test_session_analytics_list_success(self):
        """Test successful session analytics list retrieval"""
        url = reverse("analytics:session-analytics")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn("sessions", data)
        self.assertIn("analytics", data)
        self.assertIn("timeframe", data)

        analytics = data["analytics"]
        self.assertIn("total_sessions", analytics)
        self.assertIn("average_duration", analytics)
        self.assertIn("bounce_rate", analytics)

    def test_event_tracking_unauthorized_access(self):
        """Test event tracking views without authentication"""
        self.client.force_authenticate(user=None)

        urls = [
            reverse("analytics:event-tracking"),
            reverse("analytics:user-events"),
            reverse("analytics:content-interactions"),
            reverse("analytics:session-analytics"),
        ]

        for url in urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

            response = self.client.post(url, {})
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
