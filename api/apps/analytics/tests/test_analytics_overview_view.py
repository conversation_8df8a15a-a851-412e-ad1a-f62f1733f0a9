"""
Tests for Analytics Overview View
"""

import json
from unittest.mock import patch, Mock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from apps.accounts.user.tests.factories import UserFactory

User = get_user_model()


class AnalyticsOverviewViewTestCase(TestCase):
    """Test cases for AnalyticsOverviewView"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
        self.url = reverse("analytics:analytics-overview")

    def test_get_analytics_overview_success(self):
        """Test successful analytics overview retrieval"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        
        self.assertIn("timeframe", data)
        self.assertIn("user_metrics", data)
        self.assertIn("content_metrics", data)
        self.assertIn("engagement_metrics", data)
        self.assertIn("growth_metrics", data)
        self.assertIn("generated_at", data)

        
        user_metrics = data["user_metrics"]
        self.assertIn("total_users", user_metrics)
        self.assertIn("active_users", user_metrics)
        self.assertIn("new_users", user_metrics)
        self.assertIn("retention_rate", user_metrics)

    def test_get_analytics_overview_with_timeframe(self):
        """Test analytics overview with different timeframes"""
        timeframes = ["24h", "7d", "30d", "90d"]

        for timeframe in timeframes:
            response = self.client.get(self.url, {"timeframe": timeframe})

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            data = response.json()
            self.assertEqual(data["timeframe"], timeframe)

    def test_get_analytics_overview_invalid_timeframe(self):
        """Test analytics overview with invalid timeframe defaults to 30d"""
        response = self.client.get(self.url, {"timeframe": "invalid"})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(
            data["timeframe"], "invalid"
        )  

    def test_get_analytics_overview_unauthorized(self):
        """Test analytics overview access without authentication"""
        self.client.force_authenticate(user=None)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    @patch("apps.analytics.views.analytics_overview_view.User.objects.count")
    def test_get_analytics_overview_database_error(self, mock_count):
        """Test analytics overview with database error"""
        mock_count.side_effect = Exception("Database error")

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        data = response.json()
        self.assertIn("error", data)
        self.assertEqual(data["error"], "Failed to fetch analytics overview")

    def test_get_analytics_overview_zero_division_protection(self):
        """Test analytics overview handles zero division properly"""
        
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        
        self.assertIsInstance(data["user_metrics"]["retention_rate"], (int, float))
        self.assertIsInstance(
            data["engagement_metrics"]["engagement_rate"], (int, float)
        )

    def test_analytics_overview_response_format(self):
        """Test analytics overview response format is correct"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        
        self.assertIsInstance(data["user_metrics"]["retention_rate"], (int, float))
        self.assertIsInstance(
            data["engagement_metrics"]["engagement_rate"], (int, float)
        )
        self.assertIsInstance(data["content_metrics"]["content_per_user"], (int, float))

        
        self.assertIsInstance(data["generated_at"], str)

    def test_analytics_overview_metrics_calculation(self):
        """Test analytics overview metrics are calculated correctly"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        
        self.assertGreaterEqual(data["user_metrics"]["total_users"], 0)
        self.assertGreaterEqual(data["user_metrics"]["active_users"], 0)
        self.assertGreaterEqual(data["content_metrics"]["total_posts"], 0)
        self.assertGreaterEqual(data["engagement_metrics"]["total_reactions"], 0)
