"""
Macro Calculation Active View

Get the active macro calculation for the authenticated user.
"""

from rest_framework import generics, permissions

from ..models import MacroCalculationModel
from ..serializers import MacroCalculationSerializer


class MacroCalculationActiveView(generics.RetrieveAPIView):
    """
    Get the active macro calculation for the authenticated user.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MacroCalculationSerializer
    
    def get_object(self):
        try:
            return MacroCalculationModel.objects.filter(
                user=self.request.user,
                is_active=True
            ).latest('created')
        except MacroCalculationModel.DoesNotExist:
            
            return MacroCalculationModel.objects.filter(
                user=self.request.user
            ).latest('created') 