from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from config.settings.config_loader import get_calculator_config


class MacroQuickCalculationView(APIView):
    """
    Quick macro calculation endpoint with centralized configuration limits.
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Calculate macros based on calories and percentages.
        """
        try:
            data = request.data
            calories = data.get("calories")
            
            if not calories:
                return Response(
                    {"error": "Calories field is required"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            calc_config = get_calculator_config()
            macro_config = calc_config.get("macro", {})
            min_calories = macro_config.get("min_calories", 800)
            max_calories = macro_config.get("max_calories", 8000)
            
            if not (min_calories <= int(calories) <= max_calories):
                return Response(
                    {"error": f"Calories must be between {min_calories} and {max_calories}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            protein_percentage = float(data.get("protein_percentage", 25))
            carbs_percentage = float(data.get("carbs_percentage", 45))
            fat_percentage = float(data.get("fat_percentage", 30))
            
            if abs((protein_percentage + carbs_percentage + fat_percentage) - 100) > 0.1:
                return Response(
                    {"error": "Macro percentages must sum to 100%"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            protein_calories = calories * (protein_percentage / 100)
            carbs_calories = calories * (carbs_percentage / 100)
            fat_calories = calories * (fat_percentage / 100)
            
            protein_grams = protein_calories / 4
            carbs_grams = carbs_calories / 4
            fat_grams = fat_calories / 9
            
            response_data = {
                "calories": calories,
                "protein": {
                    "grams": round(protein_grams, 1),
                    "calories": round(protein_calories, 1),
                    "percentage": protein_percentage
                },
                "carbs": {
                    "grams": round(carbs_grams, 1),
                    "calories": round(carbs_calories, 1),
                    "percentage": carbs_percentage
                },
                "fat": {
                    "grams": round(fat_grams, 1),
                    "calories": round(fat_calories, 1),
                    "percentage": fat_percentage
                },
                "limits": {
                    "min_calories": min_calories,
                    "max_calories": max_calories
                }
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except ValueError as e:
            return Response(
                {"error": f"Invalid input: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 