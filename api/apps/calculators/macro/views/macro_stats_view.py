"""
Macro Stats View

Get comprehensive statistics for the user's macro calculations.
"""

from decimal import Decimal
from django.db.models import Avg, Count
from django.utils import timezone
from datetime import timedelta

from rest_framework import generics, permissions
from rest_framework.response import Response

from ..models import MacroCalculationModel, MacroGoalModel


class MacroStatsView(generics.GenericAPIView):
    """
    Get comprehensive statistics for the user's macro calculations.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get macro calculator statistics"""
        user_calculations = MacroCalculationModel.objects.filter(user=request.user)
        user_goals = MacroGoalModel.objects.filter(user=request.user)
        
        if not user_calculations.exists():
            return Response({
                'total_calculations': 0,
                'total_goals': 0,
                'achieved_goals': 0,
                'average_calories': None,
                'average_protein': None,
                'average_carbs': None,
                'average_fat': None,
                'most_used_profile': None,
                'profile_distribution': {},
                'monthly_progress': [],
            })
        
        
        total_calculations = user_calculations.count()
        total_goals = user_goals.count()
        achieved_goals = user_goals.filter(is_achieved=True).count()
        
        
        averages = user_calculations.aggregate(
            avg_calories=Avg('daily_calories'),
            avg_protein=Avg('protein_grams'),
            avg_carbs=Avg('carb_grams'),
            avg_fat=Avg('fat_grams'),
        )
        
        
        profile_counts = user_calculations.values('macro_profile').annotate(
            count=Count('macro_profile')
        ).order_by('-count')
        most_used_profile = profile_counts.first()['macro_profile'] if profile_counts else None
        
        
        profile_distribution = {}
        for profile_data in profile_counts:
            profile_distribution[profile_data['macro_profile']] = profile_data['count']
        
        
        monthly_progress = []
        for i in range(6):
            month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
            month_end = month_start + timedelta(days=31)
            
            month_calcs = user_calculations.filter(
                created__gte=month_start,
                created__lt=month_end
            )
            
            if month_calcs.exists():
                month_averages = month_calcs.aggregate(
                    avg_calories=Avg('daily_calories'),
                    avg_protein=Avg('protein_grams'),
                    avg_carbs=Avg('carb_grams'),
                    avg_fat=Avg('fat_grams'),
                )
                monthly_progress.append({
                    'month': month_start.strftime('%Y-%m'),
                    'average_calories': int(month_averages['avg_calories']) if month_averages['avg_calories'] else None,
                    'average_protein': int(month_averages['avg_protein']) if month_averages['avg_protein'] else None,
                    'average_carbs': int(month_averages['avg_carbs']) if month_averages['avg_carbs'] else None,
                    'average_fat': int(month_averages['avg_fat']) if month_averages['avg_fat'] else None,
                    'calculation_count': month_calcs.count(),
                })
        
        monthly_progress.reverse()  
        
        return Response({
            'total_calculations': total_calculations,
            'total_goals': total_goals,
            'achieved_goals': achieved_goals,
            'average_calories': int(averages['avg_calories']) if averages['avg_calories'] else None,
            'average_protein': int(averages['avg_protein']) if averages['avg_protein'] else None,
            'average_carbs': int(averages['avg_carbs']) if averages['avg_carbs'] else None,
            'average_fat': int(averages['avg_fat']) if averages['avg_fat'] else None,
            'most_used_profile': most_used_profile,
            'profile_distribution': profile_distribution,
            'monthly_progress': monthly_progress,
        }) 