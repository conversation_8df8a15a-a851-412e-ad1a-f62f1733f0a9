"""
Macro Goal Achieve View

Mark a macro goal as achieved.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from ..models import MacroGoalModel
from ..serializers import MacroGoalSerializer


class MacroGoalAchieveView(generics.UpdateAPIView):
    """
    Mark a macro goal as achieved.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MacroGoalSerializer
    
    def get_queryset(self):
        return MacroGoalModel.objects.filter(user=self.request.user)
    
    def update(self, request, *args, **kwargs):
        """Mark the goal as achieved"""
        try:
            goal = self.get_object()
            
            if goal.is_achieved:
                return Response(
                    {'detail': _('Goal is already marked as achieved.')},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            goal.is_achieved = True
            goal.achieved_date = timezone.now().date()
            goal.save()
            
            serializer = self.get_serializer(goal)
            return Response(serializer.data)
            
        except MacroGoalModel.DoesNotExist:
            return Response(
                {'detail': _('Goal not found.')},
                status=status.HTTP_404_NOT_FOUND
            ) 