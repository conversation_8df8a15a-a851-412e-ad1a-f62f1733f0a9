"""
Macro Goal List Create View

List and create macro goals for authenticated users.
"""

from rest_framework import generics, permissions

from ..models import MacroGoalModel
from ..serializers import (
    MacroGoalSerializer,
    MacroGoalCreateSerializer,
)


class MacroGoalListCreateView(generics.ListCreateAPIView):
    """
    List and create macro goals for authenticated users.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return MacroGoalModel.objects.filter(
            user=self.request.user
        ).order_by('-created')
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return MacroGoalCreateSerializer
        return MacroGoalSerializer 