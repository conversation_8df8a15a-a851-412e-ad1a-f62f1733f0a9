"""
Macro Recommendations View

Get personalized macro recommendations based on user profile and goals.
"""

from rest_framework import generics, permissions
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import MacroCalculationModel, MacroProfile


class MacroRecommendationsView(generics.GenericAPIView):
    """
    Get personalized macro recommendations based on user profile and goals.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """Get macro recommendations"""
        
        latest_calc = MacroCalculationModel.objects.filter(
            user=request.user
        ).order_by('-created').first()
        
        
        daily_calories = int(request.data.get('daily_calories', 
                                            latest_calc.daily_calories if latest_calc else 2000))
        weight = float(request.data.get('weight', 
                                      latest_calc.weight if latest_calc else 70))
        activity_goal = request.data.get('activity_goal', 
                                       latest_calc.activity_goal if latest_calc else 'general_fitness')
        
        
        profile_recommendations = {}
        
        for profile_choice in MacroProfile.choices:
            profile = profile_choice[0]
            profile_name = profile_choice[1]
            
            
            carb_ratio, protein_ratio, fat_ratio = MacroCalculationModel.get_profile_ratios(
                profile, activity_goal
            )
            
            
            macros = MacroCalculationModel.calculate_macros(
                daily_calories, carb_ratio, protein_ratio, fat_ratio
            )
            
            
            protein_per_kg = MacroCalculationModel.get_recommended_protein_per_kg(activity_goal)
            recommended_protein = weight * protein_per_kg
            
            profile_recommendations[profile] = {
                'name': profile_name,
                'ratios': {
                    'carb_percentage': round(carb_ratio * 100, 1),
                    'protein_percentage': round(protein_ratio * 100, 1),
                    'fat_percentage': round(fat_ratio * 100, 1),
                },
                'grams': {
                    'carb_grams': macros['carb_grams'],
                    'protein_grams': macros['protein_grams'],
                    'fat_grams': macros['fat_grams'],
                },
                'calories': {
                    'carb_calories': macros['carb_calories'],
                    'protein_calories': macros['protein_calories'],
                    'fat_calories': macros['fat_calories'],
                },
                'protein_per_kg': round(macros['protein_grams'] / weight, 1),
                'meets_protein_needs': macros['protein_grams'] >= recommended_protein * 0.9,
            }
        
        
        activity_recommendations = {
            'sedentary': {
                'recommended_profiles': ['balanced', 'low_carb'],
                'protein_focus': 'Moderate protein for basic needs',
                'carb_focus': 'Lower carbs due to reduced activity',
                'fat_focus': 'Moderate fat for hormone production',
            },
            'weight_loss': {
                'recommended_profiles': ['high_protein', 'low_carb'],
                'protein_focus': 'Higher protein to preserve muscle during deficit',
                'carb_focus': 'Reduced carbs to promote fat burning',
                'fat_focus': 'Moderate fat for satiety and hormones',
            },
            'muscle_gain': {
                'recommended_profiles': ['high_protein', 'bulking'],
                'protein_focus': 'High protein for muscle protein synthesis',
                'carb_focus': 'Adequate carbs for training fuel',
                'fat_focus': 'Moderate fat for hormone production',
            },
            'athletic_performance': {
                'recommended_profiles': ['athlete', 'high_protein'],
                'protein_focus': 'High protein for recovery and adaptation',
                'carb_focus': 'Higher carbs for performance fuel',
                'fat_focus': 'Moderate fat for long-term energy',
            },
            'endurance': {
                'recommended_profiles': ['athlete', 'balanced'],
                'protein_focus': 'Moderate-high protein for recovery',
                'carb_focus': 'High carbs for glycogen storage',
                'fat_focus': 'Moderate fat for aerobic metabolism',
            },
            'strength': {
                'recommended_profiles': ['high_protein', 'cutting'],
                'protein_focus': 'Very high protein for strength gains',
                'carb_focus': 'Moderate carbs for training intensity',
                'fat_focus': 'Lower fat to allow more protein/carbs',
            },
            'general_fitness': {
                'recommended_profiles': ['balanced', 'high_protein'],
                'protein_focus': 'Adequate protein for general health',
                'carb_focus': 'Balanced carbs for energy and recovery',
                'fat_focus': 'Balanced fat for overall health',
            },
        }
        
        current_activity_rec = activity_recommendations.get(activity_goal, activity_recommendations['general_fitness'])
        
        
        general_tips = [
            _('Protein needs increase with activity level and age'),
            _('Carbohydrate timing around workouts can improve performance'),
            _('Healthy fats support hormone production and nutrient absorption'),
            _('Fiber intake should be 25-35g per day from carbohydrate sources'),
            _('Hydration needs increase with higher protein intake'),
            _('Individual tolerance to different macro ratios varies'),
            _('Consistency is more important than perfection'),
        ]
        
        return Response({
            'profile_recommendations': profile_recommendations,
            'activity_specific': current_activity_rec,
            'recommended_protein_per_kg': MacroCalculationModel.get_recommended_protein_per_kg(activity_goal),
            'general_tips': general_tips,
            'meal_timing_tips': [
                _('Eat protein with each meal for optimal muscle protein synthesis'),
                _('Include carbohydrates before and after workouts'),
                _('Distribute protein evenly throughout the day'),
                _('Include healthy fats with meals for satiety'),
            ],
        }) 