"""
Macro Calculation Detail View

Retrieve, update, or delete a specific macro calculation.
"""

from rest_framework import generics, permissions

from ..models import MacroCalculationModel
from ..serializers import MacroCalculationSerializer


class MacroCalculationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific macro calculation.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MacroCalculationSerializer
    
    def get_queryset(self):
        return MacroCalculationModel.objects.filter(user=self.request.user) 