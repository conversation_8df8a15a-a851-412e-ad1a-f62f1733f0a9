"""
Macro Calculator Views

This module provides API views for macronutrient calculations,
following Django REST framework best practices.
"""

from .macro_calculation_list_create_view import MacroCalculationListCreateView
from .macro_calculation_detail_view import MacroCalculationDetailView
from .macro_calculation_active_view import MacroCalculationActiveView
from .macro_goal_list_create_view import MacroGoalListCreateView
from .macro_goal_detail_view import MacroGoalDetailView
from .macro_goal_achieve_view import MacroGoalAchieveView
from .macro_stats_view import MacroStatsView
from .macro_recommendations_view import MacroRecommendationsView
from .macro_quick_calculation_view import MacroQuickCalculationView

__all__ = [
    'MacroCalculationListCreateView',
    'MacroCalculationDetailView',
    'MacroCalculationActiveView',
    'MacroGoalListCreateView',
    'MacroGoalDetailView',
    'MacroGoalAchieveView',
    'MacroStatsView',
    'MacroRecommendationsView',
    'MacroQuickCalculationView',
] 