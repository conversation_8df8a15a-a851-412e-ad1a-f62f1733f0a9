"""
Macro Goal Detail View

Retrieve, update, or delete a specific macro goal.
"""

from rest_framework import generics, permissions

from ..models import MacroGoalModel
from ..serializers import MacroGoalSerializer


class MacroGoalDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific macro goal.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MacroGoalSerializer
    
    def get_queryset(self):
        return MacroGoalModel.objects.filter(user=self.request.user) 