"""
Macro Calculation List Create View

List and create macro calculations for authenticated users.
"""

from rest_framework import generics, permissions

from ..models import MacroCalculationModel
from ..serializers import (
    MacroCalculationSerializer,
    MacroCalculationCreateSerializer,
)


class MacroCalculationListCreateView(generics.ListCreateAPIView):
    """
    List and create macro calculations for authenticated users.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return MacroCalculationModel.objects.filter(
            user=self.request.user
        ).order_by('-created')
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return MacroCalculationCreateSerializer
        return MacroCalculationSerializer
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user) 