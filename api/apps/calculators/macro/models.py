from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth import get_user_model
from core.abstract.models import AbstractAutoIncrementModel
from config.settings.config_loader import get_calculator_config

User = get_user_model()

calc_config = get_calculator_config()
macro_config = calc_config.get("macro", {})

MIN_CALORIES = macro_config.get("min_calories", 800)
MAX_CALORIES = macro_config.get("max_calories", 8000)


class MacroCalculationModel(AbstractAutoIncrementModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="macro_calculations")
    calories = models.IntegerField(
        validators=[MinValueValidator(MIN_CALORIES), MaxValueValidator(MAX_CALORIES)],
        default=2000
    )
    protein_grams = models.FloatField(validators=[MinValueValidator(0)], default=150.0)
    carbs_grams = models.FloatField(validators=[MinValueValidator(0)], default=200.0)
    fat_grams = models.FloatField(validators=[MinValueValidator(0)], default=65.0)
    protein_percentage = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], default=30.0)
    carbs_percentage = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], default=40.0)
    fat_percentage = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], default=30.0)

    class Meta:
        db_table = "macro_calculations"
        ordering = ["-created"]

    def __str__(self):
        return f"Macro calculation for {self.user.username} - {self.calories} calories"


class MacroGoalModel(AbstractAutoIncrementModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="macro_goal")
    target_calories = models.IntegerField(
        validators=[MinValueValidator(MIN_CALORIES), MaxValueValidator(MAX_CALORIES)],
        default=2000
    )
    target_protein_grams = models.FloatField(validators=[MinValueValidator(0)], default=150.0)
    target_carbs_grams = models.FloatField(validators=[MinValueValidator(0)], default=200.0)
    target_fat_grams = models.FloatField(validators=[MinValueValidator(0)], default=65.0)
    target_protein_percentage = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], default=30.0)
    target_carbs_percentage = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], default=40.0)
    target_fat_percentage = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], default=30.0)
    
    class Meta:
        db_table = "macro_goals"
    
    def __str__(self):
        return f"Macro goal for {self.user.username} - {self.target_calories} calories"


class MacroProfile(AbstractAutoIncrementModel):
    """
    User macro profile for recommendations and tracking.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="macro_profile")
    age = models.IntegerField(validators=[MinValueValidator(10), MaxValueValidator(120)], default=30)
    gender = models.CharField(
        max_length=10, 
        choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other')],
        default='male'
    )
    activity_level = models.CharField(
        max_length=20,
        choices=[
            ('sedentary', 'Sedentary'),
            ('lightly_active', 'Lightly Active'),
            ('moderately_active', 'Moderately Active'),
            ('very_active', 'Very Active'),
            ('extra_active', 'Extra Active'),
        ],
        default='moderately_active'
    )
    weight_kg = models.FloatField(validators=[MinValueValidator(30), MaxValueValidator(300)], default=70.0)
    height_cm = models.FloatField(validators=[MinValueValidator(100), MaxValueValidator(250)], default=170.0)
    goal = models.CharField(
        max_length=20,
        choices=[
            ('lose_weight', 'Lose Weight'),
            ('maintain_weight', 'Maintain Weight'),
            ('gain_weight', 'Gain Weight'),
            ('build_muscle', 'Build Muscle'),
        ],
        default='maintain_weight'
    )
    
    class Meta:
        db_table = "macro_profiles"
    
    def __str__(self):
        return f"Macro profile for {self.user.username}" 