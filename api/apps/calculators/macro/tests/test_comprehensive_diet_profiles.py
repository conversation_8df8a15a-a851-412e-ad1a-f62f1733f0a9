"""
Comprehensive tests for all macro diet profiles.
Tests the 500 error fix and validates all diet types are working correctly.
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from decimal import Decimal

from apps.calculators.macro.models import MacroCalculationModel, MacroProfile
from apps.accounts.user.tests.factories import UserFactory

User = get_user_model()


class ComprehensiveDietProfilesTestCase(TestCase):
    """Test all macro diet profiles for correctness and completeness."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = UserFactory(email='<EMAIL>')
        self.client.force_authenticate(user=self.user)
        
        self.base_data = {
            'daily_calories': 2000,
            'weight': 70,
            'activity_goal': 'muscle_gain',
            'notes': 'Test calculation'
        }
    
    def test_all_diet_profiles_creation(self):
        """Test that all diet profiles can be created successfully via API."""
        
        
        for profile_choice in MacroProfile.choices:
            profile_value = profile_choice[0]
            profile_name = profile_choice[1]
            
            with self.subTest(profile=profile_value):
                data = {
                    **self.base_data,
                    'macro_profile': profile_value
                }
                
                response = self.client.post('/api/v1/calculators/macro/calculations/', data)
                
                
                self.assertEqual(response.status_code, status.HTTP_201_CREATED, 
                    f"Failed to create calculation for {profile_name} ({profile_value})")
                
                
                response_data = response.json()
                self.assertEqual(response_data['macro_profile'], profile_value)
                self.assertEqual(response_data['daily_calories'], 2000)
                self.assertEqual(response_data['weight'], 70.0)
                
                
                self.assertIsNotNone(response_data.get('carb_grams'))
                self.assertIsNotNone(response_data.get('protein_grams'))
                self.assertIsNotNone(response_data.get('fat_grams'))
                self.assertIsNotNone(response_data.get('carb_percentage'))
                self.assertIsNotNone(response_data.get('protein_percentage'))
                self.assertIsNotNone(response_data.get('fat_percentage'))
    
    def test_model_profile_ratios(self):
        """Test that all diet profiles have valid macro ratios."""
        
        for profile_choice in MacroProfile.choices:
            profile_value = profile_choice[0]
            
            with self.subTest(profile=profile_value):
                
                ratios = MacroCalculationModel.get_profile_ratios(profile_value)
                
                
                self.assertEqual(len(ratios), 3, f"Profile {profile_value} should return 3 ratio values")
                
                carb_ratio, protein_ratio, fat_ratio = ratios
                
                
                self.assertGreater(carb_ratio, 0, f"Carb ratio for {profile_value} should be positive")
                self.assertGreater(protein_ratio, 0, f"Protein ratio for {profile_value} should be positive")
                self.assertGreater(fat_ratio, 0, f"Fat ratio for {profile_value} should be positive")
                
                
                total_ratio = carb_ratio + protein_ratio + fat_ratio
                self.assertAlmostEqual(total_ratio, 1.0, places=2, 
                    msg=f"Ratios for {profile_value} should sum to 1.0, got {total_ratio}")
    
    def test_specific_diet_profile_calculations(self):
        """Test specific diet profiles with known expected values."""
        
        test_cases = [
            {
                'profile': MacroProfile.KETOGENIC,
                'expected_carb_percentage_range': (4, 6),  
                'expected_protein_percentage_range': (28, 32),  
                'expected_fat_percentage_range': (63, 67),  
            },
            {
                'profile': MacroProfile.MEDITERRANEAN,
                'expected_carb_percentage_range': (43, 47),  
                'expected_protein_percentage_range': (18, 22),  
                'expected_fat_percentage_range': (33, 37),  
            },
            {
                'profile': MacroProfile.VEGAN,
                'expected_carb_percentage_range': (58, 62),  
                'expected_protein_percentage_range': (13, 17),  
                'expected_fat_percentage_range': (23, 27),  
            },
            {
                'profile': MacroProfile.DASH,
                'expected_carb_percentage_range': (53, 57),  
                'expected_protein_percentage_range': (16, 20),  
                'expected_fat_percentage_range': (25, 29),  
            },
        ]
        
        for test_case in test_cases:
            with self.subTest(profile=test_case['profile']):
                data = {
                    **self.base_data,
                    'macro_profile': test_case['profile']
                }
                
                response = self.client.post('/api/v1/calculators/macro/calculations/', data)
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                
                response_data = response.json()
                
                
                carb_pct = float(response_data['carb_percentage'])
                protein_pct = float(response_data['protein_percentage'])
                fat_pct = float(response_data['fat_percentage'])
                
                self.assertGreaterEqual(carb_pct, test_case['expected_carb_percentage_range'][0])
                self.assertLessEqual(carb_pct, test_case['expected_carb_percentage_range'][1])
                
                self.assertGreaterEqual(protein_pct, test_case['expected_protein_percentage_range'][0])
                self.assertLessEqual(protein_pct, test_case['expected_protein_percentage_range'][1])
                
                self.assertGreaterEqual(fat_pct, test_case['expected_fat_percentage_range'][0])
                self.assertLessEqual(fat_pct, test_case['expected_fat_percentage_range'][1])
    
    def test_popular_diet_profiles_coverage(self):
        """Test that we have comprehensive coverage of popular diet types."""
        
        
        expected_profiles = [
            'balanced', 'high_protein', 'low_carb', 'ketogenic',
            'athlete', 'cutting', 'bulking',
            'mediterranean', 'paleo', 'zone',
            'vegetarian', 'vegan',
            'intermittent_fasting', 'diabetic_friendly', 'heart_healthy',
            'anti_inflammatory', 'dash'
        ]
        
        
        available_profiles = [choice[0] for choice in MacroProfile.choices if choice[0] != 'custom']
        
        
        for expected_profile in expected_profiles:
            with self.subTest(profile=expected_profile):
                self.assertIn(expected_profile, available_profiles, 
                    f"Expected diet profile '{expected_profile}' not found in available choices")
    
    def test_macro_recommendations_with_all_profiles(self):
        """Test macro recommendations endpoint includes all diet profiles."""
        
        data = {
            'daily_calories': 2000,
            'weight': 70,
            'activity_goal': 'muscle_gain'
        }
        
        response = self.client.post('/api/v1/calculators/macro/recommendations/', data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        response_data = response.json()
        recommendations = response_data.get('recommendations', {})
        
        
        for profile_choice in MacroProfile.choices:
            profile_value = profile_choice[0]
            
            with self.subTest(profile=profile_value):
                self.assertIn(profile_value, recommendations, 
                    f"Recommendations should include {profile_value}")
                
                recommendation = recommendations[profile_value]
                self.assertIn('name', recommendation)
                self.assertIn('ratios', recommendation)
                self.assertIn('grams', recommendation)
                self.assertIn('calories', recommendation)
    
    def test_macro_calculation_model_save_method(self):
        """Test that the model save method correctly calculates all fields."""
        
        
        calculation = MacroCalculationModel(
            user=self.user,
            daily_calories=2000,
            weight=Decimal('70.0'),
            activity_goal='muscle_gain',
            macro_profile=MacroProfile.KETOGENIC,
            notes='Test calculation'
        )
        
        
        self.assertIsNone(calculation.carb_grams)
        self.assertIsNone(calculation.protein_grams)
        self.assertIsNone(calculation.fat_grams)
        
        
        calculation.save()
        
        
        self.assertIsNotNone(calculation.carb_grams)
        self.assertIsNotNone(calculation.protein_grams)
        self.assertIsNotNone(calculation.fat_grams)
        self.assertIsNotNone(calculation.carb_calories)
        self.assertIsNotNone(calculation.protein_calories)
        self.assertIsNotNone(calculation.fat_calories)
        self.assertIsNotNone(calculation.carb_percentage)
        self.assertIsNotNone(calculation.protein_percentage)
        self.assertIsNotNone(calculation.fat_percentage)
        self.assertIsNotNone(calculation.protein_per_kg)
        
        
        self.assertLess(calculation.carb_percentage, 10)
        self.assertGreater(calculation.fat_percentage, 60)
    
    def test_edge_cases_and_validation(self):
        """Test edge cases and validation for diet profiles."""
        
        
        data = {
            **self.base_data,
            'daily_calories': 800,  
            'macro_profile': MacroProfile.BALANCED
        }
        
        response = self.client.post('/api/v1/calculators/macro/calculations/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        data = {
            **self.base_data,
            'daily_calories': 8000,  
            'macro_profile': MacroProfile.ATHLETE
        }
        
        response = self.client.post('/api/v1/calculators/macro/calculations/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        data = {
            **self.base_data,
            'weight': 20,  
            'macro_profile': MacroProfile.VEGETARIAN
        }
        
        response = self.client.post('/api/v1/calculators/macro/calculations/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        data = {
            **self.base_data,
            'weight': 500,  
            'macro_profile': MacroProfile.PALEO
        }
        
        response = self.client.post('/api/v1/calculators/macro/calculations/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_no_500_errors_on_any_profile(self):
        """Specific test to ensure NO 500 errors occur with any diet profile."""
        
        for profile_choice in MacroProfile.choices:
            profile_value = profile_choice[0]
            profile_name = profile_choice[1]
            
            with self.subTest(profile=profile_value):
                data = {
                    **self.base_data,
                    'macro_profile': profile_value
                }
                
                response = self.client.post('/api/v1/calculators/macro/calculations/', data)
                
                
                self.assertNotEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR, 
                    f"500 error occurred for {profile_name} ({profile_value})")
                
                
                self.assertEqual(response.status_code, status.HTTP_201_CREATED, 
                    f"Expected 201 for {profile_name} ({profile_value}), got {response.status_code}") 