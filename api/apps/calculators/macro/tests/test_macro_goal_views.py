"""
Test Macro Goal Views

Comprehensive test suite for macro goal views.
"""

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient
from rest_framework import status

from apps.accounts.user.tests.factories import UserFactory
from apps.calculators.macro.models import MacroGoalModel, MacroCalculationModel


class MacroGoalViewsTestCase(TestCase):
    """Test cases for Macro Goal Views"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
        
        
        self.calculation = MacroCalculationModel.objects.create(
            user=self.user,
            daily_calories=2500,
            weight=75.0,
            activity_goal='general_fitness',
            macro_profile='balanced',
            carb_grams=312,
            protein_grams=125,
            fat_grams=83,
            carb_calories=1250,
            protein_calories=500,
            fat_calories=750,
            carb_percentage=50.0,
            protein_percentage=20.0,
            fat_percentage=30.0,
            protein_per_kg=1.67
        )
        
        
        self.goal = MacroGoalModel.objects.create(
            user=self.user,
            target_protein_grams=150,
            target_carb_grams=300,
            target_fat_grams=80,
            goal_description='Test macro goal for fitness',
            notes='Test macro goal'
        )

    def test_goal_list_create_view_get(self):
        """Test listing macro goals"""
        url = reverse('macro:goal-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['count'], 1)
        results = data['results']
        self.assertEqual(len(results), 1)
        self.assertEqual(str(results[0]['id']), str(self.goal.id))
        self.assertIn('progress_percentage', results[0])
        self.assertIn('days_to_goal', results[0])
        self.assertIn('total_calories', results[0])

    def test_goal_list_create_view_post(self):
        """Test creating macro goal"""
        url = reverse('macro:goal-list-create')
        payload = {
            'target_protein_grams': 140,
            'target_carb_grams': 280,
            'target_fat_grams': 70,
            'goal_description': 'New macro goal for muscle gain',
            'notes': 'New macro goal'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        self.assertEqual(data['target_protein_grams'], 140)
        self.assertEqual(data['target_carb_grams'], 280)
        self.assertEqual(data['target_fat_grams'], 70)
        
        
        self.assertTrue(
            MacroGoalModel.objects.filter(
                user=self.user,
                target_protein_grams=140
            ).exists()
        )

    def test_goal_list_create_view_invalid_protein(self):
        """Test creating goal with invalid protein target"""
        url = reverse('macro:goal-list-create')
        payload = {
            'target_protein_grams': 10,  
            'target_carb_grams': 200,
            'target_fat_grams': 60,
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('error', response_data)
        self.assertIn('target_protein_grams', response_data['error'])

    def test_goal_list_create_view_invalid_carbs(self):
        """Test creating goal with invalid carb target"""
        url = reverse('macro:goal-list-create')
        payload = {
            'target_protein_grams': 130,
            'target_carb_grams': 5,  
            'target_fat_grams': 70,
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('error', response_data)
        self.assertIn('target_carb_grams', response_data['error'])

    def test_goal_detail_view_get(self):
        """Test retrieving specific macro goal"""
        url = reverse('macro:goal-detail', args=[self.goal.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(str(data['id']), str(self.goal.id))
        self.assertEqual(data['target_protein_grams'], 150)
        self.assertEqual(data['target_carb_grams'], 300)

    def test_goal_detail_view_put(self):
        """Test updating macro goal"""
        url = reverse('macro:goal-detail', args=[self.goal.id])
        payload = {
            'target_protein_grams': 160,
            'target_carb_grams': 320,
            'target_fat_grams': 90,
            'goal_description': 'Updated macro goal for strength',
            'notes': 'Updated macro goal'
        }
        
        response = self.client.put(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['target_protein_grams'], 160)
        self.assertEqual(data['target_carb_grams'], 320)
        self.assertEqual(data['target_fat_grams'], 90)

    def test_goal_detail_view_delete(self):
        """Test deleting macro goal"""
        url = reverse('macro:goal-detail', args=[self.goal.id])
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(
            MacroGoalModel.objects.filter(id=self.goal.id).exists()
        )

    def test_goal_achieve_view(self):
        """Test marking goal as achieved"""
        url = reverse('macro:goal-achieve', args=[self.goal.id])
        response = self.client.patch(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertTrue(data['is_achieved'])
        self.assertIsNotNone(data['achieved_date'])
        
        
        self.goal.refresh_from_db()
        self.assertTrue(self.goal.is_achieved)
        self.assertEqual(self.goal.achieved_date, timezone.now().date())

    def test_goal_achieve_view_already_achieved(self):
        """Test marking already achieved goal"""
        
        self.goal.is_achieved = True
        self.goal.achieved_date = timezone.now().date()
        self.goal.save()
        
        url = reverse('macro:goal-achieve', args=[self.goal.id])
        response = self.client.patch(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('already marked as achieved', response.json()['detail'])

    def test_goal_create_with_high_adherence_threshold(self):
        """Test goal creation with custom adherence threshold"""
        url = reverse('macro:goal-list-create')
        payload = {
            'target_protein_grams': 140,
            'target_carb_grams': 280,
            'target_fat_grams': 70,
            'adherence_threshold': 95.0,
            'goal_description': 'High precision macro goal'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        self.assertEqual(float(data['adherence_threshold']), 95.0)

    def test_goal_properties(self):
        """Test goal model properties"""
        
        expected_calories = (300 * 4) + (150 * 4) + (80 * 9)  
        self.assertEqual(self.goal.total_calories, expected_calories)
        
        
        percentages = self.goal.macro_percentages
        self.assertIn('carb', percentages)
        self.assertIn('protein', percentages)
        self.assertIn('fat', percentages)
        
        
        self.assertGreaterEqual(self.goal.days_to_goal, 0)

    def test_user_isolation(self):
        """Test that users can only see their own goals"""
        other_user = UserFactory()
        other_goal = MacroGoalModel.objects.create(
            user=other_user,
            target_protein_grams=120,
            target_carb_grams=250,
            target_fat_grams=60
        )
        
        url = reverse('macro:goal-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['count'], 1)  
        self.assertEqual(str(data['results'][0]['id']), str(self.goal.id))

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access goals"""
        self.client.force_authenticate(user=None)
        
        url = reverse('macro:goal-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 