"""
Test Macro Calculation Views

Comprehensive test suite for macro calculation views.
"""

from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from apps.accounts.user.tests.factories import UserFactory
from apps.calculators.macro.models import MacroCalculationModel


class MacroCalculationViewsTestCase(TestCase):
    """Test cases for Macro Calculation Views"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
        
        
        self.calculation = MacroCalculationModel.objects.create(
            user=self.user,
            daily_calories=2500,
            weight=75.0,
            activity_goal='general_fitness',
            macro_profile='balanced',
            carb_grams=312,
            protein_grams=125,
            fat_grams=83,
            carb_calories=1250,
            protein_calories=500,
            fat_calories=750,
            carb_percentage=50.0,
            protein_percentage=20.0,
            fat_percentage=30.0,
            protein_per_kg=1.67,
            is_active=True
        )

    def test_calculation_list_create_view_get(self):
        """Test listing macro calculations"""
        url = reverse('macro:calculation-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['count'], 1)
        results = data['results']
        self.assertEqual(len(results), 1)
        self.assertEqual(str(results[0]['id']), str(self.calculation.id))
        self.assertIn('macros_dict', results[0])
        self.assertIn('meal_distribution', results[0])
        self.assertIn('is_balanced', results[0])

    def test_calculation_list_create_view_post(self):
        """Test creating macro calculation"""
        url = reverse('macro:calculation-list-create')
        payload = {
            'daily_calories': 2000,
            'weight': 70.0,
            'activity_goal': 'weight_loss',
            'macro_profile': 'high_protein',
            'notes': 'Weight loss calculation'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        self.assertEqual(data['daily_calories'], 2000)
        self.assertEqual(float(data['weight']), 70.0)
        self.assertEqual(data['activity_goal'], 'weight_loss')
        self.assertEqual(data['macro_profile'], 'high_protein')
        
        
        self.assertTrue(
            MacroCalculationModel.objects.filter(
                user=self.user,
                daily_calories=2000
            ).exists()
        )

    def test_calculation_list_create_view_post_custom_profile(self):
        """Test creating calculation with custom macro profile"""
        url = reverse('macro:calculation-list-create')
        payload = {
            'daily_calories': 2200,
            'weight': 80.0,
            'activity_goal': 'muscle_gain',
            'macro_profile': 'custom',
            'custom_carb_percentage': 45.0,
            'custom_protein_percentage': 25.0,
            'custom_fat_percentage': 30.0,
            'notes': 'Custom macro split'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        self.assertEqual(data['macro_profile'], 'custom')
        self.assertEqual(float(data['custom_carb_percentage']), 45.0)
        self.assertEqual(float(data['custom_protein_percentage']), 25.0)
        self.assertEqual(float(data['custom_fat_percentage']), 30.0)

    def test_calculation_list_create_view_invalid_custom_percentages(self):
        """Test creating calculation with invalid custom percentages"""
        url = reverse('macro:calculation-list-create')
        payload = {
            'daily_calories': 2000,
            'weight': 70.0,
            'activity_goal': 'general_fitness',
            'macro_profile': 'custom',
            'custom_carb_percentage': 50.0,
            'custom_protein_percentage': 30.0,
            'custom_fat_percentage': 25.0,  
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('custom_percentages', response.json()['error'])

    def test_calculation_detail_view_get(self):
        """Test retrieving specific macro calculation"""
        url = reverse('macro:calculation-detail', args=[self.calculation.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(str(data['id']), str(self.calculation.id))
        self.assertEqual(data['daily_calories'], 2500)
        self.assertEqual(data['activity_goal'], 'general_fitness')

    def test_calculation_detail_view_put(self):
        """Test updating macro calculation"""
        url = reverse('macro:calculation-detail', args=[self.calculation.id])
        payload = {
            'daily_calories': 2600,
            'weight': 76.0,
            'activity_goal': 'muscle_gain',
            'macro_profile': 'bulking',
            'notes': 'Updated for muscle gain'
        }
        
        response = self.client.put(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['daily_calories'], 2600)
        self.assertEqual(data['activity_goal'], 'muscle_gain')
        self.assertEqual(data['macro_profile'], 'bulking')

    def test_calculation_detail_view_delete(self):
        """Test deleting macro calculation"""
        url = reverse('macro:calculation-detail', args=[self.calculation.id])
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(
            MacroCalculationModel.objects.filter(id=self.calculation.id).exists()
        )

    def test_calculation_active_view(self):
        """Test retrieving active macro calculation"""
        url = reverse('macro:calculation-active')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(str(data['id']), str(self.calculation.id))
        self.assertTrue(data['is_active'])

    def test_calculation_active_view_no_active(self):
        """Test retrieving active calculation when none marked as active"""
        
        self.calculation.is_active = False
        self.calculation.save()
        
        url = reverse('macro:calculation-active')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(str(data['id']), str(self.calculation.id))

    def test_user_isolation(self):
        """Test that users can only see their own calculations"""
        other_user = UserFactory()
        other_calculation = MacroCalculationModel.objects.create(
            user=other_user,
            daily_calories=3000,
            weight=90.0,
            activity_goal='strength',
            macro_profile='cutting',
            carb_grams=225,
            protein_grams=225,
            fat_grams=100,
            carb_calories=900,
            protein_calories=900,
            fat_calories=900,
            carb_percentage=30.0,
            protein_percentage=40.0,
            fat_percentage=30.0,
            protein_per_kg=2.5
        )
        
        
        url = reverse('macro:calculation-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['count'], 1)
        results = data['results']
        self.assertEqual(len(results), 1)
        self.assertEqual(str(results[0]['id']), str(self.calculation.id))
        
        
        url = reverse('macro:calculation-detail', args=[other_calculation.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access views"""
        self.client.force_authenticate(user=None)
        
        urls = [
            reverse('macro:calculation-list-create'),
            reverse('macro:calculation-detail', args=[self.calculation.id]),
            reverse('macro:calculation-active'),
        ]
        
        for url in urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 