"""
Test Macro Utility Views

Comprehensive test suite for macro utility views (stats, recommendations, quick calculation).
"""

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from rest_framework import status
from rest_framework.test import APIClient

from apps.accounts.user.tests.factories import UserFactory
from apps.calculators.macro.models import MacroCalculationModel, MacroGoalModel


class MacroUtilityViewsTestCase(TestCase):
    """Test cases for Macro Utility Views (Stats, Recommendations, Quick Calculation)"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
        
        
        self.calculation1 = MacroCalculationModel.objects.create(
            user=self.user,
            daily_calories=2500,
            weight=75.0,
            activity_goal='general_fitness',
            macro_profile='balanced',
            carb_grams=312,
            protein_grams=125,
            fat_grams=83,
            carb_calories=1250,
            protein_calories=500,
            fat_calories=750,
            carb_percentage=50.0,
            protein_percentage=20.0,
            fat_percentage=30.0,
            protein_per_kg=1.67
        )
        
        self.calculation2 = MacroCalculationModel.objects.create(
            user=self.user,
            daily_calories=2400,
            weight=74.0,
            activity_goal='weight_loss',
            macro_profile='high_protein',
            carb_grams=240,
            protein_grams=144,
            fat_grams=80,
            carb_calories=960,
            protein_calories=576,
            fat_calories=720,
            carb_percentage=40.0,
            protein_percentage=24.0,
            fat_percentage=30.0,
            protein_per_kg=1.95
        )
        
        
        self.goal1 = MacroGoalModel.objects.create(
            user=self.user,
            target_protein_grams=150,
            target_carb_grams=300,
            target_fat_grams=80,
            is_achieved=True,
            achieved_date=timezone.now().date(),
            goal_description='Achieved macro goal'
        )
        
        self.goal2 = MacroGoalModel.objects.create(
            user=self.user,
            target_protein_grams=140,
            target_carb_grams=280,
            target_fat_grams=70,
            goal_description='Current macro goal'
        )

    def test_stats_view_with_data(self):
        """Test stats view with existing calculations and goals"""
        url = reverse('macro:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('total_calculations', data)
        self.assertGreaterEqual(data['total_calculations'], 2)  
        
        self.assertIn('average_calories', data)
        self.assertIsNotNone(data['average_calories'])
        
        self.assertIn('average_protein', data)
        self.assertIsNotNone(data['average_protein'])
        
        
        self.assertIn('total_goals', data)
        self.assertEqual(data['total_goals'], 2)
        
        self.assertIn('achieved_goals', data)
        self.assertEqual(data['achieved_goals'], 1)
        
        
        self.assertIn('profile_distribution', data)
        profile_dist = data['profile_distribution']
        self.assertIn('balanced', profile_dist)
        self.assertIn('high_protein', profile_dist)

    def test_stats_view_no_data(self):
        """Test stats view with no data"""
        
        MacroCalculationModel.objects.filter(user=self.user).delete()
        MacroGoalModel.objects.filter(user=self.user).delete()
        
        url = reverse('macro:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['total_calculations'], 0)
        self.assertEqual(data['total_goals'], 0)
        self.assertEqual(data['achieved_goals'], 0)
        self.assertIsNone(data['average_calories'])
        self.assertIsNone(data['average_protein'])

    def test_recommendations_view_with_existing_calculation(self):
        """Test recommendations based on existing calculations"""
        url = reverse('macro:recommendations')
        payload = {
            'daily_calories': 2200,
            'weight': 75.0,
            'activity_goal': 'muscle_gain'
        }
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('profile_recommendations', data)
        self.assertIn('activity_specific', data)
        self.assertIn('recommended_protein_per_kg', data)
        self.assertIn('general_tips', data)
        
        
        activity_rec = data['activity_specific']
        self.assertIn('recommended_profiles', activity_rec)
        self.assertIn('protein_focus', activity_rec)

    def test_recommendations_view_default_values(self):
        """Test recommendations with no existing data"""
        
        MacroCalculationModel.objects.filter(user=self.user).delete()
        
        url = reverse('macro:recommendations')
        payload = {}  
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('profile_recommendations', data)
        self.assertIn('activity_specific', data)
        self.assertIn('recommended_protein_per_kg', data)

    def test_quick_calculation_view_valid_input(self):
        """Test quick calculation with valid input"""
        url = reverse('macro:calculation-quick')
        payload = {
            'daily_calories': 2200,
            'weight': 70.0,
            'activity_goal': 'muscle_gain',
            'macro_profile': 'high_protein'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('carb_grams', data)
        self.assertIn('protein_grams', data)
        self.assertIn('fat_grams', data)
        self.assertIn('carb_percentage', data)
        self.assertIn('protein_percentage', data)
        self.assertIn('fat_percentage', data)
        self.assertIn('protein_per_kg', data)
        
        
        self.assertFalse(
            MacroCalculationModel.objects.filter(
                user=self.user,
                daily_calories=2200
            ).exists()
        )

    def test_quick_calculation_view_custom_profile(self):
        """Test quick calculation with custom profile"""
        url = reverse('macro:calculation-quick')
        payload = {
            'daily_calories': 2000,
            'weight': 65.0,
            'activity_goal': 'weight_loss',
            'macro_profile': 'custom',
            'custom_carb_percentage': 30.0,
            'custom_protein_percentage': 40.0,
            'custom_fat_percentage': 30.0
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertAlmostEqual(float(data['carb_percentage']), 30.0, places=1)
        self.assertAlmostEqual(float(data['protein_percentage']), 40.0, places=1)
        self.assertAlmostEqual(float(data['fat_percentage']), 30.0, places=1)

    def test_quick_calculation_view_invalid_calories(self):
        """Test quick calculation with invalid calories"""
        url = reverse('macro:calculation-quick')
        payload = {
            'daily_calories': 500,  
            'weight': 70.0,
            'activity_goal': 'general_fitness',
            'macro_profile': 'balanced'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('detail', response_data)
        self.assertIn('daily calories', response_data['detail'].lower())

    def test_quick_calculation_view_invalid_weight(self):
        """Test quick calculation with invalid weight"""
        url = reverse('macro:calculation-quick')
        payload = {
            'daily_calories': 2000,
            'weight': 10.0,  
            'activity_goal': 'general_fitness',
            'macro_profile': 'balanced'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('detail', response_data)
        self.assertIn('weight', response_data['detail'].lower())

    def test_quick_calculation_view_invalid_custom_percentages(self):
        """Test quick calculation with invalid custom percentages"""
        url = reverse('macro:calculation-quick')
        payload = {
            'daily_calories': 2000,
            'weight': 70.0,
            'activity_goal': 'general_fitness',
            'macro_profile': 'custom',
            'custom_carb_percentage': 40.0,
            'custom_protein_percentage': 30.0,
            'custom_fat_percentage': 40.0  
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('detail', response_data)
        self.assertIn('100%', response_data['detail'])

    def test_quick_calculation_view_missing_required_field(self):
        """Test quick calculation with missing required field"""
        url = reverse('macro:calculation-quick')
        payload = {
            'daily_calories': 2000,
            
            'activity_goal': 'general_fitness',
            'macro_profile': 'balanced'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        response_data = response.json()
        self.assertIn('detail', response_data)
        self.assertIn('weight', response_data['detail'].lower())

    def test_user_isolation_stats(self):
        """Test that stats only include user's own data"""
        other_user = UserFactory()
        
        
        MacroCalculationModel.objects.create(
            user=other_user,
            daily_calories=3000,
            weight=80.0,
            activity_goal='bulking',
            macro_profile='athlete',
            carb_grams=450,
            protein_grams=150,
            fat_grams=100,
            carb_calories=1800,
            protein_calories=600,
            fat_calories=900,
            carb_percentage=60.0,
            protein_percentage=20.0,
            fat_percentage=30.0,
            protein_per_kg=1.88
        )
        
        url = reverse('macro:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['total_calculations'], 2)  
        self.assertEqual(data['average_calories'], 2450)  

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access utility views"""
        self.client.force_authenticate(user=None)
        
        urls = [
            reverse('macro:stats'),
            reverse('macro:recommendations'),
            reverse('macro:calculation-quick'),
        ]
        
        for url in urls:
            if 'stats' in url:
                response = self.client.get(url)
            else:
                response = self.client.post(url, {})
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 