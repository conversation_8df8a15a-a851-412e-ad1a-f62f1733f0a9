from django.urls import path

from .views import (
    MacroCalculationListCreateView,
    MacroCalculationDetailView,
    MacroCalculationActiveView,
    MacroGoalListCreateView,
    MacroGoalDetailView,
    MacroGoalAchieveView,
    MacroStatsView,
    MacroRecommendationsView,
    MacroQuickCalculationView,
)

app_name = 'macro'

urlpatterns = [
    
    path(
        'calculations/',
        MacroCalculationListCreateView.as_view(),
        name='calculation-list-create'
    ),
    path(
        'calculations/<uuid:pk>/',
        MacroCalculationDetailView.as_view(),
        name='calculation-detail'
    ),
    path(
        'calculations/active/',
        MacroCalculationActiveView.as_view(),
        name='calculation-active'
    ),
    path(
        'calculations/quick/',
        MacroQuickCalculationView.as_view(),
        name='calculation-quick'
    ),
    
    
    path(
        'goals/',
        MacroGoalListCreateView.as_view(),
        name='goal-list-create'
    ),
    path(
        'goals/<uuid:pk>/',
        MacroGoalDetailView.as_view(),
        name='goal-detail'
    ),
    path(
        'goals/<uuid:pk>/achieve/',
        MacroGoalAchieveView.as_view(),
        name='goal-achieve'
    ),
    
    
    path(
        'stats/',
        MacroStatsView.as_view(),
        name='stats'
    ),
    path(
        'recommendations/',
        MacroRecommendationsView.as_view(),
        name='recommendations'
    ),
] 