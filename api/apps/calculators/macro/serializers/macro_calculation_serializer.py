"""
Macro Calculation Serializer

Serializer for macro calculations with all calculated fields.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import MacroCalculationModel


class MacroCalculationSerializer(serializers.ModelSerializer):
    """
    Serializer for macro calculations with all calculated fields.
    """
    activity_goal_display = serializers.CharField(
        source='get_activity_goal_display',
        read_only=True
    )
    macro_profile_display = serializers.CharField(
        source='get_macro_profile_display',
        read_only=True
    )
    macros_dict = serializers.SerializerMethodField()
    meal_distribution = serializers.SerializerMethodField()
    is_balanced = serializers.SerializerMethodField()
    
    class Meta:
        model = MacroCalculationModel
        fields = [
            'id',
            'created',
            'updated',
            'is_active',
            
            
            'daily_calories',
            'weight',
            'activity_goal',
            'macro_profile',
            'custom_carb_percentage',
            'custom_protein_percentage',
            'custom_fat_percentage',
            
            
            'carb_grams',
            'protein_grams',
            'fat_grams',
            'carb_calories',
            'protein_calories',
            'fat_calories',
            'carb_percentage',
            'protein_percentage',
            'fat_percentage',
            'protein_per_kg',
            
            
            'activity_goal_display',
            'macro_profile_display',
            
            
            'macros_dict',
            'meal_distribution',
            'is_balanced',
            'notes',
        ]
        read_only_fields = [
            'id',
            'created',
            'updated',
            'carb_grams',
            'protein_grams',
            'fat_grams',
            'carb_calories',
            'protein_calories',
            'fat_calories',
            'carb_percentage',
            'protein_percentage',
            'fat_percentage',
            'protein_per_kg',
        ]
    
    def get_macros_dict(self, obj):
        """Get macronutrient breakdown as dictionary"""
        return obj.macros_dict
    
    def get_meal_distribution(self, obj):
        """Get suggested meal distribution"""
        return obj.meal_distribution
    
    def get_is_balanced(self, obj):
        """Check if macros add up to 100%"""
        return obj.is_balanced 