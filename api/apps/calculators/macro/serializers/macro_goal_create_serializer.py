"""
Macro Goal Create Serializer

Serializer for creating macro goals with validation.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import MacroGoalModel, MacroCalculationModel


class MacroGoalCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating macro goals with validation.
    """
    
    class Meta:
        model = MacroGoalModel
        fields = [
            'target_protein_grams',
            'target_carb_grams',
            'target_fat_grams',
            'adherence_threshold',
            'goal_description',
            'notes',
        ]
    
    def validate(self, data):
        """
        Validate goal data for reasonableness.
        """
        target_protein = data.get('target_protein_grams')
        target_carb = data.get('target_carb_grams')
        target_fat = data.get('target_fat_grams')
        adherence_threshold = data.get('adherence_threshold')
        
        
        if target_protein and (target_protein < 20 or target_protein > 500):
            raise serializers.ValidationError({
                'target_protein_grams': _('Target protein must be between 20g and 500g.')
            })
        
        if target_carb and (target_carb < 10 or target_carb > 2000):
            raise serializers.ValidationError({
                'target_carb_grams': _('Target carbohydrates must be between 10g and 2000g.')
            })
        
        if target_fat and (target_fat < 10 or target_fat > 300):
            raise serializers.ValidationError({
                'target_fat_grams': _('Target fat must be between 10g and 300g.')
            })
        
        
        if adherence_threshold and (adherence_threshold < 50.0 or adherence_threshold > 100.0):
            raise serializers.ValidationError({
                'adherence_threshold': _('Adherence threshold must be between 50% and 100%.')
            })
        
        
        total_calories = (target_carb * 4) + (target_protein * 4) + (target_fat * 9)
        if total_calories < 800 or total_calories > 8000:
            raise serializers.ValidationError({
                'non_field_errors': _('Total calories from macros must be between 800 and 8000.')
            })
        
        return data
    
    def create(self, validated_data):
        """
        Create goal with user assignment.
        """
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data) 