"""
Macro Calculation Create Serializer

Serializer for creating macro calculations with validation.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import MacroCalculationModel


class MacroCalculationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating macro calculations with validation.
    """
    
    class Meta:
        model = MacroCalculationModel
        fields = [
            'daily_calories',
            'weight',
            'activity_goal',
            'macro_profile',
            'custom_carb_percentage',
            'custom_protein_percentage',
            'custom_fat_percentage',
            'is_active',
            'notes',
        ]
    
    def validate(self, data):
        """
        Validate input data for macro calculations.
        """
        macro_profile = data.get('macro_profile')
        
        
        if macro_profile == 'custom':
            custom_carb = data.get('custom_carb_percentage')
            custom_protein = data.get('custom_protein_percentage')
            custom_fat = data.get('custom_fat_percentage')
            
            if not all([custom_carb, custom_protein, custom_fat]):
                raise serializers.ValidationError({
                    'custom_percentages': _('All custom percentages are required when using custom profile.')
                })
            
            
            total = float(custom_carb + custom_protein + custom_fat)
            if not 99.0 <= total <= 101.0:
                raise serializers.ValidationError({
                    'custom_percentages': _('Custom percentages must add up to 100%.')
                })
        
        return data 