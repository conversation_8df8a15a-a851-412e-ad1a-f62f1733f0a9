"""
Macro Goal Serializer

Serializer for macro goals with progress tracking.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import MacroGoalModel


class MacroGoalSerializer(serializers.ModelSerializer):
    """
    Serializer for macro goals with progress tracking.
    """
    progress_percentage = serializers.ReadOnlyField()
    days_to_goal = serializers.ReadOnlyField()
    total_calories = serializers.ReadOnlyField()
    macro_percentages = serializers.ReadOnlyField()
    
    class Meta:
        model = MacroGoalModel
        fields = [
            'id',
            'created',
            'updated',
            'target_protein_grams',
            'target_carb_grams',
            'target_fat_grams',
            'adherence_threshold',
            'goal_description',
            'is_achieved',
            'achieved_date',
            'notes',
            'progress_percentage',
            'days_to_goal',
            'total_calories',
            'macro_percentages',
        ]
        read_only_fields = [
            'id',
            'created',
            'updated',
            'is_achieved',
            'achieved_date',
            'progress_percentage',
            'days_to_goal',
            'total_calories',
            'macro_percentages',
        ]
    
    def get_progress_percentage(self, obj):
        """Calculate overall progress percentage towards macro goals"""
        if not all([obj.current_protein_average, obj.current_carb_average, obj.current_fat_average]):
            return 0.0
        
        
        protein_adherence = min(100, (obj.current_protein_average / obj.target_protein_grams) * 100)
        carb_adherence = min(100, (obj.current_carb_average / obj.target_carb_grams) * 100)
        fat_adherence = min(100, (obj.current_fat_average / obj.target_fat_grams) * 100)
        
        
        overall_adherence = (protein_adherence + carb_adherence + fat_adherence) / 3
        return round(overall_adherence, 1)
    
    def get_days_remaining(self, obj):
        """Calculate days remaining to target date"""
        if obj.is_achieved or not obj.target_date:
            return 0
        
        from django.utils import timezone
        remaining = (obj.target_date - timezone.now().date()).days
        return max(0, remaining)
    
    def get_current_adherence(self, obj):
        """Get current adherence breakdown by macro"""
        if not all([obj.current_protein_average, obj.current_carb_average, obj.current_fat_average]):
            return {
                'protein_adherence': 0,
                'carb_adherence': 0,
                'fat_adherence': 0,
                'overall_adherence': 0,
            }
        
        protein_adherence = min(100, (obj.current_protein_average / obj.target_protein_grams) * 100)
        carb_adherence = min(100, (obj.current_carb_average / obj.target_carb_grams) * 100)
        fat_adherence = min(100, (obj.current_fat_average / obj.target_fat_grams) * 100)
        overall_adherence = (protein_adherence + carb_adherence + fat_adherence) / 3
        
        return {
            'protein_adherence': round(protein_adherence, 1),
            'carb_adherence': round(carb_adherence, 1),
            'fat_adherence': round(fat_adherence, 1),
            'overall_adherence': round(overall_adherence, 1),
        } 