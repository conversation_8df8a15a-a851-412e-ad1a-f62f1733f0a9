# Generated by Django 4.2.23 on 2025-06-20 17:56

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("macro", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="MacroProfile",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "age",
                    models.IntegerField(
                        default=30,
                        validators=[
                            django.core.validators.MinValueValidator(10),
                            django.core.validators.MaxValueValidator(120),
                        ],
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[
                            ("male", "Male"),
                            ("female", "Female"),
                            ("other", "Other"),
                        ],
                        default="male",
                        max_length=10,
                    ),
                ),
                (
                    "activity_level",
                    models.CharField(
                        choices=[
                            ("sedentary", "Sedentary"),
                            ("lightly_active", "Lightly Active"),
                            ("moderately_active", "Moderately Active"),
                            ("very_active", "Very Active"),
                            ("extra_active", "Extra Active"),
                        ],
                        default="moderately_active",
                        max_length=20,
                    ),
                ),
                (
                    "weight_kg",
                    models.FloatField(
                        default=70.0,
                        validators=[
                            django.core.validators.MinValueValidator(30),
                            django.core.validators.MaxValueValidator(300),
                        ],
                    ),
                ),
                (
                    "height_cm",
                    models.FloatField(
                        default=170.0,
                        validators=[
                            django.core.validators.MinValueValidator(100),
                            django.core.validators.MaxValueValidator(250),
                        ],
                    ),
                ),
                (
                    "goal",
                    models.CharField(
                        choices=[
                            ("lose_weight", "Lose Weight"),
                            ("maintain_weight", "Maintain Weight"),
                            ("gain_weight", "Gain Weight"),
                            ("build_muscle", "Build Muscle"),
                        ],
                        default="maintain_weight",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "db_table": "macro_profiles",
            },
        ),
        migrations.AlterModelOptions(
            name="macrocalculationmodel",
            options={"ordering": ["-created"]},
        ),
        migrations.AlterModelOptions(
            name="macrogoalmodel",
            options={},
        ),
        migrations.RemoveIndex(
            model_name="macrocalculationmodel",
            name="macro_calc_user_created_idx",
        ),
        migrations.RemoveIndex(
            model_name="macrocalculationmodel",
            name="macro_calc_user_active_idx",
        ),
        migrations.RemoveIndex(
            model_name="macrogoalmodel",
            name="macro_goal_user_created_idx",
        ),
        migrations.RemoveIndex(
            model_name="macrogoalmodel",
            name="macro_goal_achieved_idx",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="activity_goal",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="carb_calories",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="carb_grams",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="carb_percentage",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="custom_carb_percentage",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="custom_fat_percentage",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="custom_protein_percentage",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="daily_calories",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="fat_calories",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="is_active",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="macro_profile",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="notes",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="protein_calories",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="protein_per_kg",
        ),
        migrations.RemoveField(
            model_name="macrocalculationmodel",
            name="weight",
        ),
        migrations.RemoveField(
            model_name="macrogoalmodel",
            name="achieved_date",
        ),
        migrations.RemoveField(
            model_name="macrogoalmodel",
            name="adherence_threshold",
        ),
        migrations.RemoveField(
            model_name="macrogoalmodel",
            name="goal_description",
        ),
        migrations.RemoveField(
            model_name="macrogoalmodel",
            name="is_achieved",
        ),
        migrations.RemoveField(
            model_name="macrogoalmodel",
            name="notes",
        ),
        migrations.RemoveField(
            model_name="macrogoalmodel",
            name="target_carb_grams",
        ),
        migrations.AddField(
            model_name="macrocalculationmodel",
            name="calories",
            field=models.IntegerField(
                default=2000,
                validators=[
                    django.core.validators.MinValueValidator(800),
                    django.core.validators.MaxValueValidator(8000),
                ],
            ),
        ),
        migrations.AddField(
            model_name="macrocalculationmodel",
            name="carbs_grams",
            field=models.FloatField(
                default=200.0, validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
        migrations.AddField(
            model_name="macrocalculationmodel",
            name="carbs_percentage",
            field=models.FloatField(
                default=40.0,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AddField(
            model_name="macrogoalmodel",
            name="target_calories",
            field=models.IntegerField(
                default=2000,
                validators=[
                    django.core.validators.MinValueValidator(800),
                    django.core.validators.MaxValueValidator(8000),
                ],
            ),
        ),
        migrations.AddField(
            model_name="macrogoalmodel",
            name="target_carbs_grams",
            field=models.FloatField(
                default=200.0, validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
        migrations.AddField(
            model_name="macrogoalmodel",
            name="target_carbs_percentage",
            field=models.FloatField(
                default=40.0,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AddField(
            model_name="macrogoalmodel",
            name="target_fat_percentage",
            field=models.FloatField(
                default=30.0,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AddField(
            model_name="macrogoalmodel",
            name="target_protein_percentage",
            field=models.FloatField(
                default=30.0,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AlterField(
            model_name="macrocalculationmodel",
            name="fat_grams",
            field=models.FloatField(
                default=65.0, validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
        migrations.AlterField(
            model_name="macrocalculationmodel",
            name="fat_percentage",
            field=models.FloatField(
                default=30.0,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AlterField(
            model_name="macrocalculationmodel",
            name="protein_grams",
            field=models.FloatField(
                default=150.0, validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
        migrations.AlterField(
            model_name="macrocalculationmodel",
            name="protein_percentage",
            field=models.FloatField(
                default=30.0,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.AlterField(
            model_name="macrocalculationmodel",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="macro_calculations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="macrogoalmodel",
            name="target_fat_grams",
            field=models.FloatField(
                default=65.0, validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
        migrations.AlterField(
            model_name="macrogoalmodel",
            name="target_protein_grams",
            field=models.FloatField(
                default=150.0, validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
        migrations.AlterField(
            model_name="macrogoalmodel",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="macro_goal",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterModelTable(
            name="macrocalculationmodel",
            table="macro_calculations",
        ),
        migrations.AlterModelTable(
            name="macrogoalmodel",
            table="macro_goals",
        ),
        migrations.AddField(
            model_name="macroprofile",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="macro_profile",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
