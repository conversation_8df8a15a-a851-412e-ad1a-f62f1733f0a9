

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid
from decimal import Decimal


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MacroCalculationModel',
            fields=[
                ('created', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('_id', models.IntegerField(db_index=True, editable=False, null=True, unique=True)),
                
                
                ('daily_calories', models.PositiveIntegerField(
                    validators=[
                        django.core.validators.MinValueValidator(800),
                        django.core.validators.MaxValueValidator(8000),
                    ],
                    verbose_name='Daily Calories',
                    help_text='Target daily calories (800-8000 kcal)',
                )),
                
                ('weight', models.DecimalField(
                    max_digits=5,
                    decimal_places=1,
                    validators=[
                        django.core.validators.MinValueValidator(Decimal('20.0')),
                        django.core.validators.MaxValueValidator(Decimal('500.0')),
                    ],
                    verbose_name='Weight (kg)',
                    help_text='Current weight in kilograms',
                )),
                
                ('activity_goal', models.CharField(
                    max_length=25,
                    choices=[
                        ('sedentary', 'Sedentary/Office Work'),
                        ('weight_loss', 'Weight Loss'),
                        ('muscle_gain', 'Muscle Gain'),
                        ('athletic_performance', 'Athletic Performance'),
                        ('endurance', 'Endurance Training'),
                        ('strength', 'Strength Training'),
                        ('general_fitness', 'General Fitness'),
                    ],
                    default='general_fitness',
                    verbose_name='Activity Goal',
                )),
                
                ('macro_profile', models.CharField(
                    max_length=20,
                    choices=[
                        ('balanced', 'Balanced (50% carbs, 20% protein, 30% fat)'),
                        ('high_protein', 'High Protein (40% carbs, 30% protein, 30% fat)'),
                        ('low_carb', 'Low Carb (25% carbs, 35% protein, 40% fat)'),
                        ('ketogenic', 'Ketogenic (5% carbs, 30% protein, 65% fat)'),
                        ('athlete', 'Athlete (55% carbs, 20% protein, 25% fat)'),
                        ('cutting', 'Cutting (30% carbs, 40% protein, 30% fat)'),
                        ('bulking', 'Bulking (50% carbs, 25% protein, 25% fat)'),
                        ('mediterranean', 'Mediterranean (45% carbs, 20% protein, 35% fat)'),
                        ('paleo', 'Paleo (30% carbs, 25% protein, 45% fat)'),
                        ('zone', 'Zone Diet (40% carbs, 30% protein, 30% fat)'),
                        ('vegetarian', 'Vegetarian (55% carbs, 15% protein, 30% fat)'),
                        ('vegan', 'Vegan (60% carbs, 15% protein, 25% fat)'),
                        ('intermittent_fasting', 'Intermittent Fasting (40% carbs, 25% protein, 35% fat)'),
                        ('diabetic_friendly', 'Diabetic Friendly (35% carbs, 25% protein, 40% fat)'),
                        ('heart_healthy', 'Heart Healthy (50% carbs, 20% protein, 30% fat)'),
                        ('anti_inflammatory', 'Anti-Inflammatory (40% carbs, 20% protein, 40% fat)'),
                        ('dash', 'DASH Diet (55% carbs, 18% protein, 27% fat)'),
                        ('custom', 'Custom Ratios')
                    ],
                    default='balanced',
                    verbose_name='Macro Profile',
                )),
                
                
                ('custom_carb_percentage', models.DecimalField(
                    max_digits=4,
                    decimal_places=1,
                    blank=True,
                    null=True,
                    validators=[
                        django.core.validators.MinValueValidator(Decimal('0.0')),
                        django.core.validators.MaxValueValidator(Decimal('100.0')),
                    ],
                    verbose_name='Custom Carb Percentage',
                    help_text='Custom carbohydrate percentage (0-100%)',
                )),
                
                ('custom_protein_percentage', models.DecimalField(
                    max_digits=4,
                    decimal_places=1,
                    blank=True,
                    null=True,
                    validators=[
                        django.core.validators.MinValueValidator(Decimal('0.0')),
                        django.core.validators.MaxValueValidator(Decimal('100.0')),
                    ],
                    verbose_name='Custom Protein Percentage',
                    help_text='Custom protein percentage (0-100%)',
                )),
                
                ('custom_fat_percentage', models.DecimalField(
                    max_digits=4,
                    decimal_places=1,
                    blank=True,
                    null=True,
                    validators=[
                        django.core.validators.MinValueValidator(Decimal('0.0')),
                        django.core.validators.MaxValueValidator(Decimal('100.0')),
                    ],
                    verbose_name='Custom Fat Percentage',
                    help_text='Custom fat percentage (0-100%)',
                )),
                
                
                ('carb_grams', models.PositiveIntegerField(
                    blank=True,
                    null=True,
                    verbose_name='Carbohydrates (g)',
                    help_text='Daily carbohydrates in grams',
                )),
                
                ('protein_grams', models.PositiveIntegerField(
                    blank=True,
                    null=True,
                    verbose_name='Protein (g)',
                    help_text='Daily protein in grams',
                )),
                
                ('fat_grams', models.PositiveIntegerField(
                    blank=True,
                    null=True,
                    verbose_name='Fat (g)',
                    help_text='Daily fat in grams',
                )),
                
                ('carb_calories', models.PositiveIntegerField(
                    blank=True,
                    null=True,
                    verbose_name='Carb Calories',
                    help_text='Calories from carbohydrates',
                )),
                
                ('protein_calories', models.PositiveIntegerField(
                    blank=True,
                    null=True,
                    verbose_name='Protein Calories',
                    help_text='Calories from protein',
                )),
                
                ('fat_calories', models.PositiveIntegerField(
                    blank=True,
                    null=True,
                    verbose_name='Fat Calories',
                    help_text='Calories from fat',
                )),
                
                ('carb_percentage', models.DecimalField(
                    max_digits=4,
                    decimal_places=1,
                    blank=True,
                    null=True,
                    verbose_name='Carb Percentage',
                    help_text='Percentage of calories from carbohydrates',
                )),
                
                ('protein_percentage', models.DecimalField(
                    max_digits=4,
                    decimal_places=1,
                    blank=True,
                    null=True,
                    verbose_name='Protein Percentage',
                    help_text='Percentage of calories from protein',
                )),
                
                ('fat_percentage', models.DecimalField(
                    max_digits=4,
                    decimal_places=1,
                    blank=True,
                    null=True,
                    verbose_name='Fat Percentage',
                    help_text='Percentage of calories from fat',
                )),
                
                ('protein_per_kg', models.DecimalField(
                    max_digits=4,
                    decimal_places=2,
                    blank=True,
                    null=True,
                    verbose_name='Protein per kg',
                    help_text='Protein grams per kg body weight',
                )),
                
                ('notes', models.TextField(
                    blank=True,
                    null=True,
                    verbose_name='Notes',
                    help_text='Optional notes about this macro calculation',
                )),
                
                ('is_active', models.BooleanField(
                    default=True,
                    verbose_name='Active',
                    help_text='Whether this calculation is active/current',
                )),
                
                ('user', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='macro_calculations',
                    to=settings.AUTH_USER_MODEL,
                    verbose_name='User',
                )),
            ],
            options={
                'app_label': 'macro',
                'db_table': 'macro_calculation',
                'verbose_name': 'Macro Calculation',
                'verbose_name_plural': 'Macro Calculations',
                'ordering': ['-created'],
            },
        ),
        
        migrations.CreateModel(
            name='MacroGoalModel',
            fields=[
                ('created', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('_id', models.IntegerField(db_index=True, editable=False, null=True, unique=True)),
                
                ('target_carb_grams', models.PositiveIntegerField(
                    validators=[
                        django.core.validators.MinValueValidator(10),
                        django.core.validators.MaxValueValidator(2000),
                    ],
                    verbose_name='Target Carbs (g)',
                    help_text='Target daily carbohydrates in grams',
                )),
                
                ('target_protein_grams', models.PositiveIntegerField(
                    validators=[
                        django.core.validators.MinValueValidator(20),
                        django.core.validators.MaxValueValidator(500),
                    ],
                    verbose_name='Target Protein (g)',
                    help_text='Target daily protein in grams',
                )),
                
                ('target_fat_grams', models.PositiveIntegerField(
                    validators=[
                        django.core.validators.MinValueValidator(10),
                        django.core.validators.MaxValueValidator(300),
                    ],
                    verbose_name='Target Fat (g)',
                    help_text='Target daily fat in grams',
                )),
                
                ('adherence_threshold', models.DecimalField(
                    max_digits=4,
                    decimal_places=1,
                    default=Decimal('90.0'),
                    validators=[
                        django.core.validators.MinValueValidator(Decimal('50.0')),
                        django.core.validators.MaxValueValidator(Decimal('100.0')),
                    ],
                    verbose_name='Adherence Threshold (%)',
                    help_text='Minimum adherence percentage to consider goal met',
                )),
                
                ('goal_description', models.TextField(
                    blank=True,
                    null=True,
                    verbose_name='Goal Description',
                    help_text='Description of the macro goal',
                )),
                
                ('is_achieved', models.BooleanField(
                    default=False,
                    verbose_name='Achieved',
                    help_text='Whether this goal has been achieved',
                )),
                
                ('achieved_date', models.DateField(
                    blank=True,
                    null=True,
                    verbose_name='Achieved Date',
                    help_text='Date when goal was achieved',
                )),
                
                ('notes', models.TextField(
                    blank=True,
                    null=True,
                    verbose_name='Notes',
                    help_text='Additional notes about this goal',
                )),
                
                ('user', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='macro_goals',
                    to=settings.AUTH_USER_MODEL,
                    verbose_name='User',
                )),
            ],
            options={
                'app_label': 'macro',
                'db_table': 'macro_goal',
                'verbose_name': 'Macro Goal',
                'verbose_name_plural': 'Macro Goals',
                'ordering': ['-created'],
            },
        ),
        
        
        migrations.AddIndex(
            model_name='macrocalculationmodel',
            index=models.Index(fields=['user', '-created'], name='macro_calc_user_created_idx'),
        ),
        migrations.AddIndex(
            model_name='macrocalculationmodel',
            index=models.Index(fields=['user', 'is_active'], name='macro_calc_user_active_idx'),
        ),
        migrations.AddIndex(
            model_name='macrogoalmodel',
            index=models.Index(fields=['user', '-created'], name='macro_goal_user_created_idx'),
        ),
        migrations.AddIndex(
            model_name='macrogoalmodel',
            index=models.Index(fields=['is_achieved'], name='macro_goal_achieved_idx'),
        ),
    ] 