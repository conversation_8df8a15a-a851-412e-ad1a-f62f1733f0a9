from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from decimal import Decimal, ROUND_HALF_UP

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class BMICategory(models.TextChoices):
    """BMI category choices based on WHO standards"""

    UNDERWEIGHT = "underweight", _("Underweight")
    NORMAL = "normal", _("Normal weight")
    OVERWEIGHT = "overweight", _("Overweight")
    OBESE_CLASS_1 = "obese_1", _("Obese Class I")
    OBESE_CLASS_2 = "obese_2", _("Obese Class II")
    OBESE_CLASS_3 = "obese_3", _("Obese Class III")


class BMICalculationModel(AbstractAutoIncrementModel):
    """
    Model to store BMI calculations for users with historical tracking.

    Follows WHO BMI categories:
    - Underweight: < 18.5
    - Normal weight: 18.5–24.9
    - Overweight: 25–29.9
    - Obese Class I: 30–34.9
    - Obese Class II: 35–39.9
    - Obese Class III: ≥ 40
    """

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="bmi_calculations",
        verbose_name=_("User"),
    )

    weight_kg = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("0.01")),
            MaxValueValidator(Decimal("999.99")),
        ],
        verbose_name=_("Weight (kg)"),
        help_text=_("Weight in kilograms (0.01 - 999.99)"),
    )

    height_cm = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("1.00")),
            MaxValueValidator(Decimal("300.00")),
        ],
        verbose_name=_("Height (cm)"),
        help_text=_("Height in centimeters (1.00 - 300.00)"),
    )

    bmi_value = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_("BMI Value"),
        help_text=_("Calculated BMI value"),
    )

    category = models.CharField(
        max_length=20, choices=BMICategory.choices, verbose_name=_("BMI Category")
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Optional notes about this BMI calculation"),
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this calculation is active/current"),
    )

    class Meta:
        app_label = "apps_calculators_bmi"
        db_table = "bmi_bmicalculation"
        verbose_name = _("BMI Calculation")
        verbose_name_plural = _("BMI Calculations")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["category"]),
            models.Index(fields=["is_active"]),
        ]

    def __str__(self):
        return (
            f"{self.user.email} - BMI: {self.bmi_value} ({self.get_category_display()})"
        )

    @staticmethod
    def calculate_bmi(weight_kg: Decimal, height_cm: Decimal) -> Decimal:
        """
        Calculate BMI given weight in kg and height in cm.

        Args:
            weight_kg: Weight in kilograms
            height_cm: Height in centimeters

        Returns:
            BMI value rounded to 2 decimal places

        Raises:
            ValueError: If height is zero or negative
        """
        if height_cm <= 0:
            raise ValueError("Height must be positive and non-zero")

        height_m = height_cm / Decimal("100.0")
        bmi = weight_kg / (height_m * height_m)

        return bmi.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def get_bmi_category(bmi_value: Decimal) -> str:
        """
        Determine BMI category based on WHO standards.

        Args:
            bmi_value: Calculated BMI value

        Returns:
            BMI category string
        """
        if bmi_value < Decimal("18.5"):
            return BMICategory.UNDERWEIGHT
        elif bmi_value < Decimal("25.0"):
            return BMICategory.NORMAL
        elif bmi_value < Decimal("30.0"):
            return BMICategory.OVERWEIGHT
        elif bmi_value < Decimal("35.0"):
            return BMICategory.OBESE_CLASS_1
        elif bmi_value < Decimal("40.0"):
            return BMICategory.OBESE_CLASS_2
        else:
            return BMICategory.OBESE_CLASS_3

    @property
    def height_m(self) -> Decimal:
        """Convert height from cm to meters"""
        return self.height_cm / Decimal("100.0")

    @property
    def bmi_status_message(self) -> str:
        """Get a descriptive message about BMI status"""
        category_messages = {
            BMICategory.UNDERWEIGHT: _(
                "You are underweight. Consider consulting a healthcare provider."
            ),
            BMICategory.NORMAL: _(
                "You have a healthy weight. Keep maintaining your current lifestyle."
            ),
            BMICategory.OVERWEIGHT: _(
                "You are overweight. Consider a balanced diet and regular exercise."
            ),
            BMICategory.OBESE_CLASS_1: _(
                "You are in the obese category. Consider consulting a healthcare provider."
            ),
            BMICategory.OBESE_CLASS_2: _(
                "You are severely obese. Please consult a healthcare provider."
            ),
            BMICategory.OBESE_CLASS_3: _(
                "You are very severely obese. Please consult a healthcare provider immediately."
            ),
        }
        return category_messages.get(self.category, _("BMI category unknown."))

    def save(self, *args, **kwargs):
        """Override save to auto-calculate BMI and category"""

        self.bmi_value = self.calculate_bmi(self.weight_kg, self.height_cm)

        self.category = self.get_bmi_category(self.bmi_value)

        if self.is_active and not self.pk:
            BMICalculationModel.objects.filter(user=self.user, is_active=True).update(
                is_active=False
            )

        super().save(*args, **kwargs)

    def clean(self):
        """Validate the model data"""
        from django.core.exceptions import ValidationError

        if self.weight_kg <= 0:
            raise ValidationError({"weight_kg": _("Weight must be positive.")})

        if self.height_cm <= 0:
            raise ValidationError({"height_cm": _("Height must be positive.")})

        if self.weight_kg > Decimal("999.99"):
            raise ValidationError({"weight_kg": _("Weight seems unreasonably high.")})

        if self.height_cm > Decimal("300.00"):
            raise ValidationError({"height_cm": _("Height seems unreasonably high.")})

        if self.height_cm < Decimal("50.00"):
            raise ValidationError({"height_cm": _("Height seems unreasonably low.")})

        if len(self.notes) > 255:
            raise ValidationError({"notes": _("Notes are too long.")})


class BMIGoalModel(AbstractAutoIncrementModel):
    """
    Model to store user BMI goals and target weights.
    """

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="bmi_goals", verbose_name=_("User")
    )

    target_weight_kg = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("0.01")),
            MaxValueValidator(Decimal("999.99")),
        ],
        verbose_name=_("Target Weight (kg)"),
        help_text=_("Target weight in kilograms"),
    )

    target_bmi = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name=_("Target BMI"),
        help_text=_("Target BMI value"),
    )

    target_category = models.CharField(
        max_length=20,
        choices=BMICategory.choices,
        verbose_name=_("Target BMI Category"),
    )

    current_height_cm = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("1.00")),
            MaxValueValidator(Decimal("300.00")),
        ],
        verbose_name=_("Current Height (cm)"),
        help_text=_("Current height in centimeters for goal calculation"),
    )

    target_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Target Date"),
        help_text=_("Optional target date to achieve this goal"),
    )

    is_achieved = models.BooleanField(
        default=False,
        verbose_name=_("Achieved"),
        help_text=_("Whether this goal has been achieved"),
    )

    achieved_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Achieved Date"),
        help_text=_("Date when goal was achieved"),
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Notes about this goal"),
    )

    class Meta:
        app_label = "apps_calculators_bmi"
        db_table = "bmi_bmigoal"
        verbose_name = _("BMI Goal")
        verbose_name_plural = _("BMI Goals")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["is_achieved"]),
        ]

    def __str__(self):
        return f"{self.user.email} - Target BMI: {self.target_bmi}"

    def save(self, *args, **kwargs):
        """Override save to auto-calculate target BMI and category"""

        self.target_bmi = BMICalculationModel.calculate_bmi(
            self.target_weight_kg, self.current_height_cm
        )

        self.target_category = BMICalculationModel.get_bmi_category(self.target_bmi)

        super().save(*args, **kwargs)


BMICalculation = BMICalculationModel
BMIGoal = BMIGoalModel
