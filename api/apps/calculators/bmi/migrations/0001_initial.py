

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="BMIGoalModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "target_weight_kg",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Target weight in kilograms",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01")),
                            django.core.validators.MaxValueValidator(Decimal("999.99")),
                        ],
                        verbose_name="Target Weight (kg)",
                    ),
                ),
                (
                    "target_bmi",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Target BMI value",
                        max_digits=5,
                        verbose_name="Target BMI",
                    ),
                ),
                (
                    "target_category",
                    models.CharField(
                        choices=[
                            ("underweight", "Underweight"),
                            ("normal", "Normal weight"),
                            ("overweight", "Overweight"),
                            ("obese_1", "Obese Class I"),
                            ("obese_2", "Obese Class II"),
                            ("obese_3", "Obese Class III"),
                        ],
                        max_length=20,
                        verbose_name="Target BMI Category",
                    ),
                ),
                (
                    "current_height_cm",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Current height in centimeters for goal calculation",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.00")),
                            django.core.validators.MaxValueValidator(Decimal("300.00")),
                        ],
                        verbose_name="Current Height (cm)",
                    ),
                ),
                (
                    "target_date",
                    models.DateField(
                        blank=True,
                        help_text="Optional target date to achieve this goal",
                        null=True,
                        verbose_name="Target Date",
                    ),
                ),
                (
                    "is_achieved",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this goal has been achieved",
                        verbose_name="Achieved",
                    ),
                ),
                (
                    "achieved_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when goal was achieved",
                        null=True,
                        verbose_name="Achieved Date",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Notes about this goal",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bmi_goals",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "BMI Goal",
                "verbose_name_plural": "BMI Goals",
                "db_table": "bmi_bmigoal",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="bmi_bmigoal_user_id_905296_idx",
                    ),
                    models.Index(
                        fields=["is_achieved"], name="bmi_bmigoal_is_achi_8278d3_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BMICalculationModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "weight_kg",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Weight in kilograms (0.01 - 999.99)",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01")),
                            django.core.validators.MaxValueValidator(Decimal("999.99")),
                        ],
                        verbose_name="Weight (kg)",
                    ),
                ),
                (
                    "height_cm",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Height in centimeters (1.00 - 300.00)",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.00")),
                            django.core.validators.MaxValueValidator(Decimal("300.00")),
                        ],
                        verbose_name="Height (cm)",
                    ),
                ),
                (
                    "bmi_value",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Calculated BMI value",
                        max_digits=5,
                        verbose_name="BMI Value",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("underweight", "Underweight"),
                            ("normal", "Normal weight"),
                            ("overweight", "Overweight"),
                            ("obese_1", "Obese Class I"),
                            ("obese_2", "Obese Class II"),
                            ("obese_3", "Obese Class III"),
                        ],
                        max_length=20,
                        verbose_name="BMI Category",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Optional notes about this BMI calculation",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this calculation is active/current",
                        verbose_name="Active",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bmi_calculations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "BMI Calculation",
                "verbose_name_plural": "BMI Calculations",
                "db_table": "bmi_bmicalculation",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="bmi_bmicalc_user_id_c7c72f_idx",
                    ),
                    models.Index(
                        fields=["category"], name="bmi_bmicalc_categor_2f1c20_idx"
                    ),
                    models.Index(
                        fields=["is_active"], name="bmi_bmicalc_is_acti_8516ec_idx"
                    ),
                ],
            },
        ),
    ]
