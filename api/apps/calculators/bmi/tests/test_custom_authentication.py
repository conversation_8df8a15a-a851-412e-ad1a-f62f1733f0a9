"""
Custom Authentication Tests

This module tests the custom JWT authentication class to ensure proper
error handling and user-friendly error messages for authentication issues.
"""

from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.test import override_settings
from rest_framework_simplejwt.tokens import AccessToken
from unittest.mock import patch

from apps.calculators.bmi.models import BMICalculationModel as BMICalculation

User = get_user_model()


@override_settings(
    PASSWORD_HASHERS=["django.contrib.auth.hashers.MD5PasswordHasher"],
    CELERY_TASK_ALWAYS_EAGER=True,
    BROKER_BACKEND="memory",
)
class CustomAuthenticationTest(APITestCase):
    """
    Test custom JWT authentication with enhanced error handling.
    """

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Test User',
            username='testuser',
            password='testpass123'
        )
        
        self.calculation_url = reverse('apps_calculators_bmi:calculation-list-create')

    def test_valid_authentication_works(self):
        """Test that valid authentication still works with custom class"""
        token = AccessToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.post(self.calculation_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        # Should not be authentication error
        self.assertNotEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_user_not_found_error_format(self):
        """Test that 'User not found' error has enhanced format"""
        # Create user, get token, delete user
        temp_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Temp User',
            username='tempuser',
            password='temppass123'
        )
        
        token = AccessToken.for_user(temp_user)
        temp_user.delete()
        
        # Try to use token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.post(self.calculation_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Check enhanced error format
        error_data = response.data.get('error', {})
        if isinstance(error_data, dict):
            self.assertIn('code', error_data)
            self.assertEqual(error_data.get('code'), 'user_not_found')
            self.assertIn('help', error_data)
        else:
            # If it's a string, check it contains the right message
            self.assertIn('User not found', str(error_data))

    def test_inactive_user_error_format(self):
        """Test that inactive user error has proper format"""
        # Create user and make inactive
        inactive_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Inactive User',
            username='inactiveuser',
            password='testpass123'
        )
        # Set inactive after creation
        inactive_user.is_active = False
        inactive_user.save()
        
        token = AccessToken.for_user(inactive_user)
        
        # Try to use token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.post(self.calculation_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Check error format
        error_data = response.data.get('error', {})
        if isinstance(error_data, dict):
            self.assertIn('code', error_data)
            self.assertIn('user_inactive', error_data.get('code', ''))

    def test_invalid_token_error_format(self):
        """Test that invalid token errors have proper format"""
        # Use completely invalid token
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid.token.here')
        
        response = self.client.post(self.calculation_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Should have error information
        self.assertIn('error', response.data)

    def test_missing_authorization_header(self):
        """Test behavior when no authorization header is provided"""
        response = self.client.post(self.calculation_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def tearDown(self):
        """Clean up test data"""
        self.client.credentials()
        User.objects.filter(email__contains='@example.com').delete()
        BMICalculation.objects.all().delete() 