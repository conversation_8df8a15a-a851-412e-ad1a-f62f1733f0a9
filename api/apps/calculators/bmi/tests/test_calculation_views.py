"""
Tests for BMI Calculation Views

This module contains comprehensive tests for BMI calculation views,
including authentication, authorization, and functionality testing.
"""

from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.test import override_settings
from decimal import Decimal

from apps.calculators.bmi.models import BMICalculationModel as BMICalculation

User = get_user_model()


@override_settings(
    PASSWORD_HASHERS=["django.contrib.auth.hashers.MD5PasswordHasher"],
    CELERY_TASK_ALWAYS_EAGER=True,
    BROKER_BACKEND="memory",
)
class BMICalculationViewsTest(APITestCase):
    """Test cases for BMI calculation views"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test User'
        )
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Other User'
        )
        
        
        self.calculation = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('70.00'),
            height_cm=Decimal('175.00'),
            is_active=True
        )
        self.other_calculation = BMICalculation.objects.create(
            user=self.other_user,
            weight_kg=Decimal('80.00'),
            height_cm=Decimal('180.00')
        )
    
    def test_calculate_bmi_anonymous_success(self):
        """Test BMI calculation endpoint (anonymous access)"""
        url = reverse('apps_calculators_bmi:calculate')
        data = {
            'weight_kg': '70.00',
            'height_cm': '175.00'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.assertEqual(response.data['bmi_value'], Decimal('22.86'))
        self.assertEqual(response.data['category'], 'normal')
        self.assertIn('healthy weight', response.data['bmi_status_message'].lower())
    
    def test_calculate_bmi_validation(self):
        """Test BMI calculation validation"""
        url = reverse('apps_calculators_bmi:calculate')
        
        
        data = {'weight_kg': '-10', 'height_cm': '175'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        data = {'weight_kg': '70', 'height_cm': '0'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        data = {'weight_kg': '1000', 'height_cm': '175'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_create_bmi_calculation_authenticated(self):
        """Test creating BMI calculation via API (authenticated)"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:calculation-list-create')
        data = {
            'weight_kg': '72.00',
            'height_cm': '175.00',
            'notes': 'Test calculation'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        calculation = BMICalculation.objects.get(id=response.data['id'])
        self.assertEqual(calculation.user, self.user)
        self.assertEqual(calculation.weight_kg, Decimal('72.00'))
        self.assertEqual(calculation.bmi_value, Decimal('23.51'))
    
    def test_create_bmi_calculation_unauthenticated(self):
        """Test creating BMI calculation without authentication fails"""
        url = reverse('apps_calculators_bmi:calculation-list-create')
        data = {
            'weight_kg': '72.00',
            'height_cm': '175.00',
            'notes': 'Test calculation'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_list_bmi_calculations_authenticated(self):
        """Test listing user's BMI calculations"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:calculation-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        data = response.data.get('results', response.data) if isinstance(response.data, dict) else response.data
        self.assertGreaterEqual(len(data), 1)  
        
        
        calculation_ids = [calc['id'] for calc in data]
        self.assertIn(str(self.calculation.id), calculation_ids)
    
    def test_list_bmi_calculations_user_isolation(self):
        """Test that users can only see their own calculations"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:calculation-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        data = response.data.get('results', response.data) if isinstance(response.data, dict) else response.data
        self.assertGreaterEqual(len(data), 1)  
        
        
        calculation_ids = [calc['id'] for calc in data]
        self.assertIn(str(self.calculation.id), calculation_ids)
        self.assertNotIn(str(self.other_calculation.id), calculation_ids)
    
    def test_retrieve_bmi_calculation(self):
        """Test retrieving a specific BMI calculation"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': self.calculation.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.calculation.id))
        self.assertEqual(response.data['weight_kg'], '70.00')
    
    def test_retrieve_other_user_calculation_fails(self):
        """Test that users cannot access other users' calculations"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': self.other_calculation.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_update_bmi_calculation(self):
        """Test updating a BMI calculation"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': self.calculation.id})
        data = {
            'weight_kg': '75.00',
            'height_cm': '175.00',
            'notes': 'Updated calculation'
        }
        
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.calculation.refresh_from_db()
        self.assertEqual(self.calculation.weight_kg, Decimal('75.00'))
        self.assertEqual(self.calculation.notes, 'Updated calculation')
    
    def test_delete_bmi_calculation(self):
        """Test deleting a BMI calculation"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': self.calculation.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        
        self.assertFalse(
            BMICalculation.objects.filter(id=self.calculation.id).exists()
        )
    
    def test_set_active_calculation(self):
        """Test setting a calculation as active"""
        self.client.force_authenticate(user=self.user)
        
        
        calculation2 = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('75.00'),
            height_cm=Decimal('175.00')
        )
        
        url = reverse('apps_calculators_bmi:set-active-calculation', kwargs={'calculation_id': calculation2.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        calculation2.refresh_from_db()
        self.calculation.refresh_from_db()
        
        self.assertTrue(calculation2.is_active)
        self.assertFalse(self.calculation.is_active)
    
    def test_set_active_calculation_other_user_fails(self):
        """Test that users cannot set other users' calculations as active"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:set-active-calculation', kwargs={'calculation_id': self.other_calculation.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_bmi_history_view(self):
        """Test BMI history endpoint"""
        self.client.force_authenticate(user=self.user)
        
        
        BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('72.00'),
            height_cm=Decimal('175.00')
        )
        
        url = reverse('apps_calculators_bmi:history')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 2)  
    
    def test_bmi_history_filtering(self):
        """Test BMI history with date filtering"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:history')
        
        
        response = self.client.get(url, {'start_date': '2023-01-01', 'end_date': '2024-12-31'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_authentication_required_for_protected_endpoints(self):
        """Test that authentication is required for protected endpoints"""
        protected_urls = [
            reverse('apps_calculators_bmi:calculation-list-create'),
            reverse('apps_calculators_bmi:history'),
            reverse('apps_calculators_bmi:stats'),
        ]
        
        for url in protected_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 