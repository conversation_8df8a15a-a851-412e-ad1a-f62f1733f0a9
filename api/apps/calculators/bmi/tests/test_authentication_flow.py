"""
Authentication Flow Tests for BMI Calculator

This module contains comprehensive tests to diagnose and fix authentication issues
in the BMI calculator, specifically the "User not found" error when accessing
protected endpoints.
"""

from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.test import override_settings
from rest_framework_simplejwt.tokens import AccessToken
from decimal import Decimal
import json

from apps.calculators.bmi.models import BMICalculationModel as BMICalculation

User = get_user_model()


@override_settings(
    PASSWORD_HASHERS=["django.contrib.auth.hashers.MD5PasswordHasher"],
    CELERY_TASK_ALWAYS_EAGER=True,
    BROKER_BACKEND="memory",
)
class BMIAuthenticationFlowTest(APITestCase):
    """
    Comprehensive authentication flow tests for BMI calculator endpoints.
    
    This test class diagnoses and fixes authentication issues including:
    - JWT token validation
    - User existence validation
    - Authentication header handling
    - Token refresh flows
    """

    def setUp(self):
        """Set up test data including users and authentication tokens"""
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Test User',
            username='testuser',
            password='testpass123'
        )
        
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Admin User',
            username='adminuser',
            password='adminpass123',
            is_staff=True,
            is_superuser=True
        )
        
        
        self.user_token = AccessToken.for_user(self.user)
        self.admin_token = AccessToken.for_user(self.admin_user)
        
        
        self.calculation_list_url = reverse('apps_calculators_bmi:calculation-list-create')
        self.history_url = reverse('apps_calculators_bmi:history')
        self.stats_url = reverse('apps_calculators_bmi:stats')

    def test_user_exists_in_database(self):
        """Test that the created users actually exist in the database"""
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())
        
        
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.fullname, 'Test User')
        self.assertEqual(user.username, 'testuser')
        self.assertTrue(user.check_password('testpass123'))

    def test_jwt_token_generation(self):
        """Test that JWT tokens are generated correctly"""
        
        token = AccessToken.for_user(self.user)
        self.assertIsNotNone(token)
        
        
        self.assertEqual(token['user_id'], self.user.id)
        
        
        self.assertIsNotNone(str(token))

    def test_unauthenticated_request_to_protected_endpoint(self):
        """Test that unauthenticated requests are properly rejected"""
        response = self.client.post(self.calculation_list_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('error', response.data)

    def test_authenticated_request_with_valid_token(self):
        """Test that authenticated requests with valid tokens work"""
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')
        
        response = self.client.post(self.calculation_list_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        
        self.assertNotEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {response.data}")

    def test_authenticated_request_with_invalid_token(self):
        """Test that requests with invalid tokens are rejected"""
        
        invalid_token = "invalid.jwt.token"
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {invalid_token}')
        
        response = self.client.post(self.calculation_list_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_authenticated_request_with_deleted_user_token(self):
        """Test behavior when token is valid but user doesn't exist"""
        
        temp_user = User.objects.create_user(
            email='<EMAIL>',
            fullname='Temp User',
            username='tempuser',
            password='temppass123'
        )
        
        temp_token = AccessToken.for_user(temp_user)
        
        
        temp_user.delete()
        
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {temp_token}')
        
        response = self.client.post(self.calculation_list_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn('error', response.data)
        
        
        print(f"Error when user deleted: {response.data}")

    def test_history_endpoint_authentication(self):
        """Test authentication for the BMI history endpoint"""
        
        response = self.client.get(self.history_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')
        response = self.client.get(self.history_url)
        
        
        self.assertNotEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_stats_endpoint_authentication(self):
        """Test authentication for the BMI stats endpoint"""
        
        response = self.client.get(self.stats_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')
        response = self.client.get(self.stats_url)
        
        
        self.assertNotEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_token_header_formats(self):
        """Test different authentication header formats"""
        test_cases = [
            f'Bearer {self.user_token}',  
            f'JWT {self.user_token}',     
            str(self.user_token),         
        ]
        
        for auth_header in test_cases:
            self.client.credentials(HTTP_AUTHORIZATION=auth_header)
            response = self.client.get(self.history_url)
            
            print(f"Auth header '{auth_header[:20]}...': Status {response.status_code}")
            if response.status_code == status.HTTP_401_UNAUTHORIZED:
                print(f"Error: {response.data}")

    def test_jwt_token_decoding_manually(self):
        """Test manual JWT token decoding to verify token structure"""
        from rest_framework_simplejwt.authentication import JWTAuthentication
        from rest_framework_simplejwt.exceptions import InvalidToken
        
        jwt_auth = JWTAuthentication()
        
        try:
            
            validated_token = jwt_auth.get_validated_token(str(self.user_token))
            user = jwt_auth.get_user(validated_token)
            
            self.assertEqual(user, self.user)
            print(f"Token validation successful for user: {user.email}")
            
        except InvalidToken as e:
            self.fail(f"Token validation failed: {e}")

    def test_user_model_and_jwt_compatibility(self):
        """Test that the user model is compatible with JWT authentication"""
        
        from django.conf import settings
        jwt_settings = getattr(settings, 'SIMPLE_JWT', {})
        user_id_field = jwt_settings.get('USER_ID_FIELD', 'id')
        
        
        self.assertTrue(hasattr(self.user, user_id_field))
        
        
        user_id_value = getattr(self.user, user_id_field)
        self.assertIsNotNone(user_id_value)
        
        print(f"User ID field '{user_id_field}': {user_id_value}")

    def test_complete_authentication_flow(self):
        """Test the complete authentication flow from login to API access"""
        
        response = self.client.post(self.calculation_list_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')
        
        
        response = self.client.post(self.calculation_list_url, {
            'weight_kg': '70.0',
            'height_cm': '175.0'
        })
        
        if response.status_code == status.HTTP_401_UNAUTHORIZED:
            print(f"Authentication failed: {response.data}")
            
            print(f"User exists: {User.objects.filter(id=self.user.id).exists()}")
            print(f"Token user_id: {self.user_token['user_id']}")
            print(f"Actual user ID: {self.user.id}")
        
        self.assertNotEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        
        history_response = self.client.get(self.history_url)
        self.assertNotEqual(history_response.status_code, status.HTTP_401_UNAUTHORIZED)

    def tearDown(self):
        """Clean up test data"""
        
        self.client.credentials()
        
        
        User.objects.filter(email__in=['<EMAIL>', '<EMAIL>']).delete()
        BMICalculation.objects.all().delete() 