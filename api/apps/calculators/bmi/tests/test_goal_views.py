"""
Tests for BMI Goal Views

This module contains comprehensive tests for BMI goal views,
including authentication, authorization, and functionality testing.
"""

from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.test import override_settings
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta

from apps.calculators.bmi.models import BMIGoalModel as BMIGoal

User = get_user_model()


@override_settings(
    PASSWORD_HASHERS=["django.contrib.auth.hashers.MD5PasswordHasher"],
    CELERY_TASK_ALWAYS_EAGER=True,
    BROKER_BACKEND="memory",
)
class BMIGoalViewsTest(APITestCase):
    """Test cases for BMI goal views"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test User'
        )
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Other User'
        )
        
        
        self.goal = BMIGoal.objects.create(
            user=self.user,
            target_weight_kg=Decimal('65.00'),
            current_height_cm=Decimal('175.00'),
            target_date=date.today() + timedelta(days=90),
            notes='Test goal'
        )
        self.other_goal = BMIGoal.objects.create(
            user=self.other_user,
            target_weight_kg=Decimal('70.00'),
            current_height_cm=Decimal('180.00'),
            target_date=date.today() + timedelta(days=60)
        )
    
    def test_create_bmi_goal_authenticated(self):
        """Test creating BMI goal via API (authenticated)"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-list-create')
        data = {
            'target_weight_kg': '68.00',
            'current_height_cm': '175.00',
            'target_date': (date.today() + timedelta(days=120)).isoformat(),
            'notes': 'New goal'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        goal = BMIGoal.objects.get(id=response.data['id'])
        self.assertEqual(goal.user, self.user)
        self.assertEqual(goal.target_weight_kg, Decimal('68.00'))
        self.assertEqual(goal.target_bmi, Decimal('22.20'))
    
    def test_create_bmi_goal_unauthenticated(self):
        """Test creating BMI goal without authentication fails"""
        url = reverse('apps_calculators_bmi:goal-list-create')
        data = {
            'target_weight_kg': '68.00',
            'current_height_cm': '175.00',
            'target_date': (date.today() + timedelta(days=120)).isoformat()
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_list_bmi_goals_authenticated(self):
        """Test listing user's BMI goals"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        data = response.data.get('results', response.data) if isinstance(response.data, dict) else response.data
        self.assertGreaterEqual(len(data), 1)  
        
        
        goal_ids = [goal['id'] for goal in data]
        self.assertIn(str(self.goal.id), goal_ids)
    
    def test_list_bmi_goals_user_isolation(self):
        """Test that users can only see their own goals"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        data = response.data.get('results', response.data) if isinstance(response.data, dict) else response.data
        self.assertGreaterEqual(len(data), 1)  
        
        
        goal_ids = [goal['id'] for goal in data]
        self.assertIn(str(self.goal.id), goal_ids)
        self.assertNotIn(str(self.other_goal.id), goal_ids)
    
    def test_retrieve_bmi_goal(self):
        """Test retrieving a specific BMI goal"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': self.goal.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.goal.id))
        self.assertEqual(response.data['target_weight_kg'], '65.00')
    
    def test_retrieve_other_user_goal_fails(self):
        """Test that users cannot access other users' goals"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': self.other_goal.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_update_bmi_goal(self):
        """Test updating a BMI goal"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': self.goal.id})
        data = {
            'target_weight_kg': '67.00',
            'current_height_cm': '175.00',
            'notes': 'Updated goal'
        }
        
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.goal.refresh_from_db()
        self.assertEqual(self.goal.target_weight_kg, Decimal('67.00'))
        self.assertEqual(self.goal.notes, 'Updated goal')
    
    def test_delete_bmi_goal(self):
        """Test deleting a BMI goal"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': self.goal.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        
        self.assertFalse(
            BMIGoal.objects.filter(id=self.goal.id).exists()
        )
    
    def test_mark_goal_achieved(self):
        """Test marking a goal as achieved"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:mark-goal-achieved', kwargs={'goal_id': self.goal.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.goal.refresh_from_db()
        self.assertTrue(self.goal.is_achieved)
        self.assertIsNotNone(self.goal.achieved_date)
    
    def test_mark_goal_achieved_other_user_fails(self):
        """Test that users cannot mark other users' goals as achieved"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:mark-goal-achieved', kwargs={'goal_id': self.other_goal.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_goal_validation(self):
        """Test BMI goal validation"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:goal-list-create')
        
        
        data = {
            'target_weight_kg': '-10',
            'current_height_cm': '175',
            'target_date': (date.today() + timedelta(days=30)).isoformat()
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        data = {
            'target_weight_kg': '70',
            'current_height_cm': '0',
            'target_date': (date.today() + timedelta(days=30)).isoformat()
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        data = {
            'target_weight_kg': '70',
            'current_height_cm': '175',
            'target_date': (date.today() - timedelta(days=30)).isoformat()
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST) 