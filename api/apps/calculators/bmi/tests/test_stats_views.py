"""
Tests for BMI Stats Views

This module contains comprehensive tests for BMI statistics views,
including authentication, authorization, and functionality testing.
"""

from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.test import override_settings
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta

from apps.calculators.bmi.models import BMICalculationModel as BMICalculation, BMIGoalModel as BMIGoal

User = get_user_model()


@override_settings(
    PASSWORD_HASHERS=["django.contrib.auth.hashers.MD5PasswordHasher"],
    CELERY_TASK_ALWAYS_EAGER=True,
    BROKER_BACKEND="memory",
)
class BMIStatsViewsTest(APITestCase):
    """Test cases for BMI stats views"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test User'
        )
        
        
        self.calculation1 = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('70.00'),
            height_cm=Decimal('175.00'),
            is_active=True
        )
        self.calculation2 = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('72.00'),
            height_cm=Decimal('175.00'),
            is_active=False
        )
        
        
        self.goal = BMIGoal.objects.create(
            user=self.user,
            target_weight_kg=Decimal('65.00'),
            current_height_cm=Decimal('175.00'),
            is_achieved=False
        )
    
    def test_bmi_stats_authenticated(self):
        """Test BMI statistics endpoint"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        expected_fields = [
            'current_bmi',
            'current_category',
            'total_calculations',
            'calculations_this_month',
            'weight_trend',
            'bmi_trend',
            'has_active_goals',
            'active_goals_count'
        ]
        
        for field in expected_fields:
            self.assertIn(field, response.data)
        
        self.assertEqual(response.data['total_calculations'], 2)
        self.assertEqual(response.data['current_bmi'], '22.86')
        self.assertEqual(response.data['current_category'], 'normal')
        self.assertTrue(response.data['has_active_goals'])
        self.assertEqual(response.data['active_goals_count'], 1)
    
    def test_bmi_stats_unauthenticated(self):
        """Test BMI statistics endpoint without authentication"""
        url = reverse('apps_calculators_bmi:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_bmi_stats_no_data(self):
        """Test BMI statistics with no data"""
        
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Empty User'
        )
        self.client.force_authenticate(user=user)
        
        url = reverse('apps_calculators_bmi:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_calculations'], 0)
        self.assertIsNone(response.data['current_bmi'])
        self.assertFalse(response.data['has_active_goals'])
        self.assertEqual(response.data['active_goals_count'], 0)
    
    def test_bmi_info_anonymous(self):
        """Test BMI information endpoint (anonymous access)"""
        url = reverse('apps_calculators_bmi:info')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('categories', response.data)
        self.assertIn('formula', response.data)
        self.assertIn('units', response.data)
        self.assertIn('recommendations', response.data)
        
        
        categories = response.data['categories']
        self.assertEqual(len(categories), 6)  
        
        for category in categories:
            self.assertIn('value', category)
            self.assertIn('label', category)
            self.assertIn('description', category)
            self.assertIn('color', category)
    
    def test_bmi_info_authenticated(self):
        """Test BMI information endpoint (authenticated access)"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:info')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.assertIn('categories', response.data)
        self.assertIn('formula', response.data)
    
    def test_bmi_trends_calculation(self):
        """Test BMI trends calculation with multiple data points"""
        self.client.force_authenticate(user=self.user)
        
        
        BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('68.00'),
            height_cm=Decimal('175.00')
        )
        BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('74.00'),
            height_cm=Decimal('175.00')
        )
        
        url = reverse('apps_calculators_bmi:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn(response.data['weight_trend'], ['increasing', 'decreasing', 'stable'])
        self.assertIn(response.data['bmi_trend'], ['increasing', 'decreasing', 'stable'])
    
    def test_bmi_stats_monthly_calculations(self):
        """Test monthly calculations count"""
        self.client.force_authenticate(user=self.user)
        
        
        old_calculation = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('69.00'),
            height_cm=Decimal('175.00')
        )
        old_calculation.created = timezone.now() - timedelta(days=45)
        old_calculation.save()
        
        url = reverse('apps_calculators_bmi:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.assertEqual(response.data['calculations_this_month'], 2)
        self.assertEqual(response.data['total_calculations'], 3) 