"""
Comprehensive Tests for All BMI URLs

This module contains exhaustive tests for all BMI endpoints defined in urls.py,
ensuring proper authentication, authorization, and functionality following
Django REST Framework best practices.
"""

from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.test import override_settings
from decimal import Decimal
from datetime import datetime, timedelta

from apps.calculators.bmi.models import BMICalculationModel as BMICalculation, BMIGoalModel as BMIGoal

User = get_user_model()


@override_settings(
    PASSWORD_HASHERS=["django.contrib.auth.hashers.MD5PasswordHasher"],
    CELERY_TASK_ALWAYS_EAGER=True,
    BROKER_BACKEND="memory",
)
class BMIUrlsComprehensiveTest(APITestCase):
    """Comprehensive test cases for all BMI URLs"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test User'
        )
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Other User'
        )
        
        
        self.calculation = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('70.00'),
            height_cm=Decimal('175.00'),
            is_active=True
        )
        self.old_calculation = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('68.00'),
            height_cm=Decimal('175.00'),
            created=datetime.now() - timedelta(days=30)
        )
        self.other_calculation = BMICalculation.objects.create(
            user=self.other_user,
            weight_kg=Decimal('80.00'),
            height_cm=Decimal('180.00')
        )
        
        
        self.goal = BMIGoal.objects.create(
            user=self.user,
            target_weight_kg=Decimal('67.00'),
            current_height_cm=Decimal('175.00')
        )
        self.other_goal = BMIGoal.objects.create(
            user=self.other_user,
            target_weight_kg=Decimal('77.00'),
            current_height_cm=Decimal('180.00')
        )

    
    
    

    def test_calculation_list_create_get_authenticated(self):
        """Test GET /calculations/ - List BMI calculations (authenticated)"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:calculation-list-create')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        data = response.data.get('results', response.data) if isinstance(response.data, dict) else response.data
        self.assertGreaterEqual(len(data), 2)  
        
        
        calculation_ids = [calc['id'] for calc in data]
        self.assertIn(str(self.calculation.id), calculation_ids)
        self.assertIn(str(self.old_calculation.id), calculation_ids)
        self.assertNotIn(str(self.other_calculation.id), calculation_ids)

    def test_calculation_list_create_get_unauthenticated(self):
        """Test GET /calculations/ - Unauthenticated access fails"""
        url = reverse('apps_calculators_bmi:calculation-list-create')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_calculation_list_create_post_authenticated(self):
        """Test POST /calculations/ - Create BMI calculation (authenticated)"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:calculation-list-create')
        
        data = {
            'weight_kg': '72.50',
            'height_cm': '175.00',
            'notes': 'New calculation test'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        calculation = BMICalculation.objects.get(id=response.data['id'])
        self.assertEqual(calculation.user, self.user)
        self.assertEqual(calculation.weight_kg, Decimal('72.50'))
        self.assertEqual(calculation.height_cm, Decimal('175.00'))
        self.assertEqual(calculation.notes, 'New calculation test')

    def test_calculation_list_create_post_unauthenticated(self):
        """Test POST /calculations/ - Unauthenticated creation fails"""
        url = reverse('apps_calculators_bmi:calculation-list-create')
        
        data = {
            'weight_kg': '72.50',
            'height_cm': '175.00',
            'notes': 'Should fail'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_calculation_detail_get_authenticated(self):
        """Test GET /calculations/<uuid>/ - Retrieve BMI calculation"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': self.calculation.id})
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.calculation.id))
        self.assertEqual(response.data['weight_kg'], '70.00')

    def test_calculation_detail_get_other_user_fails(self):
        """Test GET /calculations/<uuid>/ - Cannot access other user's calculation"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': self.other_calculation.id})
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_calculation_detail_patch_authenticated(self):
        """Test PATCH /calculations/<uuid>/ - Update BMI calculation"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': self.calculation.id})
        
        data = {
            'weight_kg': '71.00',
            'notes': 'Updated calculation'
        }
        
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.calculation.refresh_from_db()
        self.assertEqual(self.calculation.weight_kg, Decimal('71.00'))
        self.assertEqual(self.calculation.notes, 'Updated calculation')

    def test_calculation_detail_delete_authenticated(self):
        """Test DELETE /calculations/<uuid>/ - Delete BMI calculation"""
        self.client.force_authenticate(user=self.user)
        
        
        calc_to_delete = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('69.00'),
            height_cm=Decimal('175.00')
        )
        
        url = reverse('apps_calculators_bmi:calculation-detail', kwargs={'pk': calc_to_delete.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(BMICalculation.objects.filter(id=calc_to_delete.id).exists())

    
    
    

    def test_goal_list_create_get_authenticated(self):
        """Test GET /goals/ - List BMI goals (authenticated)"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:goal-list-create')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        data = response.data.get('results', response.data) if isinstance(response.data, dict) else response.data
        self.assertGreaterEqual(len(data), 1)  
        
        
        goal_ids = [goal['id'] for goal in data]
        self.assertIn(str(self.goal.id), goal_ids)
        self.assertNotIn(str(self.other_goal.id), goal_ids)

    def test_goal_list_create_get_unauthenticated(self):
        """Test GET /goals/ - Unauthenticated access fails"""
        url = reverse('apps_calculators_bmi:goal-list-create')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_goal_list_create_post_authenticated(self):
        """Test POST /goals/ - Create BMI goal (authenticated)"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:goal-list-create')
        
        data = {
            'target_weight_kg': '65.50',
            'current_height_cm': '175.00',
            'notes': 'New goal test'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        goal = BMIGoal.objects.get(id=response.data['id'])
        self.assertEqual(goal.user, self.user)
        self.assertEqual(goal.target_weight_kg, Decimal('65.50'))
        self.assertEqual(goal.notes, 'New goal test')

    def test_goal_detail_get_authenticated(self):
        """Test GET /goals/<uuid>/ - Retrieve BMI goal"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': self.goal.id})
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.goal.id))
        self.assertEqual(response.data['target_weight_kg'], '67.00')

    def test_goal_detail_get_other_user_fails(self):
        """Test GET /goals/<uuid>/ - Cannot access other user's goal"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': self.other_goal.id})
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_goal_detail_patch_authenticated(self):
        """Test PATCH /goals/<uuid>/ - Update BMI goal"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': self.goal.id})
        
        data = {
            'target_weight_kg': '65.00',
            'notes': 'Updated goal'
        }
        
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.goal.refresh_from_db()
        self.assertEqual(self.goal.target_weight_kg, Decimal('65.00'))
        self.assertEqual(self.goal.notes, 'Updated goal')

    def test_goal_detail_delete_authenticated(self):
        """Test DELETE /goals/<uuid>/ - Delete BMI goal"""
        self.client.force_authenticate(user=self.user)
        
        
        goal_to_delete = BMIGoal.objects.create(
            user=self.user,
            target_weight_kg=Decimal('61.25'),
            current_height_cm=Decimal('175.00')
        )
        
        url = reverse('apps_calculators_bmi:goal-detail', kwargs={'pk': goal_to_delete.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(BMIGoal.objects.filter(id=goal_to_delete.id).exists())

    
    
    

    def test_history_view_authenticated(self):
        """Test GET /history/ - BMI history (authenticated)"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:history')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        if 'results' in response.data:
            results = response.data['results']
        else:
            results = response.data
        
        
        self.assertGreaterEqual(len(results), 2)
        
        
        calculation_ids = [calc['id'] for calc in results]
        self.assertIn(str(self.calculation.id), calculation_ids)
        self.assertNotIn(str(self.other_calculation.id), calculation_ids)

    def test_history_view_unauthenticated(self):
        """Test GET /history/ - Unauthenticated access fails"""
        url = reverse('apps_calculators_bmi:history')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_history_view_with_filters(self):
        """Test GET /history/ - BMI history with filters"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:history')
        
        
        today = datetime.now().strftime('%Y-%m-%d')
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        response = self.client.get(url, {'start_date': yesterday, 'end_date': today})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        response = self.client.get(url, {'category': 'normal'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        response = self.client.get(url, {'limit': '1'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        if 'results' in response.data:
            results = response.data['results']
        else:
            results = response.data
        
        self.assertLessEqual(len(results), 1)

    def test_stats_view_authenticated(self):
        """Test GET /stats/ - BMI stats (authenticated)"""
        self.client.force_authenticate(user=self.user)
        url = reverse('apps_calculators_bmi:stats')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.assertIn('total_calculations', response.data)
        self.assertTrue(response.data['total_calculations'] >= 2)

    def test_stats_view_unauthenticated(self):
        """Test GET /stats/ - Unauthenticated access fails"""
        url = reverse('apps_calculators_bmi:stats')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    
    
    

    def test_calculate_bmi_anonymous(self):
        """Test POST /calculate/ - BMI calculation (anonymous)"""
        url = reverse('apps_calculators_bmi:calculate')
        
        data = {
            'weight_kg': '70.00',
            'height_cm': '175.00'
        }
        
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.assertEqual(response.data['bmi_value'], Decimal('22.86'))
        self.assertEqual(response.data['category'], 'normal')
        self.assertIn('bmi_status_message', response.data)

    def test_calculate_bmi_validation(self):
        """Test POST /calculate/ - BMI calculation validation"""
        url = reverse('apps_calculators_bmi:calculate')
        
        
        data = {'weight_kg': '-10', 'height_cm': '175'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        data = {'weight_kg': '70', 'height_cm': '0'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        
        data = {'weight_kg': '70'}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_bmi_info_view(self):
        """Test GET /info/ - BMI information (anonymous)"""
        url = reverse('apps_calculators_bmi:info')
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.assertIn('categories', response.data)
        self.assertIn('formula', response.data)

    
    
    

    def test_set_active_calculation_authenticated(self):
        """Test POST /calculations/<uuid>/set-active/ - Set active calculation"""
        self.client.force_authenticate(user=self.user)
        
        
        new_calculation = BMICalculation.objects.create(
            user=self.user,
            weight_kg=Decimal('72.00'),
            height_cm=Decimal('175.00')
        )
        
        url = reverse('apps_calculators_bmi:set-active-calculation', 
                     kwargs={'calculation_id': new_calculation.id})
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        new_calculation.refresh_from_db()
        self.calculation.refresh_from_db()
        
        self.assertTrue(new_calculation.is_active)
        self.assertFalse(self.calculation.is_active)

    def test_set_active_calculation_other_user_fails(self):
        """Test POST /calculations/<uuid>/set-active/ - Cannot set other user's calculation active"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:set-active-calculation', 
                     kwargs={'calculation_id': self.other_calculation.id})
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_set_active_calculation_unauthenticated(self):
        """Test POST /calculations/<uuid>/set-active/ - Unauthenticated access fails"""
        url = reverse('apps_calculators_bmi:set-active-calculation', 
                     kwargs={'calculation_id': self.calculation.id})
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_mark_goal_achieved_authenticated(self):
        """Test POST /goals/<uuid>/mark-achieved/ - Mark goal as achieved"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:mark-goal-achieved', 
                     kwargs={'goal_id': self.goal.id})
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        
        self.goal.refresh_from_db()
        self.assertTrue(self.goal.is_achieved)

    def test_mark_goal_achieved_other_user_fails(self):
        """Test POST /goals/<uuid>/mark-achieved/ - Cannot mark other user's goal"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('apps_calculators_bmi:mark-goal-achieved', 
                     kwargs={'goal_id': self.other_goal.id})
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_mark_goal_achieved_unauthenticated(self):
        """Test POST /goals/<uuid>/mark-achieved/ - Unauthenticated access fails"""
        url = reverse('apps_calculators_bmi:mark-goal-achieved', 
                     kwargs={'goal_id': self.goal.id})
        
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    
    
    

    def test_all_protected_endpoints_require_authentication(self):
        """Test that all protected endpoints require authentication"""
        protected_endpoints = [
            ('apps_calculators_bmi:calculation-list-create', {}),
            ('apps_calculators_bmi:calculation-detail', {'pk': self.calculation.id}),
            ('apps_calculators_bmi:goal-list-create', {}),
            ('apps_calculators_bmi:goal-detail', {'pk': self.goal.id}),
            ('apps_calculators_bmi:history', {}),
            ('apps_calculators_bmi:stats', {}),
            ('apps_calculators_bmi:set-active-calculation', {'calculation_id': self.calculation.id}),
            ('apps_calculators_bmi:mark-goal-achieved', {'goal_id': self.goal.id}),
        ]
        
        for endpoint_name, kwargs in protected_endpoints:
            with self.subTest(endpoint=endpoint_name):
                url = reverse(endpoint_name, kwargs=kwargs)
                response = self.client.get(url)
                self.assertEqual(
                    response.status_code, 
                    status.HTTP_401_UNAUTHORIZED,
                    f"Endpoint {endpoint_name} should require authentication"
                )

    def test_all_public_endpoints_allow_anonymous_access(self):
        """Test that public endpoints allow anonymous access"""
        public_endpoints = [
            ('apps_calculators_bmi:calculate', {}),
            ('apps_calculators_bmi:info', {}),
        ]
        
        for endpoint_name, kwargs in public_endpoints:
            with self.subTest(endpoint=endpoint_name):
                url = reverse(endpoint_name, kwargs=kwargs)
                if endpoint_name == 'apps_calculators_bmi:calculate':
                    
                    data = {'weight_kg': '70.00', 'height_cm': '175.00'}
                    response = self.client.post(url, data)
                else:
                    
                    response = self.client.get(url)
                
                self.assertNotEqual(
                    response.status_code, 
                    status.HTTP_401_UNAUTHORIZED,
                    f"Public endpoint {endpoint_name} should allow anonymous access"
                ) 