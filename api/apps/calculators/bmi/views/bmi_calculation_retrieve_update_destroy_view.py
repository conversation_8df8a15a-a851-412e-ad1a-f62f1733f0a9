"""
BMI Calculation Retrieve Update Destroy View

This module contains the view for retrieving, updating, and deleting BMI calculations,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions

from ..models import BMICalculation
from ..serializers import BMICalculationSerializer


class BMICalculationRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific BMI calculation.
    
    This view ensures that users can only access their own calculations
    and provides full CRUD operations for individual BMI records.
    """
    serializer_class = BMICalculationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return BMI calculations for the authenticated user only"""
        if not self.request.user.is_authenticated:
            return BMICalculation.objects.none()
        return BMICalculation.objects.filter(user=self.request.user)
    
    def get_serializer_context(self):
        """Add request to serializer context for user association"""
        return {'request': self.request}
