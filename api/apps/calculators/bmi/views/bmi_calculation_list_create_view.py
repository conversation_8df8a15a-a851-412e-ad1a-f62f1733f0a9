"""
BMI Calculation List Create View

This module contains the view for listing and creating BMI calculations,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response

from ..models import BMICalculation
from ..serializers import BMICalculationSerializer


class BMICalculationListCreateView(generics.ListCreateAPIView):
    """
    List BMI calculations or create new calculations.

    Uses DRF's get_permissions() hook for unambiguous read/write access distinction:
    - Safe methods (GET): Authenticated users can list their calculations
    - Write methods (POST): Authenticated users can create their calculations
    """

    serializer_class = BMICalculationSerializer

    def get_permissions(self):
        """
        Return permission classes based on HTTP method.

        Following Senior Developer best practices for unambiguous read/write distinction:
        - All methods require authentication for user-specific data
        - No additional permissions needed beyond authentication
        """
        return [permissions.IsAuthenticated()]

    def get_queryset(self):
        """Return BMI calculations for the authenticated user only"""
        return BMICalculation.objects.filter(user=self.request.user).order_by(
            "-created"
        )

    def perform_create(self, serializer):
        """Create BMI calculation and associate with current user"""
        
        calculation = serializer.save(user=self.request.user)

        
        if (
            not BMICalculation.objects.filter(user=self.request.user, is_active=True)
            .exclude(pk=calculation.pk)
            .exists()
        ):
            calculation.is_active = True
            calculation.save(update_fields=["is_active"])

        return calculation

    def get_serializer_context(self):
        """Add request to serializer context for user association"""
        return {"request": self.request}
