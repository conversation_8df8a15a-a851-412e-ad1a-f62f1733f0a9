"""
BMI Goal List Create View

This module contains the view for listing and creating BMI goals,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions
from django.utils.translation import gettext_lazy as _

from ..models import BMIGoal
from ..serializers import BMIGoalSerializer


class BMIGoalListCreateView(generics.ListCreateAPIView):
    """
    List user's BMI goals or create a new one.
    
    This view handles both listing existing goals and creating new ones
    for authenticated users. It ensures proper user isolation and authentication.
    """
    serializer_class = BMIGoalSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return BMI goals for the authenticated user only"""
        if not self.request.user.is_authenticated:
            return BMIGoal.objects.none()
        return BMIGoal.objects.filter(user=self.request.user)
    
    def get_serializer_context(self):
        """Add request to serializer context for user association"""
        return {'request': self.request}
    
    def perform_create(self, serializer):
        """
        Ensure the goal is created for the authenticated user.
        
        This method provides an additional layer of security by explicitly
        setting the user in the perform_create method.
        """
        if not self.request.user.is_authenticated:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied(_("Authentication is required to save goals."))
        
        serializer.save(user=self.request.user)
