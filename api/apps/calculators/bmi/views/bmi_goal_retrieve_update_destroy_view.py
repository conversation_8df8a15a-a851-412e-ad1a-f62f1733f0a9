"""
BMI Goal Retrieve Update Destroy View

This module contains the view for retrieving, updating, and deleting BMI goals,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions

from ..models import BMIGoal
from ..serializers import BMIGoalSerializer


class BMIGoalRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific BMI goal.
    
    This view ensures that users can only access their own goals
    and provides full CRUD operations for individual BMI goals.
    """
    serializer_class = BMIGoalSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return BMI goals for the authenticated user only"""
        if not self.request.user.is_authenticated:
            return BMIGoal.objects.none()
        return BMIGoal.objects.filter(user=self.request.user)
    
    def get_serializer_context(self):
        """Add request to serializer context for user association"""
        return {'request': self.request}
