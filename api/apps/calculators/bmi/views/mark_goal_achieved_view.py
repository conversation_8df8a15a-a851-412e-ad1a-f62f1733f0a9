"""
Mark Goal Achieved View

This module contains the function-based view for marking BMI goals as achieved,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import BMIGoal
from ..serializers import BMIGoalSerializer


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_goal_achieved(request, goal_id):
    """
    Mark a BMI goal as achieved.
    
    This endpoint allows users to mark their BMI goals as completed
    when they reach their target weight/BMI.
    """
    try:
        goal = BMIGoal.objects.get(
            id=goal_id,
            user=request.user
        )
        
        goal.is_achieved = True
        from django.utils import timezone
        goal.achieved_date = timezone.now().date()
        goal.save()
        
        serializer = BMIGoalSerializer(goal)
        return Response(
            {
                'message': _('BMI goal marked as achieved.'),
                'data': serializer.data
            },
            status=status.HTTP_200_OK
        )
        
    except BMIGoal.DoesNotExist:
        return Response(
            {'error': _('BMI goal not found.')},
            status=status.HTTP_404_NOT_FOUND
        )
