"""
BMI Info View

This module contains the function-based view for BMI calculator information,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from ..serializers import BMICalculatorInfoSerializer


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def bmi_info(request):
    """
    Get BMI calculator information and categories.
    
    This endpoint provides general information about BMI calculation,
    including category definitions, formula, and health recommendations.
    Can be accessed by anonymous users.
    """
    
    categories_data = [
        {
            'value': 'underweight',
            'label': _('Underweight'),
            'min_bmi': None,
            'max_bmi': Decimal('18.49'),
            'description': _('Below normal weight for height. May indicate malnutrition.'),
            'color': '#3B82F6'  
        },
        {
            'value': 'normal',
            'label': _('Normal Weight'),
            'min_bmi': Decimal('18.5'),
            'max_bmi': Decimal('24.99'),
            'description': _('Healthy weight range associated with longevity and lower disease risk.'),
            'color': '#10B981'  
        },
        {
            'value': 'overweight',
            'label': _('Overweight'),
            'min_bmi': Decimal('25.0'),
            'max_bmi': Decimal('29.99'),
            'description': _('Above normal weight. May increase risk of health problems.'),
            'color': '#F59E0B'  
        },
        {
            'value': 'obese_class_1',
            'label': _('Obesity Class I'),
            'min_bmi': Decimal('30.0'),
            'max_bmi': Decimal('34.99'),
            'description': _('Moderate obesity. Increased risk of health complications.'),
            'color': '#EF4444'  
        },
        {
            'value': 'obese_class_2',
            'label': _('Obesity Class II'),
            'min_bmi': Decimal('35.0'),
            'max_bmi': Decimal('39.99'),
            'description': _('Severe obesity. High risk of serious health problems.'),
            'color': '#DC2626'  
        },
        {
            'value': 'obese_class_3',
            'label': _('Obesity Class III'),
            'min_bmi': Decimal('40.0'),
            'max_bmi': None,
            'description': _('Very severe obesity. Very high risk of life-threatening health issues.'),
            'color': '#7F1D1D'  
        }
    ]
    
    
    formula = _("BMI = weight (kg) / (height (m))²")
    
    units = {
        'weight': {
            'metric': 'kg',
            'imperial': 'lbs'
        },
        'height': {
            'metric': 'cm',
            'imperial': 'inches'
        }
    }
    
    recommendations = {
        'underweight': _("Consider consulting a healthcare provider to discuss healthy weight gain strategies."),
        'normal': _("Maintain your current healthy lifestyle with balanced diet and regular exercise."),
        'overweight': _("Consider moderate weight loss through balanced diet and increased physical activity."),
        'obese_class_1': _("Consult a healthcare provider for a comprehensive weight management plan."),
        'obese_class_2': _("Seek medical supervision for weight loss and health risk management."),
        'obese_class_3': _("Immediate medical consultation recommended for comprehensive health assessment.")
    }
    
    info_data = {
        'categories': categories_data,
        'formula': formula,
        'units': units,
        'recommendations': recommendations
    }
    
    serializer = BMICalculatorInfoSerializer(info_data)
    return Response(serializer.data, status=status.HTTP_200_OK)
