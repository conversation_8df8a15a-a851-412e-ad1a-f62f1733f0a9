"""
BMI Stats View

This module contains the function-based view for BMI statistics,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.db.models import Avg, Count, Max, Min, Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from datetime import datetime, timedelta
from decimal import Decimal

from ..models import BMICalculation, BMIGoal, BMICategory
from ..serializers import BMIStatsSerializer


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def bmi_stats(request):
    """
    Get comprehensive BMI statistics for the authenticated user.
    
    This endpoint provides detailed analytics about the user's BMI history,
    trends, and goal progress for dashboard and tracking purposes.
    """
    user = request.user
    calculations = BMICalculation.objects.filter(user=user)
    goals = BMIGoal.objects.filter(user=user)
    
    
    total_calculations = calculations.count()
    
    
    current_month_start = timezone.now().replace(day=1)
    calculations_this_month = calculations.filter(
        created__gte=current_month_start
    ).count()
    
    
    current_calculation = calculations.filter(is_active=True).first()
    current_bmi = current_calculation.bmi_value if current_calculation else None
    current_category = current_calculation.category if current_calculation else None
    current_category_display = (
        current_calculation.get_category_display() 
        if current_calculation else None
    )
    
    
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_calculations = calculations.filter(created__gte=thirty_days_ago)
    
    
    avg_bmi_last_30_days = None
    min_bmi = None
    max_bmi = None
    
    if recent_calculations.exists():
        stats = recent_calculations.aggregate(
            avg_bmi=Avg('bmi_value'),
            min_bmi=Min('bmi_value'),
            max_bmi=Max('bmi_value')
        )
        avg_bmi_last_30_days = stats['avg_bmi']
        min_bmi = stats['min_bmi']
        max_bmi = stats['max_bmi']
    
    
    weight_trend = 'insufficient_data'
    bmi_trend = 'insufficient_data'
    
    if recent_calculations.count() >= 2:
        recent_list = list(recent_calculations.order_by('created'))
        first_weight = recent_list[0].weight_kg
        last_weight = recent_list[-1].weight_kg
        first_bmi = recent_list[0].bmi_value
        last_bmi = recent_list[-1].bmi_value
        
        weight_diff = last_weight - first_weight
        bmi_diff = last_bmi - first_bmi
        
        
        if abs(weight_diff) < Decimal('0.5'):
            weight_trend = 'stable'
        elif weight_diff > 0:
            weight_trend = 'increasing'
        else:
            weight_trend = 'decreasing'
        
        
        if abs(bmi_diff) < Decimal('0.1'):
            bmi_trend = 'stable'
        elif bmi_diff > 0:
            bmi_trend = 'increasing'
        else:
            bmi_trend = 'decreasing'
    
    
    has_active_goals = goals.filter(is_achieved=False).exists()
    active_goals_count = goals.filter(is_achieved=False).count()
    
    
    latest_calculation_date = None
    if calculations.exists():
        latest_calculation_date = calculations.first().created
    
    stats_data = {
        'current_bmi': current_bmi,
        'current_category': current_category,
        'current_category_display': current_category_display,
        'total_calculations': total_calculations,
        'calculations_this_month': calculations_this_month,
        'weight_trend': weight_trend,
        'bmi_trend': bmi_trend,
        'avg_bmi_last_30_days': avg_bmi_last_30_days,
        'min_bmi': min_bmi,
        'max_bmi': max_bmi,
        'latest_calculation_date': latest_calculation_date,
        'has_active_goals': has_active_goals,
        'active_goals_count': active_goals_count,
    }
    
    serializer = BMIStatsSerializer(stats_data)
    return Response(serializer.data, status=status.HTTP_200_OK)
