"""
Calculate BMI View

This module contains the function-based view for anonymous BMI calculation,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response

from ..models import BMICalculation
from ..serializers import BMICalculationCreateSerializer


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def calculate_bmi(request):
    """
    Calculate BMI without saving to database.
    
    This endpoint can be used by anonymous users to calculate BMI
    without requiring authentication. It's useful for the public
    BMI calculator functionality.
    """
    serializer = BMICalculationCreateSerializer(data=request.data)
    
    if serializer.is_valid():
        
        temp_instance = BMICalculation(
            weight_kg=serializer.validated_data['weight_kg'],
            height_cm=serializer.validated_data['height_cm']
        )
        
        
        return Response(
            serializer.to_representation(temp_instance),
            status=status.HTTP_200_OK
        )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
