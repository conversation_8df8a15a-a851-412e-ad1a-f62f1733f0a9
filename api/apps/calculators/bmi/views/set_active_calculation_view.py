"""
Set Active Calculation View

This module contains the function-based view for setting active BMI calculations,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import BMICalculation
from ..serializers import BMICalculationSerializer


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def set_active_calculation(request, calculation_id):
    """
    Set a specific BMI calculation as the active/current one.
    
    This endpoint allows users to mark a specific calculation as their
    current active BMI reading for tracking purposes.
    """
    try:
        calculation = BMICalculation.objects.get(
            id=calculation_id,
            user=request.user
        )
        
        
        BMICalculation.objects.filter(
            user=request.user,
            is_active=True
        ).update(is_active=False)
        
        
        calculation.is_active = True
        calculation.save()
        
        serializer = BMICalculationSerializer(calculation)
        return Response(
            {
                'message': _('BMI calculation set as active.'),
                'data': serializer.data
            },
            status=status.HTTP_200_OK
        )
        
    except BMICalculation.DoesNotExist:
        return Response(
            {'error': _('BMI calculation not found.')},
            status=status.HTTP_404_NOT_FOUND
        )
