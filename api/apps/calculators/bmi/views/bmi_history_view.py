"""
BMI History View

This module contains the view for BMI calculation history,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions

from ..models import BMICalculation, BMICategory
from ..serializers import BMIHistorySerializer


class BMIHistoryView(generics.ListAPIView):
    """
    Get user's BMI calculation history with optional filtering.
    
    This view provides historical BMI data with support for date range filtering,
    category filtering, and result limiting for analytics and tracking purposes.
    """
    serializer_class = BMIHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return BMI history for the authenticated user with optional filtering"""
        if not self.request.user.is_authenticated:
            return BMICalculation.objects.none()
            
        queryset = BMICalculation.objects.filter(user=self.request.user)
        
        # Apply date filtering
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            try:
                from datetime import datetime
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(created__date__gte=start_date)
            except ValueError:
                pass
        
        if end_date:
            try:
                from datetime import datetime
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(created__date__lte=end_date)
            except ValueError:
                pass
        
        # Apply category filtering
        category = self.request.query_params.get('category')
        if category and category in [choice[0] for choice in BMICategory.choices]:
            queryset = queryset.filter(category=category)
        
        # Apply ordering BEFORE slicing
        queryset = queryset.order_by('-created')
        
        # Apply limit filtering (must be after ordering)
        limit = self.request.query_params.get('limit')
        if limit:
            try:
                limit = int(limit)
                if limit > 0:
                    queryset = queryset[:limit]
            except (ValueError, TypeError):
                pass
        
        return queryset
