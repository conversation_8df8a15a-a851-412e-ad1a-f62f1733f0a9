"""
BMI Views Package

This package contains all BMI-related views, organized by functionality
following Django REST Framework best practices.
"""


from .bmi_calculation_list_create_view import BMICalculationListCreateView
from .bmi_calculation_retrieve_update_destroy_view import BMICalculationRetrieveUpdateDestroyView
from .bmi_history_view import B<PERSON><PERSON><PERSON>oryView
from .calculate_bmi_view import calculate_bmi
from .set_active_calculation_view import set_active_calculation
from .bmi_goal_list_create_view import BMIGoalListCreateView
from .bmi_goal_retrieve_update_destroy_view import BMIGoalRetrieveUpdateDestroyView
from .mark_goal_achieved_view import mark_goal_achieved
from .bmi_stats_view import bmi_stats
from .bmi_info_view import bmi_info

__all__ = [
    'BMICalculationListCreateView',
    'BMICalculationRetrieveUpdateDestroyView',
    'BMIHistoryView',
    'calculate_bmi',
    'set_active_calculation',
    'BMIGoalListCreateView',
    'BMIGoalRetrieveUpdateDestroyView',
    'mark_goal_achieved',
    'bmi_stats',
    'bmi_info',
]
