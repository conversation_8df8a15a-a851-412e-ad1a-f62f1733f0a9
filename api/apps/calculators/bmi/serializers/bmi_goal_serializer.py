"""
BMI Goal Serializers

This module contains serializers for BMI goal operations,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from datetime import date

from ..models import BMIGoal


class BMIGoalSerializer(serializers.ModelSerializer):
    """
    Serializer for BMI goals with auto-calculation of target BMI.
    
    This serializer handles creating and updating BMI goals for authenticated users.
    It automatically calculates target BMI and category based on target weight and height.
    """
    
    target_bmi = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        read_only=True
    )
    target_category = serializers.CharField(read_only=True)
    target_category_display = serializers.CharField(
        source='get_target_category_display', 
        read_only=True
    )
    
    class Meta:
        model = BMIGoal
        fields = [
            'id',
            '_id',
            'target_weight_kg',
            'target_bmi',
            'target_category',
            'target_category_display',
            'current_height_cm',
            'target_date',
            'is_achieved',
            'achieved_date',
            'notes',
            'created',
            'updated'
        ]
        read_only_fields = [
            'id',
            '_id',
            'target_bmi',
            'target_category',
            'created',
            'updated'
        ]
    
    def validate_target_weight_kg(self, value):
        """Validate target weight is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Target weight must be positive."))
        if value > Decimal('999.99'):
            raise serializers.ValidationError(_("Target weight seems unreasonably high."))
        if value < Decimal('1.00'):
            raise serializers.ValidationError(_("Target weight seems unreasonably low."))
        return value
    
    def validate_current_height_cm(self, value):
        """Validate height is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Height must be positive."))
        if value > Decimal('300.00'):
            raise serializers.ValidationError(_("Height seems unreasonably high."))
        if value < Decimal('50.00'):
            raise serializers.ValidationError(_("Height seems unreasonably low."))
        return value
    
    def validate_target_date(self, value):
        """Validate target date is not in the past"""
        if value < date.today():
            raise serializers.ValidationError(_("Target date cannot be in the past."))
        return value
    
    def create(self, validated_data):
        """
        Create BMI goal for the authenticated user.
        
        This method ensures that the goal is associated with the current user
        and handles authentication properly.
        """
        request = self.context.get('request')
        if not request:
            raise serializers.ValidationError(
                _("Request context is required for creating goals.")
            )
        
        user = request.user
        if not user.is_authenticated:
            raise serializers.ValidationError(
                _("Authentication is required to save goals.")
            )
        
        validated_data['user'] = user
        return super().create(validated_data) 