"""
BMI History Serializer

This module contains the serializer for BMI calculation history,
following Django REST Framework best practices.
"""

from rest_framework import serializers

from ..models import BMICalculation


class BMIHistorySerializer(serializers.ModelSerializer):
    """
    Read-only serializer for BMI calculation history.
    
    This serializer provides a simplified view of BMI calculations
    for historical tracking and analytics.
    """
    
    category_display = serializers.CharField(
        source='get_category_display', 
        read_only=True
    )
    height_m = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        read_only=True
    )
    
    class Meta:
        model = BMICalculation
        fields = [
            'id',
            '_id',
            'weight_kg',
            'height_cm',
            'height_m',
            'bmi_value',
            'category',
            'category_display',
            'notes',
            'is_active',
            'created'
        ]
        read_only_fields = [
            'id',
            '_id',
            'weight_kg',
            'height_cm',
            'height_m',
            'bmi_value',
            'category',
            'category_display',
            'notes',
            'is_active',
            'created'
        ]
