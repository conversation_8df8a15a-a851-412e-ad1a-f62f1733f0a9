"""
BMI Category Serializer

This module contains the serializer for BMI category information,
following Django REST Framework best practices.
"""

from rest_framework import serializers


class BMICategorySerializer(serializers.Serializer):
    """
    Serializer for BMI category information.
    
    This serializer provides detailed information about BMI categories,
    including ranges, descriptions, and UI styling information.
    """
    
    value = serializers.CharField()
    label = serializers.CharField()
    min_bmi = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        allow_null=True
    )
    max_bmi = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        allow_null=True
    )
    description = serializers.CharField()
    color = serializers.CharField()  
