"""
BMI Statistics Serializer

This module contains the serializer for BMI statistics and analytics,
following Django REST Framework best practices.
"""

from rest_framework import serializers


class BMIStatsSerializer(serializers.Serializer):
    """
    Serializer for BMI statistics and analytics.
    
    This serializer provides comprehensive statistics about a user's BMI history,
    trends, and goal progress.
    """
    
    current_bmi = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        allow_null=True
    )
    current_category = serializers.CharField(allow_null=True)
    current_category_display = serializers.CharField(allow_null=True)
    total_calculations = serializers.IntegerField()
    calculations_this_month = serializers.IntegerField()
    weight_trend = serializers.CharField()  
    bmi_trend = serializers.CharField()
    avg_bmi_last_30_days = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        allow_null=True
    )
    min_bmi = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        allow_null=True
    )
    max_bmi = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        allow_null=True
    )
    latest_calculation_date = serializers.DateTimeField(allow_null=True)
    has_active_goals = serializers.Boolean<PERSON>ield()
    active_goals_count = serializers.IntegerField()
