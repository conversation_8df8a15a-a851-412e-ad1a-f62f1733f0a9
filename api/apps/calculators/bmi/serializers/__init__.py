"""
BMI Serializers Package

This package contains all BMI-related serializers, organized by functionality
following Django REST Framework best practices.
"""


from .bmi_calculation_serializer import BMICalculationSerializer
from .bmi_calculation_create_serializer import BMICalculationCreateSerializer
from .bmi_history_serializer import BMIHistorySerializer
from .bmi_goal_serializer import BMIGoalSerializer
from .bmi_stats_serializer import BMIStatsSerializer
from .bmi_category_serializer import BMICategorySerializer
from .bmi_calculator_info_serializer import BMICalculatorInfoSerializer

__all__ = [
    'BMICalculationSerializer',
    'BMICalculationCreateSerializer',
    'BMIHistorySerializer',
    'BMIGoalSerializer',
    'BMIStatsSerializer',
    'BMICategorySerializer',
    'BMICalculatorInfoSerializer',
]
