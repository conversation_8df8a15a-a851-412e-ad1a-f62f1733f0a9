"""
BMI Calculator Info Serializer

This module contains the serializer for BMI calculator information,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from .bmi_category_serializer import BMICategorySerializer


class BMICalculatorInfoSerializer(serializers.Serializer):
    """
    Serializer for BMI calculator information and categories.
    
    This serializer provides general information about BMI calculation,
    including formula, units, and health recommendations.
    """
    
    categories = BMICategorySerializer(many=True)
    formula = serializers.CharField()
    units = serializers.DictField()
    recommendations = serializers.DictField()
