"""
BMI Calculation Serializer

This module contains the main BMI calculation serializer for authenticated users,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from ..models import BMICalculation


class BMICalculationSerializer(serializers.ModelSerializer):
    """
    Serializer for BMI calculations with auto-calculation and validation.
    
    This serializer handles creating and updating BMI calculations for authenticated users.
    It automatically calculates BMI value and category based on weight and height.
    """
    
    bmi_value = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        read_only=True
    )
    category = serializers.CharField(read_only=True)
    category_display = serializers.CharField(
        source='get_category_display', 
        read_only=True
    )
    bmi_status_message = serializers.CharField(read_only=True)
    height_m = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        read_only=True
    )
    
    class Meta:
        model = BMICalculation
        fields = [
            'id',
            '_id',
            'weight_kg',
            'height_cm',
            'height_m',
            'bmi_value',
            'category',
            'category_display',
            'bmi_status_message',
            'notes',
            'is_active',
            'created',
            'updated'
        ]
        read_only_fields = [
            'id',
            '_id',
            'bmi_value',
            'category',
            'created',
            'updated'
        ]
    
    def validate_weight_kg(self, value):
        """Validate weight is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Weight must be positive."))
        if value > Decimal('999.99'):
            raise serializers.ValidationError(_("Weight seems unreasonably high."))
        if value < Decimal('1.00'):
            raise serializers.ValidationError(_("Weight seems unreasonably low."))
        return value
    
    def validate_height_cm(self, value):
        """Validate height is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Height must be positive."))
        if value > Decimal('300.00'):
            raise serializers.ValidationError(_("Height seems unreasonably high."))
        if value < Decimal('50.00'):
            raise serializers.ValidationError(_("Height seems unreasonably low."))
        return value
    
    def create(self, validated_data):
        """
        Create BMI calculation for the authenticated user.
        
        This method ensures that the calculation is associated with the current user
        and handles authentication properly.
        """
        request = self.context.get('request')
        if not request:
            raise serializers.ValidationError(
                _("Request context is required for creating calculations.")
            )
        
        user = request.user
        if not user.is_authenticated:
            raise serializers.ValidationError(
                _("Authentication is required to save calculations.")
            )
        
        validated_data['user'] = user
        return super().create(validated_data) 