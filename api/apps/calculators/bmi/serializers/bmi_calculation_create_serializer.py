"""
BMI Calculation Create Serializer

This module contains the serializer for anonymous BMI calculations,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from ..models import BMICalculation, BMICategory


class BMICalculationCreateSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for creating BMI calculations without saving to database.
    
    This is used for the anonymous BMI calculation endpoint that doesn't require authentication.
    It calculates and returns BMI data without persisting it to the database.
    """
    
    bmi_value = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        read_only=True
    )
    category = serializers.CharField(read_only=True)
    category_display = serializers.CharField(read_only=True)
    bmi_status_message = serializers.CharField(read_only=True)
    
    class Meta:
        model = BMICalculation
        fields = [
            'weight_kg',
            'height_cm',
            'bmi_value',
            'category',
            'category_display',
            'bmi_status_message'
        ]
    
    def validate_weight_kg(self, value):
        """Validate weight is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Weight must be positive."))
        if value > Decimal('999.99'):
            raise serializers.ValidationError(_("Weight seems unreasonably high."))
        if value < Decimal('1.00'):
            raise serializers.ValidationError(_("Weight seems unreasonably low."))
        return value
    
    def validate_height_cm(self, value):
        """Validate height is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Height must be positive."))
        if value > Decimal('300.00'):
            raise serializers.ValidationError(_("Height seems unreasonably high."))
        if value < Decimal('50.00'):
            raise serializers.ValidationError(_("Height seems unreasonably low."))
        return value
    
    def to_representation(self, instance):
        """Add calculated BMI data to the representation"""
        data = super().to_representation(instance)
        
        
        bmi_value = BMICalculation.calculate_bmi(
            instance.weight_kg, 
            instance.height_cm
        )
        category = BMICalculation.get_bmi_category(bmi_value)
        
        
        temp_instance = BMICalculation(
            weight_kg=instance.weight_kg,
            height_cm=instance.height_cm,
            bmi_value=bmi_value,
            category=category
        )
        
        data.update({
            'bmi_value': bmi_value,
            'category': category,
            'category_display': dict(BMICategory.choices)[category],
            'bmi_status_message': temp_instance.bmi_status_message
        })
        
        return data
