"""
BMI Calculator URL Configuration

This module defines URL patterns for BMI calculator endpoints,
following Django REST Framework best practices and proper organization.
"""

from django.urls import path
from . import views

app_name = 'apps_calculators_bmi'

urlpatterns = [
    
    path(
        'calculations/',
        views.BMICalculationListCreateView.as_view(),
        name='calculation-list-create'
    ),
    path(
        'calculations/<uuid:pk>/',
        views.BMICalculationRetrieveUpdateDestroyView.as_view(),
        name='calculation-detail'
    ),
    
    
    path(
        'goals/',
        views.BMIGoalListCreateView.as_view(),
        name='goal-list-create'
    ),
    path(
        'goals/<uuid:pk>/',
        views.BMIGoalRetrieveUpdateDestroyView.as_view(),
        name='goal-detail'
    ),
    
    
    path(
        'history/',
        views.BMIHistoryView.as_view(),
        name='history'
    ),
    path(
        'stats/',
        views.bmi_stats,
        name='stats'
    ),
    
    
    path(
        'calculate/',
        views.calculate_bmi,
        name='calculate'
    ),
    path(
        'info/',
        views.bmi_info,
        name='info'
    ),
    
    
    path(
        'calculations/<uuid:calculation_id>/set-active/',
        views.set_active_calculation,
        name='set-active-calculation'
    ),
    path(
        'goals/<uuid:goal_id>/mark-achieved/',
        views.mark_goal_achieved,
        name='mark-goal-achieved'
    ),
] 