from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import BMICalculation, BMIGoal


@admin.register(BMICalculation)
class BMICalculationAdmin(admin.ModelAdmin):
    """
    Admin interface for BMI calculations with comprehensive filtering and display.
    """

    list_display = [
        "user_display",
        "weight_kg",
        "height_cm",
        "bmi_value_display",
        "category_display",
        "is_active",
        "created",
    ]

    list_filter = [
        "category",
        "is_active",
        "created",
        "updated",
    ]

    search_fields = [
        "user__email",
        "user__fullname",
        "user__username",
    ]

    readonly_fields = [
        "id",
        "_id",
        "bmi_value",
        "category",
        "height_m",
        "bmi_status_message",
        "created",
        "updated",
    ]

    fieldsets = [
        (_("User Information"), {"fields": ("user",)}),
        (
            _("Measurements"),
            {
                "fields": (
                    "weight_kg",
                    "height_cm",
                    "height_m",
                )
            },
        ),
        (
            _("BMI Results"),
            {
                "fields": (
                    "bmi_value",
                    "category",
                    "bmi_status_message",
                )
            },
        ),
        (
            _("Additional Information"),
            {
                "fields": (
                    "notes",
                    "is_active",
                )
            },
        ),
        (
            _("System Information"),
            {
                "fields": (
                    "id",
                    "_id",
                    "created",
                    "updated",
                ),
                "classes": ("collapse",),
            },
        ),
    ]

    ordering = ["-created"]

    date_hierarchy = "created"

    actions = ["mark_as_active", "mark_as_inactive"]

    def user_display(self, obj):
        """Display user information"""
        return format_html(
            "<strong>{}</strong><br/><small>{}</small>",
            obj.user.full_name,
            obj.user.email,
        )

    user_display.short_description = _("User")

    def bmi_value_display(self, obj):
        """Display BMI value with color coding"""
        colors = {
            "underweight": "#3B82F6",
            "normal": "#10B981",
            "overweight": "#F59E0B",
            "obese_1": "#EF4444",
            "obese_2": "#DC2626",
            "obese_3": "#991B1B",
        }

        color = colors.get(obj.category, "#6B7280")

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.bmi_value,
        )

    bmi_value_display.short_description = _("BMI Value")

    def category_display(self, obj):
        """Display category with color coding"""
        colors = {
            "underweight": "#3B82F6",
            "normal": "#10B981",
            "overweight": "#F59E0B",
            "obese_1": "#EF4444",
            "obese_2": "#DC2626",
            "obese_3": "#991B1B",
        }

        color = colors.get(obj.category, "#6B7280")

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_category_display(),
        )

    category_display.short_description = _("Category")

    def mark_as_active(self, request, queryset):
        """Mark selected calculations as active"""
        for calculation in queryset:

            BMICalculation.objects.filter(user=calculation.user, is_active=True).update(
                is_active=False
            )

            calculation.is_active = True
            calculation.save()

        self.message_user(
            request, _("Selected calculations have been marked as active.")
        )

    mark_as_active.short_description = _("Mark as active")

    def mark_as_inactive(self, request, queryset):
        """Mark selected calculations as inactive"""
        queryset.update(is_active=False)
        self.message_user(
            request, _("Selected calculations have been marked as inactive.")
        )

    mark_as_inactive.short_description = _("Mark as inactive")

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related("user")


@admin.register(BMIGoal)
class BMIGoalAdmin(admin.ModelAdmin):
    """
    Admin interface for BMI goals with comprehensive filtering and display.
    """

    list_display = [
        "user_display",
        "target_weight_kg",
        "target_bmi_display",
        "target_category_display",
        "target_date",
        "is_achieved",
        "achieved_date",
        "created",
    ]

    list_filter = [
        "target_category",
        "is_achieved",
        "target_date",
        "achieved_date",
        "created",
    ]

    search_fields = [
        "user__email",
        "user__fullname",
        "user__username",
    ]

    readonly_fields = [
        "id",
        "_id",
        "target_bmi",
        "target_category",
        "created",
        "updated",
    ]

    fieldsets = [
        (_("User Information"), {"fields": ("user",)}),
        (
            _("Goal Details"),
            {
                "fields": (
                    "target_weight_kg",
                    "current_height_cm",
                    "target_bmi",
                    "target_category",
                )
            },
        ),
        (
            _("Timeline"),
            {
                "fields": (
                    "target_date",
                    "is_achieved",
                    "achieved_date",
                )
            },
        ),
        (_("Additional Information"), {"fields": ("notes",)}),
        (
            _("System Information"),
            {
                "fields": (
                    "id",
                    "_id",
                    "created",
                    "updated",
                ),
                "classes": ("collapse",),
            },
        ),
    ]

    ordering = ["-created"]

    date_hierarchy = "created"

    actions = ["mark_as_achieved", "mark_as_not_achieved"]

    def user_display(self, obj):
        """Display user information"""
        return format_html(
            "<strong>{}</strong><br/><small>{}</small>",
            obj.user.full_name,
            obj.user.email,
        )

    user_display.short_description = _("User")

    def target_bmi_display(self, obj):
        """Display target BMI with color coding"""
        colors = {
            "underweight": "#3B82F6",
            "normal": "#10B981",
            "overweight": "#F59E0B",
            "obese_1": "#EF4444",
            "obese_2": "#DC2626",
            "obese_3": "#991B1B",
        }

        color = colors.get(obj.target_category, "#6B7280")

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.target_bmi,
        )

    target_bmi_display.short_description = _("Target BMI")

    def target_category_display(self, obj):
        """Display target category with color coding"""
        colors = {
            "underweight": "#3B82F6",
            "normal": "#10B981",
            "overweight": "#F59E0B",
            "obese_1": "#EF4444",
            "obese_2": "#DC2626",
            "obese_3": "#991B1B",
        }

        color = colors.get(obj.target_category, "#6B7280")

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_target_category_display(),
        )

    target_category_display.short_description = _("Target Category")

    def mark_as_achieved(self, request, queryset):
        """Mark selected goals as achieved"""
        from django.utils import timezone

        queryset.update(is_achieved=True, achieved_date=timezone.now().date())

        self.message_user(request, _("Selected goals have been marked as achieved."))

    mark_as_achieved.short_description = _("Mark as achieved")

    def mark_as_not_achieved(self, request, queryset):
        """Mark selected goals as not achieved"""
        queryset.update(is_achieved=False, achieved_date=None)

        self.message_user(
            request, _("Selected goals have been marked as not achieved.")
        )

    mark_as_not_achieved.short_description = _("Mark as not achieved")

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related("user")
