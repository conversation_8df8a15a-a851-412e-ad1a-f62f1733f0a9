"""
Calorie Recommendation Serializer

Serializer for calorie recommendations based on user profile.
"""

from rest_framework import serializers


class CalorieRecommendationSerializer(serializers.Serializer):
    """
    Serializer for calorie recommendations based on user profile.
    """
    
    recommended_calories = serializers.IntegerField()
    calorie_range_min = serializers.IntegerField()
    calorie_range_max = serializers.IntegerField()
    recommended_goal_type = serializers.CharField()
    recommended_activity_level = serializers.CharField()
    
    recommended_protein_grams = serializers.IntegerField()
    recommended_carb_grams = serializers.IntegerField()
    recommended_fat_grams = serializers.IntegerField()
    
    recommendation_reason = serializers.CharField()
    tips = serializers.ListField(child=serializers.CharField()) 