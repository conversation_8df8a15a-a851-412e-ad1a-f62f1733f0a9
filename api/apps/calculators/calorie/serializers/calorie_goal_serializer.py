"""
Calorie Goal Serializer

Serializer for calorie goals with progress tracking.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from ..models import CalorieGoalModel, CalorieGoalType


class CalorieGoalSerializer(serializers.ModelSerializer):
    """
    Serializer for calorie goals with progress tracking.
    """
    
    weight_difference = serializers.FloatField(read_only=True)
    days_to_goal = serializers.IntegerField(read_only=True)
    progress_percentage = serializers.FloatField(read_only=True)
    
    goal_type_display = serializers.CharField(source='get_goal_type_display', read_only=True)
    
    class Meta:
        model = CalorieGoalModel
        fields = [
            'id',
            'goal_type',
            'goal_type_display',
            'target_calories',
            'target_weight',
            'current_weight',
            'target_date',
            'weekly_weight_loss_rate',
            'goal_description',
            'weight_difference',
            'days_to_goal',
            'progress_percentage',
            'is_achieved',
            'achieved_date',
            'notes',
            'created',
            'updated',
        ]
        read_only_fields = ['id', 'created', 'updated']
    
    def validate_target_date(self, value):
        """Validate target date is in the future"""
        if value <= timezone.now().date():
            raise serializers.ValidationError(
                _("Target date must be in the future.")
            )
        return value
    
    def validate(self, attrs):
        """Validate goal consistency"""
        target_weight = attrs.get('target_weight')
        current_weight = attrs.get('current_weight')
        goal_type = attrs.get('goal_type')
        
        if target_weight and current_weight:
            weight_diff = float(target_weight) - float(current_weight)
            
            if goal_type in [CalorieGoalType.WEIGHT_LOSS, CalorieGoalType.CUTTING] and weight_diff >= 0:
                raise serializers.ValidationError(
                    _("For weight loss goals, target weight must be less than current weight.")
                )
            elif goal_type in [CalorieGoalType.WEIGHT_GAIN, CalorieGoalType.BULKING] and weight_diff <= 0:
                raise serializers.ValidationError(
                    _("For weight gain goals, target weight must be greater than current weight.")
                )
        
        return attrs
    
    def to_representation(self, instance):
        """Add additional calculated fields to the representation"""
        data = super().to_representation(instance)
        
        data['target_weight_formatted'] = f"{float(instance.target_weight):.1f} kg"
        data['current_weight_formatted'] = f"{float(instance.current_weight):.1f} kg"
        data['target_calories_formatted'] = f"{instance.target_calories:,} kcal/day"
        
        if instance.is_achieved:
            data['status'] = _("Achieved")
            data['status_color'] = "success"
        elif instance.days_to_goal <= 0:
            data['status'] = _("Overdue")
            data['status_color'] = "danger"
        elif instance.days_to_goal <= 7:
            data['status'] = _("Due Soon")
            data['status_color'] = "warning"
        else:
            data['status'] = _("In Progress")
            data['status_color'] = "primary"
        
        return data 