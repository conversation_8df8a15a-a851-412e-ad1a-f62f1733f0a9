"""
Calorie Calculation Create Serializer

Serializer for creating calorie calculations with validation.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import CalorieCalculationModel


class CalorieCalculationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating calorie calculations with validation.
    """
    
    class Meta:
        model = CalorieCalculationModel
        fields = [
            'age',
            'gender',
            'weight',
            'height',
            'activity_level',
            'goal_type',
            'notes',
        ]
    
    def validate(self, attrs):
        """Validate the combination of attributes"""
        try:
            bmr = CalorieCalculationModel.calculate_bmr(
                float(attrs['weight']),
                attrs['height'],
                attrs['age'],
                attrs['gender']
            )
            if bmr < 800 or bmr > 4000:
                raise serializers.ValidationError(
                    _("Calculated BMR is outside normal range. Please check your inputs.")
                )
        except Exception as e:
            raise serializers.ValidationError(
                _("Error calculating BMR: {error}").format(error=str(e))
            )
        
        return attrs
    
    def create(self, validated_data):
        """Create calorie calculation with user from request"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data) 