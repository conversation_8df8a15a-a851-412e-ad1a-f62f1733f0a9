"""
Calorie Stats Serializer

Serializer for calorie calculator statistics.
"""

from rest_framework import serializers

from .calorie_calculation_serializer import CalorieCalculationSerializer
from .calorie_goal_serializer import CalorieGoalSerializer


class CalorieStatsSerializer(serializers.Serializer):
    """
    Serializer for calorie calculator statistics.
    """
    
    total_calculations = serializers.IntegerField()
    active_calculations = serializers.IntegerField()
    total_goals = serializers.IntegerField()
    active_goals = serializers.IntegerField()
    achieved_goals = serializers.IntegerField()
    average_target_calories = serializers.IntegerField()
    most_common_goal_type = serializers.CharField()
    most_common_activity_level = serializers.CharField()
    
    recent_calculations = CalorieCalculationSerializer(many=True, read_only=True)
    recent_goals = CalorieGoalSerializer(many=True, read_only=True) 