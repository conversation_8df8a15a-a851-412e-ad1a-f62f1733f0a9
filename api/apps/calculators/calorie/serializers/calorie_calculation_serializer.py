"""
Calorie Calculation Serializer

Serializer for calorie calculations with comprehensive validation and formatting.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import CalorieCalculationModel, CalorieGoalType


class CalorieCalculationSerializer(serializers.ModelSerializer):
    """
    Serializer for calorie calculations with comprehensive validation and formatting.
    """
    
    bmr = serializers.IntegerField(read_only=True)
    tdee = serializers.IntegerField(read_only=True)
    target_calories = serializers.IntegerField(read_only=True)
    calorie_adjustment = serializers.IntegerField(read_only=True)
    activity_multiplier = serializers.DecimalField(max_digits=3, decimal_places=2, read_only=True)
    
    daily_protein_grams = serializers.IntegerField(read_only=True)
    daily_fat_grams = serializers.IntegerField(read_only=True)
    daily_carb_grams = serializers.IntegerField(read_only=True)
    macros_dict = serializers.DictField(read_only=True)
    
    activity_level_display = serializers.CharField(source='get_activity_level_display', read_only=True)
    goal_type_display = serializers.CharField(source='get_goal_type_display', read_only=True)
    
    class Meta:
        model = CalorieCalculationModel
        fields = [
            'id',
            'age',
            'gender',
            'weight',
            'height',
            'activity_level',
            'activity_level_display',
            'goal_type',
            'goal_type_display',
            'bmr',
            'tdee',
            'target_calories',
            'calorie_adjustment',
            'activity_multiplier',
            'daily_protein_grams',
            'daily_fat_grams',
            'daily_carb_grams',
            'macros_dict',
            'notes',
            'is_active',
            'created',
            'updated',
        ]
        read_only_fields = ['id', 'created', 'updated']
    
    def validate_weight(self, value):
        """Validate weight is within reasonable range"""
        if value < 20 or value > 500:
            raise serializers.ValidationError(
                _("Weight must be between 20 and 500 kg.")
            )
        return value
    
    def validate_height(self, value):
        """Validate height is within reasonable range"""
        if value < 50 or value > 300:
            raise serializers.ValidationError(
                _("Height must be between 50 and 300 cm.")
            )
        return value
    
    def validate_age(self, value):
        """Validate age is within reasonable range"""
        if value < 1 or value > 120:
            raise serializers.ValidationError(
                _("Age must be between 1 and 120 years.")
            )
        return value
    
    def to_representation(self, instance):
        """Add additional calculated fields to the representation"""
        data = super().to_representation(instance)
        
        data['weight_formatted'] = f"{float(instance.weight):.1f} kg"
        data['height_formatted'] = f"{instance.height} cm"
        data['target_calories_formatted'] = f"{instance.target_calories:,} kcal/day"
        data['bmr_formatted'] = f"{instance.bmr:,} kcal/day"
        data['tdee_formatted'] = f"{instance.tdee:,} kcal/day"
        
        if instance.goal_type == CalorieGoalType.WEIGHT_LOSS:
            data['recommendation'] = _("Focus on creating a sustainable calorie deficit through diet and exercise.")
        elif instance.goal_type == CalorieGoalType.WEIGHT_GAIN:
            data['recommendation'] = _("Increase calorie intake with nutrient-dense foods and strength training.")
        elif instance.goal_type == CalorieGoalType.MUSCLE_GAIN:
            data['recommendation'] = _("Combine moderate calorie surplus with high protein intake and resistance training.")
        else:
            data['recommendation'] = _("Maintain current calorie intake for weight stability.")
        
        return data 