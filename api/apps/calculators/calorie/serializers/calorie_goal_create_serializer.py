"""
Calorie Goal Create Serializer

Serializer for creating calorie goals with validation.
"""

from rest_framework import serializers

from ..models import CalorieGoalModel


class CalorieGoalCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating calorie goals with validation.
    """
    
    class Meta:
        model = CalorieGoalModel
        fields = [
            'goal_type',
            'target_calories',
            'target_weight',
            'current_weight',
            'target_date',
            'weekly_weight_loss_rate',
            'goal_description',
            'notes',
        ]
    
    def create(self, validated_data):
        """Create calorie goal with user from request"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data) 