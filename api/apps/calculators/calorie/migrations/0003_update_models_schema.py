# Generated by Django 4.2.23 on 2025-06-19 10:15

from django.db import migrations, models
import django.core.validators
from decimal import Decimal
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("apps_calculators_calorie", "0002_add_current_weight_default"),
    ]

    operations = [
        # CalorieCalculationModel updates
        
        # Remove old fields that are no longer needed
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='height_cm',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='weight_kg',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='formula',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='body_fat_percentage',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='bmr_calories',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='tdee_calories',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='maintenance_calories',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='cutting_calories',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='aggressive_cutting_calories',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='bulking_calories',
        ),
        migrations.RemoveField(
            model_name='caloriecalculationmodel',
            name='lean_bulking_calories',
        ),
        
        # Add new fields to CalorieCalculationModel
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='weight',
            field=models.DecimalField(
                max_digits=5,
                decimal_places=1,
                validators=[
                    django.core.validators.MinValueValidator(Decimal('20.0')),
                    django.core.validators.MaxValueValidator(Decimal('500.0')),
                ],
                default=Decimal('70.0'),
                verbose_name='Weight (kg)',
                help_text='Weight in kilograms (20-500 kg)',
            ),
        ),
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='height',
            field=models.PositiveIntegerField(
                validators=[
                    django.core.validators.MinValueValidator(50),
                    django.core.validators.MaxValueValidator(300),
                ],
                default=170,
                verbose_name='Height (cm)',
                help_text='Height in centimeters (50-300 cm)',
            ),
        ),
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='goal_type',
            field=models.CharField(
                max_length=20,
                choices=[
                    ('weight_loss', 'Weight Loss'),
                    ('weight_maintenance', 'Weight Maintenance'),
                    ('weight_gain', 'Weight Gain'),
                    ('muscle_gain', 'Muscle Gain'),
                    ('cutting', 'Cutting (Fat Loss)'),
                    ('bulking', 'Bulking (Muscle Gain)'),
                ],
                default='weight_maintenance',
                verbose_name='Goal Type',
            ),
        ),
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='bmr',
            field=models.PositiveIntegerField(
                blank=True,
                null=True,
                verbose_name='BMR (kcal/day)',
                help_text='Basal Metabolic Rate',
            ),
        ),
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='tdee',
            field=models.PositiveIntegerField(
                blank=True,
                null=True,
                verbose_name='TDEE (kcal/day)',
                help_text='Total Daily Energy Expenditure',
            ),
        ),
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='target_calories',
            field=models.PositiveIntegerField(
                blank=True,
                null=True,
                verbose_name='Target Calories (kcal/day)',
                help_text='Recommended daily calories for goal',
            ),
        ),
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='calorie_adjustment',
            field=models.IntegerField(
                blank=True,
                null=True,
                default=0,
                verbose_name='Calorie Adjustment',
                help_text='Calories added/subtracted from TDEE for goal',
            ),
        ),
        migrations.AddField(
            model_name='caloriecalculationmodel',
            name='activity_multiplier',
            field=models.DecimalField(
                max_digits=3,
                decimal_places=2,
                blank=True,
                null=True,
                verbose_name='Activity Multiplier',
                help_text='Multiplier applied to BMR for activity level',
            ),
        ),
        
        # Update age field to add default and validators
        migrations.AlterField(
            model_name='caloriecalculationmodel',
            name='age',
            field=models.PositiveIntegerField(
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(120),
                ],
                default=30,
                verbose_name='Age',
                help_text='Age in years (1-120)',
            ),
        ),
        
        # Update gender field to add default
        migrations.AlterField(
            model_name='caloriecalculationmodel',
            name='gender',
            field=models.CharField(
                max_length=10,
                choices=[
                    ('male', 'Male'),
                    ('female', 'Female'),
                ],
                default='male',
                verbose_name='Gender',
            ),
        ),
        
        # CalorieGoalModel updates
        
        # Remove old fields
        migrations.RemoveField(
            model_name='caloriegoalmodel',
            name='target_weight_kg',
        ),
        migrations.RemoveField(
            model_name='caloriegoalmodel',
            name='target_body_fat_percentage',
        ),
        migrations.RemoveField(
            model_name='caloriegoalmodel',
            name='target_timeframe_weeks',
        ),
        migrations.RemoveField(
            model_name='caloriegoalmodel',
            name='daily_calorie_target',
        ),
        migrations.RemoveField(
            model_name='caloriegoalmodel',
            name='weekly_weight_change_kg',
        ),
        
        # Add new fields to CalorieGoalModel
        migrations.AddField(
            model_name='caloriegoalmodel',
            name='target_calories',
            field=models.PositiveIntegerField(
                validators=[
                    django.core.validators.MinValueValidator(800),
                    django.core.validators.MaxValueValidator(8000),
                ],
                default=2000,
                verbose_name='Target Daily Calories',
                help_text='Target calories per day',
            ),
        ),
        migrations.AddField(
            model_name='caloriegoalmodel',
            name='target_weight',
            field=models.DecimalField(
                max_digits=5,
                decimal_places=1,
                validators=[
                    django.core.validators.MinValueValidator(Decimal('20.0')),
                    django.core.validators.MaxValueValidator(Decimal('500.0')),
                ],
                default=Decimal('70.0'),
                verbose_name='Target Weight (kg)',
                help_text='Target weight in kilograms',
            ),
        ),
        migrations.AddField(
            model_name='caloriegoalmodel',
            name='current_weight',
            field=models.DecimalField(
                max_digits=5,
                decimal_places=1,
                validators=[
                    django.core.validators.MinValueValidator(Decimal('20.0')),
                    django.core.validators.MaxValueValidator(Decimal('500.0')),
                ],
                default=Decimal('70.0'),
                verbose_name='Current Weight (kg)',
                help_text='Current weight in kilograms',
            ),
        ),
        migrations.AddField(
            model_name='caloriegoalmodel',
            name='target_date',
            field=models.DateField(
                default=django.utils.timezone.now,
                verbose_name='Target Date',
                help_text='Date to achieve the goal',
            ),
        ),
        migrations.AddField(
            model_name='caloriegoalmodel',
            name='weekly_weight_loss_rate',
            field=models.DecimalField(
                max_digits=3,
                decimal_places=1,
                default=Decimal('0.5'),
                validators=[
                    django.core.validators.MinValueValidator(Decimal('0.1')),
                    django.core.validators.MaxValueValidator(Decimal('2.0')),
                ],
                verbose_name='Weekly Weight Change Rate (kg)',
                help_text='Target weight change per week in kg',
            ),
        ),
        
        # Update goal_type field choices
        migrations.AlterField(
            model_name='caloriegoalmodel',
            name='goal_type',
            field=models.CharField(
                max_length=20,
                choices=[
                    ('weight_loss', 'Weight Loss'),
                    ('weight_maintenance', 'Weight Maintenance'),
                    ('weight_gain', 'Weight Gain'),
                    ('muscle_gain', 'Muscle Gain'),
                    ('cutting', 'Cutting (Fat Loss)'),
                    ('bulking', 'Bulking (Muscle Gain)'),
                ],
                default='weight_maintenance',
                verbose_name='Goal Type',
            ),
        ),
        
        # Update indexes to match new model structure
        migrations.RemoveIndex(
            model_name='caloriecalculationmodel',
            name='calorie_cal_user_id_a1b2c3_idx',
        ),
        migrations.RemoveIndex(
            model_name='caloriegoalmodel',
            name='calorie_goa_goal_ty_m4n5o6_idx',
        ),
        
        # Add new indexes
        migrations.AddIndex(
            model_name='caloriecalculationmodel',
            index=models.Index(fields=['user', '-created'], name='calorie_calc_user_created_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriecalculationmodel',
            index=models.Index(fields=['goal_type'], name='calorie_calc_goal_type_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriegoalmodel',
            index=models.Index(fields=['goal_type'], name='calorie_goal_goal_type_idx'),
        ),
    ] 