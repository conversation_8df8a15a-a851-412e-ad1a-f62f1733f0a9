# Generated by Django 4.2.23 on 2025-06-20 17:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("apps_calculators_calorie", "0003_update_models_schema"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="caloriecalculationmodel",
            name="calorie_cal_created_g7h8i9_idx",
        ),
        migrations.RemoveIndex(
            model_name="caloriegoalmodel",
            name="calorie_goa_user_id_j1k2l3_idx",
        ),
        migrations.RemoveIndex(
            model_name="caloriegoalmodel",
            name="calorie_goa_created_s1t2u3_idx",
        ),
        migrations.RenameIndex(
            model_name="caloriecalculationmodel",
            new_name="calorie_cal_user_id_6fe634_idx",
            old_name="calorie_calc_user_created_idx",
        ),
        migrations.RenameIndex(
            model_name="caloriecalculationmodel",
            new_name="calorie_cal_goal_ty_a31bc2_idx",
            old_name="calorie_calc_goal_type_idx",
        ),
        migrations.RenameIndex(
            model_name="caloriecalculationmodel",
            new_name="calorie_cal_is_acti_b045f6_idx",
            old_name="calorie_cal_is_acti_d4e5f6_idx",
        ),
        migrations.RenameIndex(
            model_name="caloriegoalmodel",
            new_name="calorie_goa_goal_ty_3955a7_idx",
            old_name="calorie_goal_goal_type_idx",
        ),
        migrations.RenameIndex(
            model_name="caloriegoalmodel",
            new_name="calorie_goa_is_achi_e21c43_idx",
            old_name="calorie_goa_is_achi_p7q8r9_idx",
        ),
        migrations.AlterField(
            model_name="caloriecalculationmodel",
            name="is_active",
            field=models.BooleanField(
                default=True,
                help_text="Whether this calculation is active/current",
                verbose_name="Active",
            ),
        ),
        migrations.AlterField(
            model_name="caloriecalculationmodel",
            name="notes",
            field=models.TextField(
                blank=True,
                help_text="Optional notes about this calorie calculation",
                null=True,
                verbose_name="Notes",
            ),
        ),
        migrations.AlterField(
            model_name="caloriegoalmodel",
            name="notes",
            field=models.TextField(
                blank=True,
                help_text="Additional notes about this goal",
                null=True,
                verbose_name="Notes",
            ),
        ),
        migrations.AddIndex(
            model_name="caloriegoalmodel",
            index=models.Index(
                fields=["user", "-created"], name="calorie_goa_user_id_47eee7_idx"
            ),
        ),
    ]
