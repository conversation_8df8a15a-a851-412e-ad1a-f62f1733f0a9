

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CalorieCalculationModel',
            fields=[
                ('created', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('_id', models.IntegerField(db_index=True, editable=False, null=True, unique=True)),
                ('height_cm', models.DecimalField(decimal_places=1, help_text='Height in centimeters', max_digits=5, validators=[django.core.validators.MinValueValidator(50.0), django.core.validators.MaxValueValidator(300.0)], verbose_name='Height (cm)')),
                ('weight_kg', models.DecimalField(decimal_places=1, help_text='Current weight in kilograms', max_digits=5, validators=[django.core.validators.MinValueValidator(20.0), django.core.validators.MaxValueValidator(500.0)], verbose_name='Weight (kg)')),
                ('age', models.PositiveIntegerField(help_text='Age in years', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(120)], verbose_name='Age')),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female')], max_length=10, verbose_name='Gender')),
                ('activity_level', models.CharField(choices=[('sedentary', 'Sedentary (little to no exercise)'), ('lightly_active', 'Lightly Active (light exercise 1-3 days/week)'), ('moderately_active', 'Moderately Active (moderate exercise 3-5 days/week)'), ('very_active', 'Very Active (heavy exercise 6-7 days/week)'), ('extremely_active', 'Extremely Active (very heavy physical work or 2x/day training)')], default='moderately_active', max_length=20, verbose_name='Activity Level')),
                ('formula', models.CharField(choices=[('mifflin_st_jeor', 'Mifflin-St Jeor (Recommended)'), ('harris_benedict', 'Harris-Benedict (Original)'), ('harris_benedict_revised', 'Harris-Benedict (Revised)'), ('katch_mcardle', 'Katch-McArdle (Body Fat Based)')], default='mifflin_st_jeor', help_text='Formula used for BMR calculation', max_length=25, verbose_name='Calculation Formula')),
                ('body_fat_percentage', models.DecimalField(blank=True, decimal_places=1, help_text='Body fat percentage (required for Katch-McArdle formula)', max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(3.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Body Fat (%)')),
                ('bmr_calories', models.PositiveIntegerField(help_text='Basal Metabolic Rate in calories', verbose_name='BMR (calories)')),
                ('tdee_calories', models.PositiveIntegerField(help_text='Total Daily Energy Expenditure in calories', verbose_name='TDEE (calories)')),
                ('maintenance_calories', models.PositiveIntegerField(help_text='Calories needed to maintain current weight', verbose_name='Maintenance Calories')),
                ('cutting_calories', models.PositiveIntegerField(help_text='Calories for weight loss (500 cal deficit)', verbose_name='Cutting Calories')),
                ('aggressive_cutting_calories', models.PositiveIntegerField(help_text='Calories for aggressive weight loss (750 cal deficit)', verbose_name='Aggressive Cutting')),
                ('bulking_calories', models.PositiveIntegerField(help_text='Calories for weight gain (300 cal surplus)', verbose_name='Bulking Calories')),
                ('lean_bulking_calories', models.PositiveIntegerField(help_text='Calories for lean muscle gain (150 cal surplus)', verbose_name='Lean Bulking')),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this calculation', null=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, help_text='Mark as active calculation for the user', verbose_name='Active')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calorie_calculations', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Calorie Calculation',
                'verbose_name_plural': 'Calorie Calculations',
                'db_table': 'calorie_calculation',
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='CalorieGoalModel',
            fields=[
                ('created', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('_id', models.IntegerField(db_index=True, editable=False, null=True, unique=True)),
                ('goal_type', models.CharField(choices=[('weight_loss', 'Weight Loss'), ('weight_gain', 'Weight Gain'), ('maintain', 'Maintain Weight'), ('recomposition', 'Body Recomposition')], max_length=15, verbose_name='Goal Type')),
                ('target_weight_kg', models.DecimalField(blank=True, decimal_places=1, help_text='Target weight in kilograms', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(20.0), django.core.validators.MaxValueValidator(500.0)], verbose_name='Target Weight (kg)')),
                ('target_body_fat_percentage', models.DecimalField(blank=True, decimal_places=1, help_text='Target body fat percentage', max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(3.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Target Body Fat (%)')),
                ('target_timeframe_weeks', models.PositiveIntegerField(help_text='Target timeframe to achieve goal in weeks', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(208)], verbose_name='Timeframe (weeks)')),
                ('daily_calorie_target', models.PositiveIntegerField(help_text='Daily calorie target to achieve goal', validators=[django.core.validators.MinValueValidator(800), django.core.validators.MaxValueValidator(8000)], verbose_name='Daily Calorie Target')),
                ('weekly_weight_change_kg', models.DecimalField(decimal_places=2, help_text='Expected weekly weight change in kg', max_digits=4, verbose_name='Weekly Weight Change (kg)')),
                ('goal_description', models.TextField(blank=True, help_text='Description of the calorie goal', null=True, verbose_name='Goal Description')),
                ('is_achieved', models.BooleanField(default=False, help_text='Whether this goal has been achieved', verbose_name='Achieved')),
                ('achieved_date', models.DateField(blank=True, help_text='Date when goal was achieved', null=True, verbose_name='Achieved Date')),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this goal', null=True, verbose_name='Notes')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calorie_goals', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Calorie Goal',
                'verbose_name_plural': 'Calorie Goals',
                'db_table': 'calorie_goal',
                'ordering': ['-created'],
            },
        ),
        migrations.AddIndex(
            model_name='caloriecalculationmodel',
            index=models.Index(fields=['user'], name='calorie_cal_user_id_a1b2c3_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriecalculationmodel',
            index=models.Index(fields=['is_active'], name='calorie_cal_is_acti_d4e5f6_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriecalculationmodel',
            index=models.Index(fields=['-created'], name='calorie_cal_created_g7h8i9_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriegoalmodel',
            index=models.Index(fields=['user'], name='calorie_goa_user_id_j1k2l3_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriegoalmodel',
            index=models.Index(fields=['goal_type'], name='calorie_goa_goal_ty_m4n5o6_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriegoalmodel',
            index=models.Index(fields=['is_achieved'], name='calorie_goa_is_achi_p7q8r9_idx'),
        ),
        migrations.AddIndex(
            model_name='caloriegoalmodel',
            index=models.Index(fields=['-created'], name='calorie_goa_created_s1t2u3_idx'),
        ),
    ] 