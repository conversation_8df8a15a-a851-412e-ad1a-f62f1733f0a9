from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from decimal import Decimal, ROUND_HALF_UP
from django.utils import timezone

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class ActivityLevel(models.TextChoices):
    """Activity level choices for calorie calculation"""
    
    SEDENTARY = "sedentary", _("Sedentary (little to no exercise)")
    LIGHTLY_ACTIVE = "lightly_active", _("Lightly Active (light exercise 1-3 days/week)")
    MODERATELY_ACTIVE = "moderately_active", _("Moderately Active (moderate exercise 3-5 days/week)")
    VERY_ACTIVE = "very_active", _("Very Active (heavy exercise 6-7 days/week)")
    EXTREMELY_ACTIVE = "extremely_active", _("Extremely Active (very heavy physical work or 2x/day training)")


class CalorieGoalType(models.TextChoices):
    """Calorie goal type choices"""
    
    WEIGHT_LOSS = "weight_loss", _("Weight Loss")
    WEIGHT_MAINTENANCE = "weight_maintenance", _("Weight Maintenance")
    WEIGHT_GAIN = "weight_gain", _("Weight Gain")
    MUSCLE_GAIN = "muscle_gain", _("Muscle Gain")
    CUTTING = "cutting", _("Cutting (Fat Loss)")
    BULKING = "bulking", _("Bulking (Muscle Gain)")


class CalorieCalculationModel(AbstractAutoIncrementModel):
    """
    Model to store calorie calculations for users with historical tracking.
    
    Calculates daily calorie needs based on:
    - BMR (Basal Metabolic Rate) using Mifflin-St Jeor equation
    - Activity level multipliers
    - Goal-specific adjustments
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="calorie_calculations",
        verbose_name=_("User"),
    )
    
    
    age = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1),
            MaxValueValidator(120),
        ],
        default=30,
        verbose_name=_("Age"),
        help_text=_("Age in years (1-120)"),
    )
    
    gender = models.CharField(
        max_length=10,
        choices=[
            ('male', _('Male')),
            ('female', _('Female')),
        ],
        default='male',
        verbose_name=_("Gender"),
    )
    
    weight = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        validators=[
            MinValueValidator(Decimal('20.0')),
            MaxValueValidator(Decimal('500.0')),
        ],
        default=Decimal('70.0'),
        verbose_name=_("Weight (kg)"),
        help_text=_("Weight in kilograms (20-500 kg)"),
    )
    
    height = models.PositiveIntegerField(
        validators=[
            MinValueValidator(50),
            MaxValueValidator(300),
        ],
        default=170,
        verbose_name=_("Height (cm)"),
        help_text=_("Height in centimeters (50-300 cm)"),
    )
    
    activity_level = models.CharField(
        max_length=20,
        choices=ActivityLevel.choices,
        default=ActivityLevel.MODERATELY_ACTIVE,
        verbose_name=_("Activity Level"),
    )
    
    goal_type = models.CharField(
        max_length=20,
        choices=CalorieGoalType.choices,
        default=CalorieGoalType.WEIGHT_MAINTENANCE,
        verbose_name=_("Goal Type"),
    )
    
    
    bmr = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_("BMR (kcal/day)"),
        help_text=_("Basal Metabolic Rate"),
    )
    
    tdee = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_("TDEE (kcal/day)"),
        help_text=_("Total Daily Energy Expenditure"),
    )
    
    target_calories = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name=_("Target Calories (kcal/day)"),
        help_text=_("Recommended daily calories for goal"),
    )
    
    calorie_adjustment = models.IntegerField(
        blank=True,
        null=True,
        default=0,
        verbose_name=_("Calorie Adjustment"),
        help_text=_("Calories added/subtracted from TDEE for goal"),
    )
    
    
    activity_multiplier = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name=_("Activity Multiplier"),
        help_text=_("Multiplier applied to BMR for activity level"),
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Optional notes about this calorie calculation"),
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this calculation is active/current"),
    )
    
    class Meta:
        app_label = "apps_calculators_calorie"
        db_table = "calorie_calculation"
        verbose_name = _("Calorie Calculation")
        verbose_name_plural = _("Calorie Calculations")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["goal_type"]),
            models.Index(fields=["is_active"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.target_calories} kcal/day ({self.get_goal_type_display()})"
    
    @staticmethod
    def calculate_bmr(weight: float, height: int, age: int, gender: str) -> int:
        """
        Calculate BMR using Mifflin-St Jeor equation.
        
        Args:
            weight: Weight in kg
            height: Height in cm
            age: Age in years
            gender: 'male' or 'female'
            
        Returns:
            BMR in kcal/day
        """
        if gender.lower() == 'male':
            bmr = 10 * weight + 6.25 * height - 5 * age + 5
        else:
            bmr = 10 * weight + 6.25 * height - 5 * age - 161
        
        return int(bmr)
    
    @staticmethod
    def get_activity_multiplier(activity_level: str) -> float:
        """
        Get activity multiplier for TDEE calculation.
        
        Args:
            activity_level: Activity level choice
            
        Returns:
            Activity multiplier
        """
        multipliers = {
            ActivityLevel.SEDENTARY: 1.2,
            ActivityLevel.LIGHTLY_ACTIVE: 1.375,
            ActivityLevel.MODERATELY_ACTIVE: 1.55,
            ActivityLevel.VERY_ACTIVE: 1.725,
            ActivityLevel.EXTREMELY_ACTIVE: 1.9,
        }
        return multipliers.get(activity_level, 1.55)
    
    @staticmethod
    def get_goal_adjustment(goal_type: str, tdee: int) -> int:
        """
        Get calorie adjustment for specific goals.
        
        Args:
            goal_type: Goal type choice
            tdee: Total Daily Energy Expenditure
            
        Returns:
            Calorie adjustment (positive for surplus, negative for deficit)
        """
        adjustments = {
            CalorieGoalType.WEIGHT_LOSS: -500,  
            CalorieGoalType.WEIGHT_MAINTENANCE: 0,
            CalorieGoalType.WEIGHT_GAIN: 300,  
            CalorieGoalType.MUSCLE_GAIN: 200,  
            CalorieGoalType.CUTTING: -750,  
            CalorieGoalType.BULKING: 500,  
        }
        return adjustments.get(goal_type, 0)
    
    @property
    def daily_protein_grams(self) -> int:
        """Calculate recommended daily protein in grams"""
        
        return int(float(self.weight) * 2.0)
    
    @property
    def daily_fat_grams(self) -> int:
        """Calculate recommended daily fat in grams"""
        
        return int((self.target_calories * 0.275) / 9)
    
    @property
    def daily_carb_grams(self) -> int:
        """Calculate recommended daily carbohydrates in grams"""
        
        protein_calories = self.daily_protein_grams * 4
        fat_calories = self.daily_fat_grams * 9
        remaining_calories = self.target_calories - protein_calories - fat_calories
        return max(0, int(remaining_calories / 4))
    
    @property
    def macros_dict(self) -> dict:
        """Get macronutrient breakdown as dictionary"""
        return {
            'protein_grams': self.daily_protein_grams,
            'fat_grams': self.daily_fat_grams,
            'carb_grams': self.daily_carb_grams,
            'protein_calories': self.daily_protein_grams * 4,
            'fat_calories': self.daily_fat_grams * 9,
            'carb_calories': self.daily_carb_grams * 4,
        }
    
    def save(self, *args, **kwargs):
        """Override save to auto-calculate calorie values"""
        
        
        self.bmr = self.calculate_bmr(
            float(self.weight), self.height, self.age, self.gender
        )
        
        
        self.activity_multiplier = Decimal(str(self.get_activity_multiplier(self.activity_level)))
        self.tdee = int(self.bmr * float(self.activity_multiplier))
        
        
        self.calorie_adjustment = self.get_goal_adjustment(self.goal_type, self.tdee)
        self.target_calories = self.tdee + self.calorie_adjustment
        
        
        min_calories = 1200 if self.gender == 'female' else 1500
        self.target_calories = max(self.target_calories, min_calories)
        
        
        if self.is_active and not self.pk:
            CalorieCalculationModel.objects.filter(
                user=self.user, is_active=True
            ).update(is_active=False)
        
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate the model data"""
        from django.core.exceptions import ValidationError
        
        super().clean()
        
        if self.weight <= 0:
            raise ValidationError({
                'weight': _('Weight must be positive.')
            })
        
        if self.height <= 0:
            raise ValidationError({
                'height': _('Height must be positive.')
            })


class CalorieGoalModel(AbstractAutoIncrementModel):
    """
    Model to store user calorie goals and track progress.
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="calorie_goals",
        verbose_name=_("User"),
    )
    
    goal_type = models.CharField(
        max_length=20,
        choices=CalorieGoalType.choices,
        default=CalorieGoalType.WEIGHT_MAINTENANCE,
        verbose_name=_("Goal Type"),
    )
    
    target_calories = models.PositiveIntegerField(
        validators=[
            MinValueValidator(800),
            MaxValueValidator(8000),
        ],
        default=2000,
        verbose_name=_("Target Daily Calories"),
        help_text=_("Target calories per day"),
    )
    
    target_weight = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        validators=[
            MinValueValidator(Decimal('20.0')),
            MaxValueValidator(Decimal('500.0')),
        ],
        default=Decimal('70.0'),
        verbose_name=_("Target Weight (kg)"),
        help_text=_("Target weight in kilograms"),
    )
    
    current_weight = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        validators=[
            MinValueValidator(Decimal('20.0')),
            MaxValueValidator(Decimal('500.0')),
        ],
        default=Decimal('70.0'),
        verbose_name=_("Current Weight (kg)"),
        help_text=_("Current weight in kilograms"),
    )
    
    target_date = models.DateField(
        default=timezone.now,
        verbose_name=_("Target Date"),
        help_text=_("Date to achieve the goal"),
    )
    
    weekly_weight_loss_rate = models.DecimalField(
        max_digits=3,
        decimal_places=1,
        default=Decimal('0.5'),
        validators=[
            MinValueValidator(Decimal('0.1')),
            MaxValueValidator(Decimal('2.0')),
        ],
        verbose_name=_("Weekly Weight Change Rate (kg)"),
        help_text=_("Target weight change per week in kg"),
    )
    
    goal_description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Goal Description"),
        help_text=_("Description of the calorie goal"),
    )
    
    is_achieved = models.BooleanField(
        default=False,
        verbose_name=_("Achieved"),
        help_text=_("Whether this goal has been achieved"),
    )
    
    achieved_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Achieved Date"),
        help_text=_("Date when goal was achieved"),
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Additional notes about this goal"),
    )
    
    class Meta:
        app_label = "apps_calculators_calorie"
        db_table = "calorie_goal"
        verbose_name = _("Calorie Goal")
        verbose_name_plural = _("Calorie Goals")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["goal_type"]),
            models.Index(fields=["is_achieved"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.get_goal_type_display()}: {self.target_calories} kcal/day"
    
    @property
    def weight_difference(self) -> float:
        """Calculate weight difference to target"""
        return float(self.target_weight - self.current_weight)
    
    @property
    def days_to_goal(self) -> int:
        """Calculate days remaining to reach goal"""
        today = timezone.now().date()
        return (self.target_date - today).days
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage towards goal"""
        if self.is_achieved:
            return 100.0
        
        total_weight_change = abs(self.weight_difference)
        if total_weight_change == 0:
            return 100.0
        
        
        
        days_elapsed = (timezone.now().date() - self.created.date()).days
        expected_weight_change = (float(self.weekly_weight_loss_rate) / 7) * days_elapsed
        
        progress = min(expected_weight_change / total_weight_change * 100, 100)
        return max(0, progress)



CalorieCalculation = CalorieCalculationModel
CalorieGoal = CalorieGoalModel 