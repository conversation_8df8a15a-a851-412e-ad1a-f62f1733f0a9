from django.urls import path

from .views import (
    CalorieCalculationListCreateView,
    CalorieCalculationDetailView,
    CalorieCalculationActiveView,
    CalorieGoalListCreateView,
    CalorieGoalDetailView,
    CalorieGoalAchieveView,
    CalorieStatsView,
    CalorieRecommendationsView,
    CalorieQuickCalculationView,
)

app_name = 'apps_calculators_calorie'

urlpatterns = [
    
    path(
        'calculations/',
        CalorieCalculationListCreateView.as_view(),
        name='calculation-list-create'
    ),
    path(
        'calculations/<uuid:pk>/',
        CalorieCalculationDetailView.as_view(),
        name='calculation-detail'
    ),
    path(
        'calculations/active/',
        CalorieCalculationActiveView.as_view(),
        name='calculation-active'
    ),
    path(
        'calculations/quick/',
        CalorieQuickCalculationView.as_view(),
        name='calculation-quick'
    ),
    
    
    path(
        'goals/',
        CalorieGoalListCreateView.as_view(),
        name='goal-list-create'
    ),
    path(
        'goals/<uuid:pk>/',
        CalorieGoalDetailView.as_view(),
        name='goal-detail'
    ),
    path(
        'goals/<uuid:pk>/achieve/',
        CalorieGoalAchieveView.as_view(),
        name='goal-achieve'
    ),
    
    
    path(
        'stats/',
        CalorieStatsView.as_view(),
        name='stats'
    ),
    path(
        'recommendations/',
        CalorieRecommendationsView.as_view(),
        name='recommendations'
    ),
] 