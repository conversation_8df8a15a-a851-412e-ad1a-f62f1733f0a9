"""
Calorie Goal Detail View

Handle retrieving, updating, and deleting calorie goals.
"""

from rest_framework import generics, permissions

from ..models import CalorieGoalModel
from ..serializers import CalorieGoalSerializer


class CalorieGoalDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a calorie goal.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CalorieGoalSerializer
    
    def get_queryset(self):
        return CalorieGoalModel.objects.filter(user=self.request.user) 