"""
Calorie Calculation Detail View

Handle retrieving, updating, and deleting calorie calculations.
"""

from rest_framework import generics, permissions

from ..models import CalorieCalculationModel
from ..serializers import CalorieCalculationSerializer


class CalorieCalculationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a calorie calculation.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CalorieCalculationSerializer
    
    def get_queryset(self):
        return CalorieCalculationModel.objects.filter(user=self.request.user) 