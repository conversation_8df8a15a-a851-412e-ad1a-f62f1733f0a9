"""
Calorie Goal List Create View

Handle listing and creating calorie goals.
"""

from rest_framework import generics, permissions

from ..models import CalorieGoalModel
from ..serializers import CalorieGoalSerializer, CalorieGoalCreateSerializer


class CalorieGoalListCreateView(generics.ListCreateAPIView):
    """
    List user's calorie goals or create a new one.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return CalorieGoalModel.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CalorieGoalCreateSerializer
        return CalorieGoalSerializer 