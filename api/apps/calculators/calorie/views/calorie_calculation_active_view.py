"""
Calorie Calculation Active View

Handle retrieving the user's active calorie calculation.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import CalorieCalculationModel
from ..serializers import CalorieCalculationSerializer


class CalorieCalculationActiveView(generics.RetrieveAPIView):
    """
    Get the user's active calorie calculation.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CalorieCalculationSerializer
    
    def get_object(self):
        try:
            return CalorieCalculationModel.objects.get(
                user=self.request.user,
                is_active=True
            )
        except CalorieCalculationModel.DoesNotExist:
            return None
    
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance is None:
            return Response(
                {'detail': _('No active calorie calculation found.')},
                status=status.HTTP_404_NOT_FOUND
            )
        serializer = self.get_serializer(instance)
        return Response(serializer.data) 