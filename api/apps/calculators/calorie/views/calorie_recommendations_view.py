"""
Calorie Recommendations View

Handle providing personalized calorie recommendations.
"""

from rest_framework import generics, permissions
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import CalorieCalculationModel, ActivityLevel, CalorieGoalType
from ..serializers import CalorieRecommendationSerializer


class CalorieRecommendationsView(generics.GenericAPIView):
    """
    Get personalized calorie recommendations based on user profile and goals.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CalorieRecommendationSerializer
    
    def post(self, request, *args, **kwargs):
        latest_calc = CalorieCalculationModel.objects.filter(
            user=request.user
        ).order_by('-created').first()
        
        age = request.data.get('age', latest_calc.age if latest_calc else 30)
        gender = request.data.get('gender', latest_calc.gender if latest_calc else 'male')
        weight = float(request.data.get('weight', latest_calc.weight if latest_calc else 70))
        height = int(request.data.get('height', latest_calc.height if latest_calc else 170))
        activity_level = request.data.get('activity_level', ActivityLevel.MODERATELY_ACTIVE)
        primary_goal = request.data.get('primary_goal', CalorieGoalType.WEIGHT_MAINTENANCE)
        
        bmr = CalorieCalculationModel.calculate_bmr(weight, height, age, gender)
        activity_multiplier = CalorieCalculationModel.get_activity_multiplier(activity_level)
        tdee = int(bmr * activity_multiplier)
        
        calorie_adjustment = CalorieCalculationModel.get_goal_adjustment(primary_goal, tdee)
        recommended_calories = tdee + calorie_adjustment
        
        min_calories = 1200 if gender == 'female' else 1500
        recommended_calories = max(recommended_calories, min_calories)
        
        calorie_range_min = max(min_calories, recommended_calories - 200)
        calorie_range_max = recommended_calories + 200
        
        protein_grams = int(weight * 2.0)
        fat_grams = int((recommended_calories * 0.275) / 9)
        carb_grams = int((recommended_calories - (protein_grams * 4) - (fat_grams * 9)) / 4)
        
        goal_reasons = {
            CalorieGoalType.WEIGHT_LOSS: _("Creating a moderate calorie deficit for sustainable weight loss"),
            CalorieGoalType.WEIGHT_GAIN: _("Providing calorie surplus for healthy weight gain"),
            CalorieGoalType.MUSCLE_GAIN: _("Optimizing calories and protein for muscle building"),
            CalorieGoalType.CUTTING: _("Aggressive calorie deficit for fat loss while preserving muscle"),
            CalorieGoalType.BULKING: _("Substantial calorie surplus for maximum muscle and strength gains"),
            CalorieGoalType.WEIGHT_MAINTENANCE: _("Maintaining current weight with balanced nutrition"),
        }
        
        tips = []
        if primary_goal in [CalorieGoalType.WEIGHT_LOSS, CalorieGoalType.CUTTING]:
            tips.extend([
                _("Focus on high-protein foods to maintain muscle mass"),
                _("Include plenty of vegetables for volume and nutrients"),
                _("Stay hydrated and get adequate sleep"),
                _("Consider strength training to preserve muscle"),
            ])
        elif primary_goal in [CalorieGoalType.WEIGHT_GAIN, CalorieGoalType.BULKING]:
            tips.extend([
                _("Eat frequent, nutrient-dense meals"),
                _("Include healthy fats like nuts, avocado, and olive oil"),
                _("Focus on compound exercises for muscle building"),
                _("Track your progress with measurements, not just weight"),
            ])
        else:
            tips.extend([
                _("Maintain a balanced diet with all food groups"),
                _("Stay consistent with your eating patterns"),
                _("Listen to your hunger and fullness cues"),
                _("Regular physical activity supports overall health"),
            ])
        
        recommendation_data = {
            'recommended_calories': recommended_calories,
            'calorie_range_min': calorie_range_min,
            'calorie_range_max': calorie_range_max,
            'recommended_goal_type': primary_goal,
            'recommended_activity_level': activity_level,
            'recommended_protein_grams': protein_grams,
            'recommended_carb_grams': carb_grams,
            'recommended_fat_grams': fat_grams,
            'recommendation_reason': goal_reasons.get(primary_goal, ''),
            'tips': tips,
        }
        
        serializer = self.get_serializer(recommendation_data)
        return Response(serializer.data) 