"""
Calorie Goal Achieve View

Handle marking calorie goals as achieved.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from ..models import CalorieGoalModel
from ..serializers import CalorieGoalSerializer


class CalorieGoalAchieveView(generics.UpdateAPIView):
    """
    Mark a calorie goal as achieved.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CalorieGoalSerializer
    
    def get_queryset(self):
        return CalorieGoalModel.objects.filter(user=self.request.user)
    
    def update(self, request, *args, **kwargs):
        try:
            goal = self.get_object()
        except CalorieGoalModel.DoesNotExist:
            return Response(
                {'detail': _('Goal not found.')},
                status=status.HTTP_404_NOT_FOUND
            )
        
        if goal.is_achieved:
            return Response(
                {'detail': _('Goal is already marked as achieved.')},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        goal.is_achieved = True
        goal.achieved_date = timezone.now().date()
        goal.save()
        
        serializer = self.get_serializer(goal)
        return Response(serializer.data) 