"""
Calorie Calculation List Create View

Handle listing and creating calorie calculations.
"""

from rest_framework import generics, permissions

from ..models import CalorieCalculationModel
from ..serializers import CalorieCalculationSerializer, CalorieCalculationCreateSerializer


class CalorieCalculationListCreateView(generics.ListCreateAPIView):
    """
    List user's calorie calculations or create a new one.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return CalorieCalculationModel.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CalorieCalculationCreateSerializer
        return CalorieCalculationSerializer 