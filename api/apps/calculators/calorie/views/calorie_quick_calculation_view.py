"""
Calorie Quick Calculation View

Handle quick calorie calculations without saving to database.
"""

from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import CalorieCalculationModel


class CalorieQuickCalculationView(generics.GenericAPIView):
    """
    Perform a quick calorie calculation without saving to database.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        required_fields = ['age', 'gender', 'weight', 'height', 'activity_level', 'goal_type']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {'detail': _(f'{field} is required.')},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        try:
            age = int(request.data['age'])
            gender = request.data['gender']
            weight = float(request.data['weight'])
            height = int(request.data['height'])
            activity_level = request.data['activity_level']
            goal_type = request.data['goal_type']
            
            if not (1 <= age <= 120):
                raise ValueError("Age must be between 1 and 120")
            if not (20 <= weight <= 500):
                raise ValueError("Weight must be between 20 and 500 kg")
            if not (50 <= height <= 300):
                raise ValueError("Height must be between 50 and 300 cm")
            
            bmr = CalorieCalculationModel.calculate_bmr(weight, height, age, gender)
            activity_multiplier = CalorieCalculationModel.get_activity_multiplier(activity_level)
            tdee = int(bmr * activity_multiplier)
            calorie_adjustment = CalorieCalculationModel.get_goal_adjustment(goal_type, tdee)
            target_calories = tdee + calorie_adjustment
            
            min_calories = 1200 if gender == 'female' else 1500
            target_calories = max(target_calories, min_calories)
            
            protein_grams = int(weight * 2.0)
            fat_grams = int((target_calories * 0.275) / 9)
            carb_grams = int((target_calories - (protein_grams * 4) - (fat_grams * 9)) / 4)
            
            result = {
                'bmr': bmr,
                'tdee': tdee,
                'target_calories': target_calories,
                'calorie_adjustment': calorie_adjustment,
                'activity_multiplier': round(activity_multiplier, 2),
                'daily_protein_grams': protein_grams,
                'daily_fat_grams': fat_grams,
                'daily_carb_grams': carb_grams,
                'macros_dict': {
                    'protein_grams': protein_grams,
                    'fat_grams': fat_grams,
                    'carb_grams': carb_grams,
                    'protein_calories': protein_grams * 4,
                    'fat_calories': fat_grams * 9,
                    'carb_calories': carb_grams * 4,
                }
            }
            
            return Response(result)
            
        except (ValueError, TypeError) as e:
            return Response(
                {'detail': _('Invalid input: {error}').format(error=str(e))},
                status=status.HTTP_400_BAD_REQUEST
            ) 