"""
Calorie Stats View

Handle retrieving comprehensive calorie calculator statistics.
"""

from rest_framework import generics, permissions
from rest_framework.response import Response
from django.db.models import Avg, Count

from ..models import CalorieCalculationModel, CalorieGoalModel
from ..serializers import CalorieStatsSerializer


class CalorieStatsView(generics.GenericAPIView):
    """
    Get comprehensive statistics for the user's calorie calculations and goals.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = CalorieStatsSerializer
    
    def get(self, request, *args, **kwargs):
        user = request.user
        
        calculations = CalorieCalculationModel.objects.filter(user=user)
        total_calculations = calculations.count()
        active_calculations = calculations.filter(is_active=True).count()
        
        goals = CalorieGoalModel.objects.filter(user=user)
        total_goals = goals.count()
        active_goals = goals.filter(is_achieved=False).count()
        achieved_goals = goals.filter(is_achieved=True).count()
        
        avg_calories = calculations.aggregate(
            avg=Avg('target_calories')
        )['avg'] or 0
        
        most_common_goal_type = calculations.values('goal_type').annotate(
            count=Count('goal_type')
        ).order_by('-count').first()
        
        most_common_activity_level = calculations.values('activity_level').annotate(
            count=Count('activity_level')
        ).order_by('-count').first()
        
        recent_calculations = calculations.order_by('-created')[:5]
        recent_goals = goals.order_by('-created')[:5]
        
        stats_data = {
            'total_calculations': total_calculations,
            'active_calculations': active_calculations,
            'total_goals': total_goals,
            'active_goals': active_goals,
            'achieved_goals': achieved_goals,
            'average_target_calories': int(avg_calories),
            'most_common_goal_type': most_common_goal_type['goal_type'] if most_common_goal_type else '',
            'most_common_activity_level': most_common_activity_level['activity_level'] if most_common_activity_level else '',
            'recent_calculations': recent_calculations,
            'recent_goals': recent_goals,
        }
        
        serializer = self.get_serializer(stats_data)
        return Response(serializer.data) 