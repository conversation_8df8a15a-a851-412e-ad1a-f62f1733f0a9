# Generated by Django 4.2.9 on 2025-06-11 22:42

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TDEEGoalModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "goal_type",
                    models.CharField(
                        choices=[
                            ("maintain", "Maintain current weight"),
                            ("lose", "Lose weight"),
                            ("gain", "Gain weight"),
                        ],
                        max_length=20,
                        verbose_name="Goal Type",
                    ),
                ),
                (
                    "target_calories_per_day",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Target daily calorie intake (500 - 9999 calories)",
                        max_digits=7,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("500.00")),
                            django.core.validators.MaxValueValidator(
                                Decimal("9999.99")
                            ),
                        ],
                        verbose_name="Target Calories Per Day",
                    ),
                ),
                (
                    "current_weight_kg",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Current weight in kilograms",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01")),
                            django.core.validators.MaxValueValidator(Decimal("999.99")),
                        ],
                        verbose_name="Current Weight (kg)",
                    ),
                ),
                (
                    "target_weight_kg",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Target weight in kilograms",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01")),
                            django.core.validators.MaxValueValidator(Decimal("999.99")),
                        ],
                        verbose_name="Target Weight (kg)",
                    ),
                ),
                (
                    "target_date",
                    models.DateField(
                        blank=True,
                        help_text="Target date to achieve the goal",
                        null=True,
                        verbose_name="Target Date",
                    ),
                ),
                (
                    "weekly_weight_change_goal",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Target weight change per week in kg (negative for loss, positive for gain)",
                        max_digits=4,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("-5.00")),
                            django.core.validators.MaxValueValidator(Decimal("5.00")),
                        ],
                        verbose_name="Weekly Weight Change Goal (kg)",
                    ),
                ),
                (
                    "is_achieved",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this goal has been achieved",
                        verbose_name="Achieved",
                    ),
                ),
                (
                    "achieved_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when goal was achieved",
                        null=True,
                        verbose_name="Achieved Date",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Optional notes about this goal",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tdee_goals",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "TDEE Goal",
                "verbose_name_plural": "TDEE Goals",
                "db_table": "tdee_tdeegoalmodel",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="tdee_tdeego_user_id_f8f679_idx",
                    ),
                    models.Index(
                        fields=["goal_type"], name="tdee_tdeego_goal_ty_55c43f_idx"
                    ),
                    models.Index(
                        fields=["is_achieved"], name="tdee_tdeego_is_achi_5bded4_idx"
                    ),
                    models.Index(
                        fields=["target_date"], name="tdee_tdeego_target__c04ab9_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TDEECalculationModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "weight_kg",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Weight in kilograms (0.01 - 999.99)",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01")),
                            django.core.validators.MaxValueValidator(Decimal("999.99")),
                        ],
                        verbose_name="Weight (kg)",
                    ),
                ),
                (
                    "height_cm",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Height in centimeters (1.00 - 300.00)",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.00")),
                            django.core.validators.MaxValueValidator(Decimal("300.00")),
                        ],
                        verbose_name="Height (cm)",
                    ),
                ),
                (
                    "age_years",
                    models.PositiveIntegerField(
                        help_text="Age in years (1 - 120)",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(120),
                        ],
                        verbose_name="Age (years)",
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("male", "Male"), ("female", "Female")],
                        max_length=10,
                        verbose_name="Gender",
                    ),
                ),
                (
                    "activity_level",
                    models.CharField(
                        choices=[
                            ("sedentary", "Sedentary (little or no exercise)"),
                            (
                                "light",
                                "Lightly active (light exercise/sports 1-3 days/week)",
                            ),
                            (
                                "moderate",
                                "Moderately active (moderate exercise/sports 3-5 days/week)",
                            ),
                            (
                                "high",
                                "Very active (hard exercise/sports 6-7 days a week)",
                            ),
                            (
                                "extreme",
                                "Extremely active (very hard exercise, physical job or training twice a day)",
                            ),
                        ],
                        max_length=20,
                        verbose_name="Activity Level",
                    ),
                ),
                (
                    "bmr_value",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Calculated Basal Metabolic Rate in calories/day",
                        max_digits=7,
                        verbose_name="BMR Value",
                    ),
                ),
                (
                    "tdee_value",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Calculated Total Daily Energy Expenditure in calories/day",
                        max_digits=7,
                        verbose_name="TDEE Value",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Optional notes about this TDEE calculation",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this calculation is active/current",
                        verbose_name="Active",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tdee_calculations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "TDEE Calculation",
                "verbose_name_plural": "TDEE Calculations",
                "db_table": "tdee_tdeecalculationmodel",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="tdee_tdeeca_user_id_524d7b_idx",
                    ),
                    models.Index(
                        fields=["activity_level"], name="tdee_tdeeca_activit_ec27f0_idx"
                    ),
                    models.Index(
                        fields=["is_active"], name="tdee_tdeeca_is_acti_43a5bb_idx"
                    ),
                ],
            },
        ),
    ]
