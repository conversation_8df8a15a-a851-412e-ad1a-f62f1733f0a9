from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from decimal import Decimal, ROUND_HALF_UP
from datetime import date

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class ActivityLevel(models.TextChoices):
    """Activity level choices for TDEE calculation"""

    SEDENTARY = "sedentary", _("Sedentary (little or no exercise)")
    LIGHT = "light", _("Lightly active (light exercise/sports 1-3 days/week)")
    MODERATE = "moderate", _(
        "Moderately active (moderate exercise/sports 3-5 days/week)"
    )
    HIGH = "high", _("Very active (hard exercise/sports 6-7 days a week)")
    EXTREME = "extreme", _(
        "Extremely active (very hard exercise, physical job or training twice a day)"
    )


class Gender(models.TextChoices):
    """Gender choices for BMR calculation"""

    MALE = "male", _("Male")
    FEMALE = "female", _("Female")


class GoalType(models.TextChoices):
    """Goal types for TDEE/calorie goals"""

    MAINTAIN = "maintain", _("Maintain current weight")
    LOSE = "lose", _("Lose weight")
    GAIN = "gain", _("Gain weight")


class TDEECalculationModel(AbstractAutoIncrementModel):
    """
    Model to store TDEE calculations for users with historical tracking.

    Uses the Harris-Benedict equation revised by Mifflin-St Jeor:
    - Men: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) + 5
    - Women: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) - 161

    Then TDEE = BMR × Activity Factor
    """

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="tdee_calculations",
        verbose_name=_("User"),
    )

    weight_kg = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("0.01")),
            MaxValueValidator(Decimal("999.99")),
        ],
        verbose_name=_("Weight (kg)"),
        help_text=_("Weight in kilograms (0.01 - 999.99)"),
    )

    height_cm = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("1.00")),
            MaxValueValidator(Decimal("300.00")),
        ],
        verbose_name=_("Height (cm)"),
        help_text=_("Height in centimeters (1.00 - 300.00)"),
    )

    age_years = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(120)],
        verbose_name=_("Age (years)"),
        help_text=_("Age in years (1 - 120)"),
    )

    gender = models.CharField(
        max_length=10, choices=Gender.choices, verbose_name=_("Gender")
    )

    activity_level = models.CharField(
        max_length=20, choices=ActivityLevel.choices, verbose_name=_("Activity Level")
    )

    bmr_value = models.DecimalField(
        max_digits=7,
        decimal_places=2,
        verbose_name=_("BMR Value"),
        help_text=_("Calculated Basal Metabolic Rate in calories/day"),
    )

    tdee_value = models.DecimalField(
        max_digits=7,
        decimal_places=2,
        verbose_name=_("TDEE Value"),
        help_text=_("Calculated Total Daily Energy Expenditure in calories/day"),
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Optional notes about this TDEE calculation"),
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this calculation is active/current"),
    )

    class Meta:
        app_label = "apps.calculators.tdee"
        db_table = "tdee_tdeecalculationmodel"
        verbose_name = _("TDEE Calculation")
        verbose_name_plural = _("TDEE Calculations")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["activity_level"]),
            models.Index(fields=["is_active"]),
        ]

    def __str__(self):
        return f"{self.user.email} - TDEE: {self.tdee_value} kcal/day ({self.get_activity_level_display()})"

    @staticmethod
    def calculate_bmr(
        weight_kg: Decimal, height_cm: Decimal, age_years: int, gender: str
    ) -> Decimal:
        """
        Calculate BMR using the Mifflin-St Jeor equation.

        Args:
            weight_kg: Weight in kilograms
            height_cm: Height in centimeters
            age_years: Age in years
            gender: Gender ('male' or 'female')

        Returns:
            BMR value in calories/day

        Raises:
            ValueError: If invalid gender provided
        """
        if gender == Gender.MALE:
            bmr = 10 * weight_kg + Decimal("6.25") * height_cm - 5 * age_years + 5
        elif gender == Gender.FEMALE:
            bmr = 10 * weight_kg + Decimal("6.25") * height_cm - 5 * age_years - 161
        else:
            raise ValueError(f"Invalid gender: {gender}")

        return bmr.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @staticmethod
    def get_activity_factor(activity_level: str) -> Decimal:
        """
        Get activity factor multiplier for TDEE calculation.

        Args:
            activity_level: Activity level string

        Returns:
            Activity factor as Decimal
        """
        activity_factors = {
            ActivityLevel.SEDENTARY: Decimal("1.2"),
            ActivityLevel.LIGHT: Decimal("1.375"),
            ActivityLevel.MODERATE: Decimal("1.55"),
            ActivityLevel.HIGH: Decimal("1.725"),
            ActivityLevel.EXTREME: Decimal("1.9"),
        }
        return activity_factors.get(activity_level, Decimal("1.2"))

    @staticmethod
    def calculate_tdee(bmr: Decimal, activity_level: str) -> Decimal:
        """
        Calculate TDEE by multiplying BMR with activity factor.

        Args:
            bmr: Basal Metabolic Rate
            activity_level: Activity level string

        Returns:
            TDEE value in calories/day
        """
        factor = TDEECalculationModel.get_activity_factor(activity_level)
        tdee = bmr * factor
        return tdee.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @property
    def activity_factor(self) -> Decimal:
        """Get the activity factor for this calculation"""
        return self.get_activity_factor(self.activity_level)

    @property
    def tdee_status_message(self) -> str:
        """Get a descriptive message about TDEE and calorie needs"""
        return _(
            "Your TDEE is %(tdee_value)s calories per day. "
            "This is the amount you need to maintain your current weight. "
            "To lose weight, eat less than this amount. "
            "To gain weight, eat more than this amount."
        ) % {"tdee_value": self.tdee_value}

    def save(self, *args, **kwargs):
        """Override save to auto-calculate BMR and TDEE"""

        self.bmr_value = self.calculate_bmr(
            self.weight_kg, self.height_cm, self.age_years, self.gender
        )

        self.tdee_value = self.calculate_tdee(self.bmr_value, self.activity_level)

        if self.is_active and not self.pk:
            TDEECalculationModel.objects.filter(user=self.user, is_active=True).update(
                is_active=False
            )

        super().save(*args, **kwargs)

    def clean(self):
        """Validate the model data"""
        from django.core.exceptions import ValidationError

        if self.weight_kg <= 0:
            raise ValidationError({"weight_kg": _("Weight must be positive.")})

        if self.height_cm <= 0:
            raise ValidationError({"height_cm": _("Height must be positive.")})

        if self.age_years <= 0:
            raise ValidationError({"age_years": _("Age must be positive.")})

        if self.weight_kg > Decimal("999.99"):
            raise ValidationError({"weight_kg": _("Weight seems unreasonably high.")})

        if self.height_cm > Decimal("300.00"):
            raise ValidationError({"height_cm": _("Height seems unreasonably high.")})

        if self.height_cm < Decimal("50.00"):
            raise ValidationError({"height_cm": _("Height seems unreasonably low.")})

        if self.age_years > 120:
            raise ValidationError({"age_years": _("Age seems unreasonably high.")})

        if len(str(self.notes)) > 500:
            raise ValidationError({"notes": _("Notes are too long.")})


class TDEEGoalModel(AbstractAutoIncrementModel):
    """
    Model to store user TDEE/calorie goals and targets.
    """

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="tdee_goals",
        verbose_name=_("User"),
    )

    goal_type = models.CharField(
        max_length=20, choices=GoalType.choices, verbose_name=_("Goal Type")
    )

    target_calories_per_day = models.DecimalField(
        max_digits=7,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("500.00")),
            MaxValueValidator(Decimal("9999.99")),
        ],
        verbose_name=_("Target Calories Per Day"),
        help_text=_("Target daily calorie intake (500 - 9999 calories)"),
    )

    current_weight_kg = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("0.01")),
            MaxValueValidator(Decimal("999.99")),
        ],
        verbose_name=_("Current Weight (kg)"),
        help_text=_("Current weight in kilograms"),
    )

    target_weight_kg = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("0.01")),
            MaxValueValidator(Decimal("999.99")),
        ],
        verbose_name=_("Target Weight (kg)"),
        help_text=_("Target weight in kilograms"),
    )

    target_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Target Date"),
        help_text=_("Target date to achieve the goal"),
    )

    weekly_weight_change_goal = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal("-5.00")),
            MaxValueValidator(Decimal("5.00")),
        ],
        default=Decimal("0.00"),
        verbose_name=_("Weekly Weight Change Goal (kg)"),
        help_text=_(
            "Target weight change per week in kg (negative for loss, positive for gain)"
        ),
    )

    is_achieved = models.BooleanField(
        default=False,
        verbose_name=_("Achieved"),
        help_text=_("Whether this goal has been achieved"),
    )

    achieved_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Achieved Date"),
        help_text=_("Date when goal was achieved"),
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Optional notes about this goal"),
    )

    class Meta:
        app_label = "apps.calculators.tdee"
        db_table = "tdee_tdeegoalmodel"
        verbose_name = _("TDEE Goal")
        verbose_name_plural = _("TDEE Goals")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["goal_type"]),
            models.Index(fields=["is_achieved"]),
            models.Index(fields=["target_date"]),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.get_goal_type_display()}: {self.target_calories_per_day} kcal/day"

    @property
    def weight_difference(self) -> Decimal:
        """Calculate the weight difference between current and target"""
        return self.target_weight_kg - self.current_weight_kg

    @property
    def calories_difference_from_maintenance(self) -> Decimal:
        """
        Calculate calorie difference from maintenance for weight change.
        Assumes 1 pound (0.45 kg) = 3500 calories.
        """

        current_calculation = self.user.tdee_calculations.filter(is_active=True).first()
        if not current_calculation:
            return Decimal("0.00")

        maintenance_calories = current_calculation.tdee_value
        return self.target_calories_per_day - maintenance_calories

    @property
    def estimated_weekly_weight_change(self) -> Decimal:
        """
        Estimate weekly weight change based on calorie deficit/surplus.
        3500 calories ≈ 1 pound (0.45 kg)
        """
        daily_calorie_diff = self.calories_difference_from_maintenance
        weekly_calorie_diff = daily_calorie_diff * 7

        kg_per_calorie = Decimal("0.45") / Decimal("3500")
        estimated_change = weekly_calorie_diff * kg_per_calorie

        return estimated_change.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @property
    def estimated_time_to_goal_weeks(self) -> int:
        """Estimate weeks to reach goal based on weekly weight change"""
        if self.weekly_weight_change_goal == 0:
            return 0

        weeks = abs(self.weight_difference / self.weekly_weight_change_goal)
        return int(weeks.quantize(Decimal("1"), rounding=ROUND_HALF_UP))

    @property
    def goal_status_message(self) -> str:
        """Get a descriptive message about the goal progress"""
        if self.is_achieved:
            return _("Congratulations! You've achieved your goal!")

        if self.goal_type == GoalType.MAINTAIN:
            return _(
                "Maintain your current weight by eating approximately {} calories per day."
            ).format(self.target_calories_per_day)
        elif self.goal_type == GoalType.LOSE:
            return _(
                "To lose weight, eat approximately {} calories per day. This should help you lose about {} kg per week."
            ).format(self.target_calories_per_day, abs(self.weekly_weight_change_goal))
        elif self.goal_type == GoalType.GAIN:
            return _(
                "To gain weight, eat approximately {} calories per day. This should help you gain about {} kg per week."
            ).format(self.target_calories_per_day, self.weekly_weight_change_goal)

        return _("Work towards your goal of {} calories per day.").format(
            self.target_calories_per_day
        )

    def save(self, *args, **kwargs):
        """Override save to handle goal achievements"""

        if not self.is_achieved and self.current_weight_kg:
            weight_diff = abs(self.weight_difference)
            if weight_diff <= Decimal("0.5"):
                self.is_achieved = True
                self.achieved_date = date.today()

        super().save(*args, **kwargs)

    def clean(self):
        """Validate the model data"""
        from django.core.exceptions import ValidationError

        if self.current_weight_kg <= 0:
            raise ValidationError(
                {"current_weight_kg": _("Current weight must be positive.")}
            )

        if self.target_weight_kg <= 0:
            raise ValidationError(
                {"target_weight_kg": _("Target weight must be positive.")}
            )

        if self.target_date and self.target_date <= date.today():
            raise ValidationError(
                {"target_date": _("Target date must be in the future.")}
            )

        if self.target_calories_per_day < Decimal("500"):
            raise ValidationError(
                {
                    "target_calories_per_day": _(
                        "Target calories seem too low for health."
                    )
                }
            )

        if self.target_calories_per_day > Decimal("9999"):
            raise ValidationError(
                {
                    "target_calories_per_day": _(
                        "Target calories seem unreasonably high."
                    )
                }
            )
