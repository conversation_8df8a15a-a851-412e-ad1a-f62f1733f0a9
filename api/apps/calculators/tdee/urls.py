"""
TDEE Calculator URLs

This module defines URL patterns for the TDEE calculator app,
following Django URL configuration best practices.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import (
    calculate_tdee,
    TDEECalculationListCreateView,
    TDEECalculationRetrieveUpdateDestroyView,
    TDEEGoalListCreateView,
    TDEEGoalRetrieveUpdateDestroyView,
    TDEEHistoryView,
    TDEEStatsView,
    TDEEInfoView,
    set_active_tdee_calculation,
    mark_tdee_goal_achieved,
)

app_name = 'apps_calculators_tdee'

urlpatterns = [
    
    path('calculate/', calculate_tdee, name='calculate'),
    
    
    path('info/', TDEEInfoView, name='info'),
    
    
    path('calculations/', TDEECalculationListCreateView.as_view(), name='calculation-list-create'),
    path('calculations/<uuid:id>/', TDEECalculationRetrieveUpdateDestroyView.as_view(), name='calculation-detail'),
    path('calculations/<uuid:calculation_id>/set-active/', set_active_tdee_calculation, name='set-active-calculation'),
    
    
    path('goals/', TDEEGoalListCreateView.as_view(), name='goal-list-create'),
    path('goals/<uuid:id>/', TDEEGoalRetrieveUpdateDestroyView.as_view(), name='goal-detail'),
    path('goals/<uuid:goal_id>/mark-achieved/', mark_tdee_goal_achieved, name='mark-goal-achieved'),
    
    
    path('history/', TDEEHistoryView.as_view(), name='history'),
    path('stats/', TDEEStatsView, name='stats'),
] 