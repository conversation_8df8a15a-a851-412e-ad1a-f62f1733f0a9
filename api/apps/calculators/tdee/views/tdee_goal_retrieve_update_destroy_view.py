"""
TDEE Goal Retrieve Update Destroy View

This module contains the class-based view for retrieving, updating, and deleting TDEE goals,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions

from ..models import TDEEGoalModel
from ..serializers import TDEEGoalSerializer


class TDEEGoalRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific TDEE goal for authenticated users.
    
    GET: Returns the specific TDEE goal
    PUT/PATCH: Updates the TDEE goal
    DELETE: Deletes the TDEE goal
    """
    
    serializer_class = TDEEGoalSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Return TDEE goals for the authenticated user only"""
        return TDEEGoalModel.objects.filter(user=self.request.user) 