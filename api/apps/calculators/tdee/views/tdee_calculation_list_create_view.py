"""
TDEE Calculation List Create View

This module contains the class-based view for listing and creating TDEE calculations,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter

from ..models import TDEECalculationModel
from ..serializers.tdee_calculation_serializer import TDEECalculationSerializer


class TDEECalculationListCreateView(generics.ListCreateAPIView):
    """
    List and create TDEE calculations for authenticated users.
    
    GET: Returns a list of user's TDEE calculations
    POST: Creates a new TDEE calculation for the authenticated user
    """
    
    serializer_class = TDEECalculationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['activity_level', 'gender', 'is_active']
    ordering_fields = ['created', 'tdee_value', 'bmr_value']
    ordering = ['-created']
    
    def get_queryset(self):
        """Return TDEE calculations for the authenticated user only"""
        return TDEECalculationModel.objects.filter(user=self.request.user) 