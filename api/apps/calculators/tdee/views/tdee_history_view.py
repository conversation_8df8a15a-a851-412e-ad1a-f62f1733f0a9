"""
TDEE History View

This module contains the view for TDEE calculation history,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import Ordering<PERSON>ilter
from django.utils import timezone
from datetime import timedelta

from ..models import TDEECalculationModel
from ..serializers.tdee_history_serializer import TDEEHistorySerializer


class TDEEHistoryView(generics.ListAPIView):
    """
    List TDEE calculation history for authenticated users.
    
    This view provides historical TDEE calculation data for analytics,
    charts, and trend analysis. It supports filtering by date ranges
    and activity levels.
    """
    
    serializer_class = TDEEHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['activity_level', 'gender']
    ordering_fields = ['created', 'tdee_value', 'bmr_value']
    ordering = ['-created']
    
    def get_queryset(self):
        """
        Return TDEE calculation history for the authenticated user.
        
        Supports query parameters:
        - days: Number of days to look back (default: 90)
        - limit: Maximum number of records to return (handled after filtering and ordering)
        """
        queryset = TDEECalculationModel.objects.filter(user=self.request.user)
        
        
        days = self.request.query_params.get('days')
        if days:
            try:
                days = int(days)
                start_date = timezone.now() - timedelta(days=days)
                queryset = queryset.filter(created__gte=start_date)
            except (ValueError, TypeError):
                pass  
        
        return queryset 
    
    def filter_queryset(self, queryset):
        """
        Apply filters and then limit the results.
        This ensures ordering is applied before limiting.
        """
        
        queryset = super().filter_queryset(queryset)
        
        
        limit = self.request.query_params.get('limit')
        if limit:
            try:
                limit = int(limit)
                if limit > 0:
                    queryset = queryset[:limit]
            except (ValueError, TypeError):
                pass  
                
        return queryset 