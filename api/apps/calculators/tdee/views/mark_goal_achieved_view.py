"""
Mark TDEE Goal Achieved View

This module contains the view for marking TDEE goals as achieved,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from datetime import date

from ..models import TDEEGoalModel
from ..serializers import TDEEGoalSerializer


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_tdee_goal_achieved(request, goal_id):
    """
    Mark a specific TDEE goal as achieved for the authenticated user.
    
    This endpoint allows users to manually mark one of their TDEE goals
    as achieved, setting the achieved date to today.
    
    Args:
        goal_id: UUID of the goal to mark as achieved
    """
    user = request.user
    
    # Get the goal to mark as achieved
    goal = get_object_or_404(
        TDEEGoalModel,
        id=goal_id,
        user=user
    )
    
    # Check if already achieved
    if goal.is_achieved:
        return Response(
            {'message': 'Goal is already marked as achieved'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Mark as achieved
    goal.is_achieved = True
    goal.achieved_date = date.today()
    goal.save()
    
    # Return the updated goal
    serializer = TDEEGoalSerializer(goal)
    return Response(
        {
            'message': 'TDEE goal marked as achieved successfully',
            'goal': serializer.data
        },
        status=status.HTTP_200_OK
    ) 