"""
TDEE Goal List Create View

This module contains the class-based view for listing and creating TDEE goals,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter

from ..models import TDEEGoalModel
from ..serializers import TDEEGoalSerializer


class TDEEGoalListCreateView(generics.ListCreateAPIView):
    """
    List and create TDEE goals for authenticated users.
    
    GET: Returns a list of user's TDEE goals
    POST: Creates a new TDEE goal for the authenticated user
    """
    
    serializer_class = TDEEGoalSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['goal_type', 'is_achieved']
    ordering_fields = ['created', 'target_date', 'target_calories_per_day']
    ordering = ['-created']
    
    def get_queryset(self):
        """Return TDEE goals for the authenticated user only"""
        return TDEEGoalModel.objects.filter(user=self.request.user) 