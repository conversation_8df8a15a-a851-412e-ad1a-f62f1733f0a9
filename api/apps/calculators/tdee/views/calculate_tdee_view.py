"""
Calculate TDEE View

This module contains the function-based view for anonymous TDEE calculation,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response

from ..models import TDEECalculationModel
from ..serializers.tdee_calculation_create_serializer import TDEECalculationCreateSerializer


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def calculate_tdee(request):
    """
    Calculate TDEE without saving to database.
    
    This endpoint can be used by anonymous users to calculate TDEE
    without requiring authentication. It's useful for the public
    TDEE calculator functionality.
    
    Expected payload:
    {
        "weight_kg": 70.0,
        "height_cm": 175.0,
        "age_years": 30,
        "gender": "male",
        "activity_level": "moderate"
    }
    """
    serializer = TDEECalculationCreateSerializer(data=request.data)
    
    if serializer.is_valid():
        # Create temporary instance for calculation
        temp_instance = TDEECalculationModel(
            weight_kg=serializer.validated_data['weight_kg'],
            height_cm=serializer.validated_data['height_cm'],
            age_years=serializer.validated_data['age_years'],
            gender=serializer.validated_data['gender'],
            activity_level=serializer.validated_data['activity_level']
        )
        
        # Return calculated data
        return Response(
            serializer.to_representation(temp_instance),
            status=status.HTTP_200_OK
        )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST) 