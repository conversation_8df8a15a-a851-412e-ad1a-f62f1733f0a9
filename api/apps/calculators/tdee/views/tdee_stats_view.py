"""
TDEE Stats View

This module contains the view for TDEE statistics and analytics,
following Django REST Framework best practices.
"""

from rest_framework import permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Avg, Min, Max, Count
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
import logging
import calendar

from ..models import TDEECalculationModel, TDEEGoalModel, ActivityLevel
from ..serializers import TDEEStatsSerializer

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def TDEEStatsView(request):
    """
    Get comprehensive TDEE statistics for the authenticated user.
    
    This endpoint provides detailed analytics about the user's TDEE
    calculations, trends, and goal progress.
    """
    try:
        user = request.user
        
        # Get all user's calculations
        calculations = TDEECalculationModel.objects.filter(user=user)
        goals = TDEEGoalModel.objects.filter(user=user)
        
        # Current calculation
        current_calculation = calculations.filter(is_active=True).first()
        
        # Date ranges - ensure timezone-aware dates
        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)
        
        # Recent calculations
        recent_calculations = calculations.filter(created__gte=thirty_days_ago)
        
        # Basic stats
        total_calculations = calculations.count()
        calculations_this_month = recent_calculations.count()
        
        # Current values
        current_tdee = current_calculation.tdee_value if current_calculation else None
        current_bmr = current_calculation.bmr_value if current_calculation else None
        current_activity_level = current_calculation.activity_level if current_calculation else None
        current_activity_level_display = current_calculation.get_activity_level_display() if current_calculation else None
        
        # Averages
        avg_stats = recent_calculations.aggregate(
            avg_tdee=Avg('tdee_value'),
            avg_bmr=Avg('bmr_value')
        )
        
        # Min/Max values
        minmax_stats = calculations.aggregate(
            min_tdee=Min('tdee_value'),
            max_tdee=Max('tdee_value'),
            min_bmr=Min('bmr_value'),
            max_bmr=Max('bmr_value')
        )
        
        # Trends
        def calculate_trend(queryset, field):
            """Calculate trend based on recent vs older data"""
            try:
                if queryset.count() < 2:
                    return 'insufficient_data'
                
                recent_avg = queryset.filter(
                    created__gte=now - timedelta(days=15)
                ).aggregate(avg=Avg(field))['avg']
                
                older_avg = queryset.filter(
                    created__lt=now - timedelta(days=15),
                    created__gte=now - timedelta(days=30)
                ).aggregate(avg=Avg(field))['avg']
                
                if not recent_avg or not older_avg:
                    return 'insufficient_data'
                
                # Ensure both values are Decimal for safe comparison
                recent_avg = Decimal(str(recent_avg))
                older_avg = Decimal(str(older_avg))
                
                diff = recent_avg - older_avg
                if abs(diff) < Decimal('50'):  # Less than 50 calories difference
                    return 'stable'
                elif diff > 0:
                    return 'increasing'
                else:
                    return 'decreasing'
            except Exception as e:
                logger.warning(f"Error calculating trend for {field}: {e}")
                return 'insufficient_data'
        
        tdee_trend = calculate_trend(recent_calculations, 'tdee_value')
        weight_trend = calculate_trend(recent_calculations, 'weight_kg')
        
        # Activity level distribution - safe access to choices
        activity_distribution = {}
        try:
            # Get activity level choices directly from the TextChoices class
            for level_value, level_display in ActivityLevel.choices:
                count = calculations.filter(activity_level=level_value).count()
                activity_distribution[level_display] = count
        except Exception as e:
            logger.warning(f"Error building activity distribution: {e}")
            # Fallback: build distribution from existing data
            for calc in calculations.values('activity_level').distinct():
                if calc['activity_level']:
                    count = calculations.filter(activity_level=calc['activity_level']).count()
                    activity_distribution[calc['activity_level']] = count
        
        # Goal stats
        active_goals = goals.filter(is_achieved=False)
        achieved_goals = goals.filter(is_achieved=True)
        
        # Weekly averages for trend analysis
        weekly_averages = []
        try:
            for i in range(12):  # Last 12 weeks
                week_start = now - timedelta(weeks=i+1)
                week_end = now - timedelta(weeks=i)
                
                week_data = calculations.filter(
                    created__gte=week_start,
                    created__lt=week_end
                ).aggregate(
                    avg_tdee=Avg('tdee_value'),
                    avg_bmr=Avg('bmr_value'),
                    avg_weight=Avg('weight_kg'),
                    count=Count('id')
                )
                
                if week_data['count'] > 0:
                    weekly_averages.append({
                        'week_start': week_start.date().isoformat(),
                        'week_end': week_end.date().isoformat(),
                        'avg_tdee': float(week_data['avg_tdee']) if week_data['avg_tdee'] else None,
                        'avg_bmr': float(week_data['avg_bmr']) if week_data['avg_bmr'] else None,
                        'avg_weight': float(week_data['avg_weight']) if week_data['avg_weight'] else None,
                        'count': week_data['count']
                    })
        except Exception as e:
            logger.warning(f"Error calculating weekly averages: {e}")
            weekly_averages = []
        
        # Monthly summary - Fixed calculation logic
        monthly_summary = {}
        try:
            for i in range(6):  # Last 6 months
                # Calculate the start of the month i months ago
                target_date = now.replace(day=1) - timedelta(days=1)  # Go to previous month first
                for _ in range(i):
                    # Go back one more month for each iteration
                    target_date = target_date.replace(day=1) - timedelta(days=1)
                
                # Now target_date is in the month we want
                month_start = target_date.replace(day=1)
                
                # Calculate the last day of this month
                last_day = calendar.monthrange(target_date.year, target_date.month)[1]
                month_end = target_date.replace(day=last_day, hour=23, minute=59, second=59)
                
                month_data = calculations.filter(
                    created__gte=month_start,
                    created__lte=month_end
                ).aggregate(
                    avg_tdee=Avg('tdee_value'),
                    avg_bmr=Avg('bmr_value'),
                    count=Count('id')
                )
                
                month_key = month_start.strftime('%Y-%m')
                monthly_summary[month_key] = {
                    'avg_tdee': float(month_data['avg_tdee']) if month_data['avg_tdee'] else None,
                    'avg_bmr': float(month_data['avg_bmr']) if month_data['avg_bmr'] else None,
                    'count': month_data['count']
                }
        except Exception as e:
            logger.warning(f"Error calculating monthly summary: {e}")
            monthly_summary = {}
        
        # Prepare response data
        stats_data = {
            'current_tdee': current_tdee,
            'current_bmr': current_bmr,
            'current_activity_level': current_activity_level,
            'current_activity_level_display': current_activity_level_display,
            'total_calculations': total_calculations,
            'calculations_this_month': calculations_this_month,
            'tdee_trend': tdee_trend,
            'weight_trend': weight_trend,
            'avg_tdee_last_30_days': avg_stats['avg_tdee'],
            'avg_bmr_last_30_days': avg_stats['avg_bmr'],
            'min_tdee': minmax_stats['min_tdee'],
            'max_tdee': minmax_stats['max_tdee'],
            'min_bmr': minmax_stats['min_bmr'],
            'max_bmr': minmax_stats['max_bmr'],
            'latest_calculation_date': current_calculation.created if current_calculation else None,
            'has_active_goals': active_goals.exists(),
            'active_goals_count': active_goals.count(),
            'achieved_goals_count': achieved_goals.count(),
            'activity_level_distribution': activity_distribution,
            'weekly_averages': weekly_averages,
            'monthly_summary': monthly_summary,
        }
        
        serializer = TDEEStatsSerializer(data=stats_data)
        if serializer.is_valid():
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            logger.error(f"Serializer validation failed: {serializer.errors}")
            return Response(
                {'error': 'Data validation failed', 'details': serializer.errors},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    except Exception as e:
        logger.error(f"Error in TDEEStatsView: {e}", exc_info=True)
        return Response(
            {'error': 'Internal server error occurred while calculating statistics'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        ) 