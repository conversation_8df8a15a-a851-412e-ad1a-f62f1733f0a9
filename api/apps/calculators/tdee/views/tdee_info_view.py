"""
TDEE Info View

This module contains the view for TDEE calculator information,
following Django REST Framework best practices.
"""

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import ActivityLevel, Gender, TDEECalculationModel
from ..serializers import TDEECalculatorInfoSerializer


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def TDEEInfoView(request):
    """
    Get general information about the TDEE calculator.
    
    This endpoint provides metadata about the TDEE calculator including
    activity levels, formulas, recommendations, and usage guidelines.
    """
    
    # Activity levels with factors
    activity_levels = []
    for level in ActivityLevel.choices:
        factor = TDEECalculationModel.get_activity_factor(level[0])
        activity_levels.append({
            'value': level[0],
            'label': level[1],
            'factor': factor,
            'description': _get_activity_description(level[0])
        })
    
    # Gender options
    gender_options = []
    for gender in Gender.choices:
        gender_options.append({
            'value': gender[0],
            'label': gender[1]
        })
    
    # BMR formula information
    bmr_formula = {
        'name': 'Mifflin-St Jeor Equation',
        'description': str(_('The most accurate BMR calculation method for most people')),
        'formulas': {
            'male': 'BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) + 5',
            'female': 'BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) - 161'
        },
        'tdee_calculation': 'TDEE = BMR × Activity Factor'
    }
    
    # Activity factors
    activity_factors = {}
    for level in ActivityLevel.choices:
        factor = TDEECalculationModel.get_activity_factor(level[0])
        activity_factors[level[0]] = {
            'factor': factor,
            'label': level[1]
        }
    
    # Units information
    units = {
        'weight': {
            'primary': 'kg',
            'label': 'Kilograms',
            'conversion': 'To convert from pounds: kg = lbs ÷ 2.205'
        },
        'height': {
            'primary': 'cm',
            'label': 'Centimeters',
            'conversion': 'To convert from feet/inches: cm = (feet × 12 + inches) × 2.54'
        },
        'age': {
            'primary': 'years',
            'label': 'Years'
        },
        'calories': {
            'primary': 'kcal/day',
            'label': 'Kilocalories per day'
        }
    }
    
    # Recommendations
    recommendations = {
        'weight_loss': {
            'title': str(_('For Weight Loss')),
            'description': str(_('Eat 300-500 calories below your TDEE for gradual weight loss (0.25-0.5 kg per week)')),
            'safe_deficit': 500,
            'max_deficit': 1000,
            'warning': str(_('Do not go below 1200 calories/day for women or 1500 calories/day for men'))
        },
        'weight_gain': {
            'title': str(_('For Weight Gain')),
            'description': str(_('Eat 300-500 calories above your TDEE for gradual weight gain (0.25-0.5 kg per week)')),
            'safe_surplus': 500,
            'max_surplus': 1000
        },
        'maintenance': {
            'title': str(_('For Weight Maintenance')),
            'description': str(_('Eat approximately your TDEE to maintain current weight'))
        },
        'accuracy': {
            'title': str(_('Improving Accuracy')),
            'tips': [
                str(_('Track your weight and calories for 2-3 weeks')),
                str(_('Adjust based on actual results')),
                str(_('Consider body composition changes')),
                str(_('Account for metabolic adaptation'))
            ]
        }
    }
    
    # Limitations
    limitations = [
        str(_('TDEE calculations are estimates and individual results may vary')),
        str(_('Does not account for metabolic disorders or medications')),
        str(_('May be less accurate for very muscular or obese individuals')),
        str(_('Activity levels are subjective and may need adjustment')),
        str(_('Does not consider body composition (muscle vs fat ratio)')),
        str(_('Metabolic rate can change over time with diet and exercise'))
    ]
    
    # Prepare response data
    info_data = {
        'activity_levels': activity_levels,
        'gender_options': gender_options,
        'bmr_formula': bmr_formula,
        'activity_factors': activity_factors,
        'units': units,
        'recommendations': recommendations,
        'limitations': limitations
    }
    
    serializer = TDEECalculatorInfoSerializer(data=info_data)
    serializer.is_valid(raise_exception=True)
    
    return Response(serializer.data)


def _get_activity_description(activity_level):
    """Get detailed description for activity level"""
    descriptions = {
        'sedentary': str(_('Spend most of the day sitting (e.g. desk job, bank teller)')),
        'light': str(_('Light exercise/sports 1-3 days/week (e.g. teacher, salesperson)')),
        'moderate': str(_('Moderate exercise/sports 3-5 days/week (e.g. food server, postal carrier)')),
        'high': str(_('Hard exercise/sports 6-7 days a week (e.g. bike messenger, carpenter)')),
        'extreme': str(_('Very hard exercise, physical job or training twice a day (e.g. athlete, construction worker)'))
    }
    return descriptions.get(activity_level, '') 