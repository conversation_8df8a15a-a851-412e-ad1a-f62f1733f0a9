"""
Set Active TDEE Calculation View

This module contains the view for setting an active TDEE calculation,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from ..models import TDEECalculationModel
from ..serializers import TDEECalculationSerializer


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def set_active_tdee_calculation(request, calculation_id):
    """
    Set a specific TDEE calculation as active for the authenticated user.
    
    This endpoint allows users to set one of their existing TDEE calculations
    as the active/current one. Only one calculation can be active at a time.
    
    Args:
        calculation_id: UUID of the calculation to set as active
    """
    user = request.user
    
    # Get the calculation to set as active
    calculation = get_object_or_404(
        TDEECalculationModel,
        id=calculation_id,
        user=user
    )
    
    # Deactivate all other calculations for this user
    TDEECalculationModel.objects.filter(
        user=user,
        is_active=True
    ).update(is_active=False)
    
    # Set this calculation as active
    calculation.is_active = True
    calculation.save()
    
    # Return the updated calculation
    serializer = TDEECalculationSerializer(calculation)
    return Response(
        {
            'message': 'TDEE calculation set as active successfully',
            'calculation': serializer.data
        },
        status=status.HTTP_200_OK
    ) 