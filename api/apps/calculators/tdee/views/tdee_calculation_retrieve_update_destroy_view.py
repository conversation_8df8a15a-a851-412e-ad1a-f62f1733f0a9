"""
TDEE Calculation Retrieve Update Destroy View

This module contains the class-based view for retrieving, updating, and deleting TDEE calculations,
following Django REST Framework best practices.
"""

from rest_framework import generics, permissions

from ..models import TDEECalculationModel
from ..serializers import TDEECalculationSerializer


class TDEECalculationRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific TDEE calculation for authenticated users.
    
    GET: Returns the specific TDEE calculation
    PUT/PATCH: Updates the TDEE calculation
    DELETE: Deletes the TDEE calculation
    """
    
    serializer_class = TDEECalculationSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def get_queryset(self):
        """Return TDEE calculations for the authenticated user only"""
        return TDEECalculationModel.objects.filter(user=self.request.user) 