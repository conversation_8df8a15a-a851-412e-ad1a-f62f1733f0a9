import json
from datetime import datetime, timedelta
from decimal import Decimal
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch, MagicMock

from apps.calculators.tdee.models import TDEECalculationModel, TDEEGoalModel
from apps.calculators.tdee.serializers import (
    TDEECalculationSerializer,
    TDEEGoalSerializer,
)

User = get_user_model()


class TDEECalculationRetrieveUpdateDestroyViewTest(TestCase):
    """Tests for TDEECalculationRetrieveUpdateDestroyView."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.other_user = User.objects.create_user(
            username="otheruser", email="<EMAIL>", password="otherpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        self.calculation = TDEECalculationModel.objects.create(
            user=self.user,
            age=30,
            gender=TDEECalculationModel.Gender.MALE,
            weight_kg=Decimal("70.0"),
            height_cm=Decimal("175.0"),
            activity_level=TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
            bmr=Decimal("1650.0"),
            tdee=Decimal("2558.0"),
            goal_type=TDEECalculationModel.GoalType.MAINTAIN_WEIGHT,
        )

        
        self.other_calculation = TDEECalculationModel.objects.create(
            user=self.other_user,
            age=25,
            gender=TDEECalculationModel.Gender.FEMALE,
            weight_kg=Decimal("60.0"),
            height_cm=Decimal("165.0"),
            activity_level=TDEECalculationModel.ActivityLevel.LIGHTLY_ACTIVE,
            bmr=Decimal("1400.0"),
            tdee=Decimal("1932.0"),
            goal_type=TDEECalculationModel.GoalType.LOSE_WEIGHT,
        )

    def test_retrieve_calculation(self):
        """Test retrieving a TDEE calculation."""
        url = reverse(
            "calculators:tdee:calculation-detail", kwargs={"id": self.calculation.id}
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        
        self.assertEqual(data["id"], str(self.calculation.id))
        self.assertEqual(data["age"], self.calculation.age)
        self.assertEqual(data["gender"], self.calculation.gender)
        self.assertEqual(float(data["weight_kg"]), float(self.calculation.weight_kg))
        self.assertEqual(float(data["tdee"]), float(self.calculation.tdee))

    def test_retrieve_nonexistent_calculation(self):
        """Test retrieving a nonexistent calculation."""
        url = reverse("calculators:tdee:calculation-detail", kwargs={"id": 99999})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_retrieve_other_user_calculation(self):
        """Test that users cannot retrieve other users' calculations."""
        url = reverse(
            "calculators:tdee:calculation-detail",
            kwargs={"id": self.other_calculation.id},
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_calculation(self):
        """Test updating a TDEE calculation."""
        url = reverse(
            "calculators:tdee:calculation-detail", kwargs={"id": self.calculation.id}
        )
        data = {
            "age": 31,
            "weight_kg": "72.0",
            "activity_level": TDEECalculationModel.ActivityLevel.VERY_ACTIVE,
            "notes": "Updated calculation",
        }

        response = self.client.patch(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        
        self.calculation.refresh_from_db()
        self.assertEqual(self.calculation.age, 31)
        self.assertEqual(float(self.calculation.weight_kg), 72.0)
        self.assertEqual(
            self.calculation.activity_level,
            TDEECalculationModel.ActivityLevel.VERY_ACTIVE,
        )

    def test_update_calculation_recalculates_values(self):
        """Test that updating calculation triggers recalculation."""
        url = reverse(
            "calculators:tdee:calculation-detail", kwargs={"id": self.calculation.id}
        )
        original_tdee = self.calculation.tdee

        data = {
            "weight_kg": "80.0",  
            "activity_level": TDEECalculationModel.ActivityLevel.VERY_ACTIVE,
        }

        response = self.client.patch(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        
        self.calculation.refresh_from_db()
        self.assertNotEqual(self.calculation.tdee, original_tdee)
        self.assertGreater(
            self.calculation.tdee, original_tdee
        )  

    def test_delete_calculation(self):
        """Test deleting a TDEE calculation."""
        url = reverse(
            "calculators:tdee:calculation-detail", kwargs={"id": self.calculation.id}
        )
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        
        with self.assertRaises(TDEECalculationModel.DoesNotExist):
            TDEECalculationModel.objects.get(id=self.calculation.id)

    def test_delete_other_user_calculation(self):
        """Test that users cannot delete other users' calculations."""
        url = reverse(
            "calculators:tdee:calculation-detail",
            kwargs={"id": self.other_calculation.id},
        )
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        
        self.assertTrue(
            TDEECalculationModel.objects.filter(id=self.other_calculation.id).exists()
        )

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access calculations."""
        self.client.force_authenticate(user=None)

        url = reverse(
            "calculators:tdee:calculation-detail", kwargs={"id": self.calculation.id}
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class TDEECalculationListCreateViewTest(TestCase):
    """Tests for TDEECalculationListCreateView."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        self.calculations = [
            TDEECalculationModel.objects.create(
                user=self.user,
                age=30,
                gender=TDEECalculationModel.Gender.MALE,
                weight_kg=Decimal("70.0"),
                height_cm=Decimal("175.0"),
                activity_level=TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
                bmr=Decimal("1650.0"),
                tdee=Decimal("2558.0"),
                goal_type=TDEECalculationModel.GoalType.MAINTAIN_WEIGHT,
                is_active=True,
            ),
            TDEECalculationModel.objects.create(
                user=self.user,
                age=30,
                gender=TDEECalculationModel.Gender.MALE,
                weight_kg=Decimal("68.0"),
                height_cm=Decimal("175.0"),
                activity_level=TDEECalculationModel.ActivityLevel.LIGHTLY_ACTIVE,
                bmr=Decimal("1620.0"),
                tdee=Decimal("2239.0"),
                goal_type=TDEECalculationModel.GoalType.LOSE_WEIGHT,
                created=timezone.now() - timedelta(days=1),
            ),
        ]

    def test_list_calculations(self):
        """Test listing TDEE calculations."""
        url = reverse("calculators:tdee:calculation-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 2)

        
        calc_data = results[0]
        self.assertIn("id", calc_data)
        self.assertIn("tdee", calc_data)
        self.assertIn("bmr", calc_data)
        self.assertIn("goal_type", calc_data)

    def test_list_calculations_ordering(self):
        """Test that calculations are ordered by creation date (newest first)."""
        url = reverse("calculators:tdee:calculation-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertTrue(results[0]["is_active"])

    def test_create_calculation(self):
        """Test creating a new TDEE calculation."""
        url = reverse("calculators:tdee:calculation-list")
        data = {
            "age": 25,
            "gender": TDEECalculationModel.Gender.FEMALE,
            "weight_kg": "60.0",
            "height_cm": "165.0",
            "activity_level": TDEECalculationModel.ActivityLevel.VERY_ACTIVE,
            "goal_type": TDEECalculationModel.GoalType.GAIN_WEIGHT,
            "notes": "New calculation for bulking",
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        
        calc = TDEECalculationModel.objects.get(id=response.data["id"])
        self.assertEqual(calc.user, self.user)
        self.assertEqual(calc.age, 25)
        self.assertEqual(calc.gender, TDEECalculationModel.Gender.FEMALE)
        self.assertEqual(float(calc.weight_kg), 60.0)

        
        self.assertGreater(calc.bmr, 0)
        self.assertGreater(calc.tdee, calc.bmr)

    def test_create_calculation_sets_as_active(self):
        """Test that creating a new calculation sets it as active and deactivates others."""
        url = reverse("calculators:tdee:calculation-list")
        data = {
            "age": 32,
            "gender": TDEECalculationModel.Gender.MALE,
            "weight_kg": "75.0",
            "height_cm": "180.0",
            "activity_level": TDEECalculationModel.ActivityLevel.SEDENTARY,
            "goal_type": TDEECalculationModel.GoalType.LOSE_WEIGHT,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        
        new_calc = TDEECalculationModel.objects.get(id=response.data["id"])
        self.assertTrue(new_calc.is_active)

        
        self.calculations[0].refresh_from_db()
        self.assertFalse(self.calculations[0].is_active)

    def test_create_calculation_invalid_data(self):
        """Test creating calculation with invalid data."""
        url = reverse("calculators:tdee:calculation-list")
        data = {
            "age": -5,  
            "gender": "invalid_gender",
            "weight_kg": "not_a_number",
            "height_cm": "165.0",
            "activity_level": TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_list_calculations_user_isolation(self):
        """Test that users only see their own calculations."""
        
        other_user = User.objects.create_user(
            username="otheruser", email="<EMAIL>", password="otherpass123"
        )

        TDEECalculationModel.objects.create(
            user=other_user,
            age=28,
            gender=TDEECalculationModel.Gender.FEMALE,
            weight_kg=Decimal("55.0"),
            height_cm=Decimal("160.0"),
            activity_level=TDEECalculationModel.ActivityLevel.LIGHTLY_ACTIVE,
            bmr=Decimal("1300.0"),
            tdee=Decimal("1795.0"),
            goal_type=TDEECalculationModel.GoalType.MAINTAIN_WEIGHT,
        )

        url = reverse("calculators:tdee:calculation-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        
        self.assertEqual(len(results), 2)
        for result in results:
            self.assertEqual(result["user"], str(self.user.id))

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access calculations."""
        self.client.force_authenticate(user=None)

        url = reverse("calculators:tdee:calculation-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class TDEEGoalViewTest(TestCase):
    """Tests for TDEE Goal views."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        self.calculation = TDEECalculationModel.objects.create(
            user=self.user,
            age=30,
            gender=TDEECalculationModel.Gender.MALE,
            weight_kg=Decimal("70.0"),
            height_cm=Decimal("175.0"),
            activity_level=TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
            bmr=Decimal("1650.0"),
            tdee=Decimal("2558.0"),
            goal_type=TDEECalculationModel.GoalType.LOSE_WEIGHT,
        )

        
        self.goal = TDEEGoalModel.objects.create(
            user=self.user,
            calculation=self.calculation,
            goal_type=TDEEGoalModel.GoalType.WEIGHT_LOSS,
            target_weight=Decimal("65.0"),
            current_weight=Decimal("70.0"),
            target_date=timezone.now() + timedelta(days=90),
            target_calories_per_day=2058,  
            description="Lose 5kg in 3 months",
        )

    def test_list_goals(self):
        """Test listing TDEE goals."""
        url = reverse("calculators:tdee:goal-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data.get("results", response.data)

        self.assertEqual(len(results), 1)
        goal_data = results[0]
        self.assertEqual(goal_data["goal_type"], self.goal.goal_type)
        self.assertEqual(
            float(goal_data["target_weight"]), float(self.goal.target_weight)
        )

    def test_create_goal(self):
        """Test creating a new TDEE goal."""
        url = reverse("calculators:tdee:goal-list")
        data = {
            "calculation": str(self.calculation.id),
            "goal_type": TDEEGoalModel.GoalType.MUSCLE_GAIN,
            "target_weight": "75.0",
            "current_weight": "70.0",
            "target_date": (timezone.now() + timedelta(days=120)).date().isoformat(),
            "target_calories_per_day": 2800,
            "description": "Gain muscle mass",
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        
        goal = TDEEGoalModel.objects.get(id=response.data["id"])
        self.assertEqual(goal.user, self.user)
        self.assertEqual(goal.goal_type, TDEEGoalModel.GoalType.MUSCLE_GAIN)

    def test_retrieve_goal(self):
        """Test retrieving a specific goal."""
        url = reverse("calculators:tdee:goal-detail", kwargs={"id": self.goal.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        self.assertEqual(data["id"], str(self.goal.id))
        self.assertEqual(data["goal_type"], self.goal.goal_type)
        self.assertEqual(float(data["target_weight"]), float(self.goal.target_weight))

    def test_update_goal_progress(self):
        """Test updating goal with current progress."""
        url = reverse("calculators:tdee:goal-detail", kwargs={"id": self.goal.id})
        data = {"current_weight": "68.0", "notes": "Good progress this week"}

        response = self.client.patch(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.goal.refresh_from_db()
        self.assertEqual(float(self.goal.current_weight), 68.0)

    def test_mark_goal_achieved(self):
        """Test marking a goal as achieved."""
        url = reverse("calculators:tdee:goal-achieve", kwargs={"id": self.goal.id})
        data = {
            "final_weight": "65.0",
            "achievement_notes": "Goal achieved ahead of schedule!",
        }

        response = self.client.patch(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.goal.refresh_from_db()
        self.assertTrue(self.goal.is_achieved)
        self.assertIsNotNone(self.goal.achieved_date)


class TDEEStatsViewTest(TestCase):
    """Tests for TDEE Stats view."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

        
        base_date = timezone.now() - timedelta(days=30)
        for i in range(5):
            TDEECalculationModel.objects.create(
                user=self.user,
                age=30,
                gender=TDEECalculationModel.Gender.MALE,
                weight_kg=Decimal(f"{70 - i}"),  
                height_cm=Decimal("175.0"),
                activity_level=TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
                bmr=Decimal(f"{1650 - i*20}"),
                tdee=Decimal(f"{2558 - i*30}"),
                goal_type=TDEECalculationModel.GoalType.LOSE_WEIGHT,
                created=base_date + timedelta(days=i * 7),
            )

    def test_get_stats(self):
        """Test getting TDEE statistics."""
        url = reverse("calculators:tdee:stats")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        
        self.assertIn("total_calculations", data)
        self.assertIn("weight_trend", data)
        self.assertIn("tdee_trend", data)
        self.assertIn("current_stats", data)
        self.assertIn("goal_progress", data)

        
        self.assertEqual(data["total_calculations"], 5)
        self.assertIn("direction", data["weight_trend"])
        self.assertIn("change_kg", data["weight_trend"])

    def test_get_stats_custom_period(self):
        """Test getting stats for custom time period."""
        url = reverse("calculators:tdee:stats")
        response = self.client.get(url, {"days": 14})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        
        self.assertEqual(data["period_days"], 14)

    def test_get_stats_no_data(self):
        """Test stats with no calculation data."""
        
        new_user = User.objects.create_user(
            username="newuser", email="<EMAIL>", password="newpass123"
        )
        self.client.force_authenticate(user=new_user)

        url = reverse("calculators:tdee:stats")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        
        self.assertEqual(data["total_calculations"], 0)
        self.assertIn("message", data)


class TDEEInfoViewTest(TestCase):
    """Tests for TDEE Info view."""

    def setUp(self):
        self.client = APIClient()
        

    def test_get_tdee_info(self):
        """Test getting TDEE calculator information."""
        url = reverse("calculators:tdee:info")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data

        
        self.assertIn("calculator_info", data)
        self.assertIn("activity_levels", data)
        self.assertIn("formulas", data)
        self.assertIn("goal_types", data)

        
        activity_levels = data["activity_levels"]
        self.assertIsInstance(activity_levels, list)
        self.assertGreater(len(activity_levels), 0)

        
        for level in activity_levels:
            self.assertIn("value", level)
            self.assertIn("label", level)
            self.assertIn("description", level)
            self.assertIn("multiplier", level)


class CalculateTDEEViewTest(TestCase):
    """Tests for anonymous TDEE calculation view."""

    def setUp(self):
        self.client = APIClient()
        

    def test_calculate_tdee_anonymous(self):
        """Test calculating TDEE without saving to database."""
        url = reverse("calculators:tdee:calculate")
        data = {
            "age": 30,
            "gender": "male",
            "weight_kg": 70.0,
            "height_cm": 175.0,
            "activity_level": "moderately_active",
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        result = response.data

        
        self.assertIn("bmr", result)
        self.assertIn("tdee", result)
        self.assertIn("activity_level", result)
        self.assertIn("recommendations", result)

        
        self.assertGreater(result["bmr"], 0)
        self.assertGreater(result["tdee"], result["bmr"])

    def test_calculate_tdee_invalid_data(self):
        """Test TDEE calculation with invalid data."""
        url = reverse("calculators:tdee:calculate")
        data = {
            "age": -5,  
            "gender": "invalid_gender",
            "weight_kg": "not_a_number",
            "height_cm": 175.0,
            "activity_level": "moderately_active",
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class TDEEPermissionsTest(TestCase):
    """Tests for permissions across TDEE views."""

    def setUp(self):
        self.client = APIClient()
        self.user1 = User.objects.create_user(
            username="user1", email="<EMAIL>", password="pass123"
        )
        self.user2 = User.objects.create_user(
            username="user2", email="<EMAIL>", password="pass123"
        )

        
        self.user1_calc = TDEECalculationModel.objects.create(
            user=self.user1,
            age=30,
            gender=TDEECalculationModel.Gender.MALE,
            weight_kg=Decimal("70.0"),
            height_cm=Decimal("175.0"),
            activity_level=TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
            bmr=Decimal("1650.0"),
            tdee=Decimal("2558.0"),
            goal_type=TDEECalculationModel.GoalType.MAINTAIN_WEIGHT,
        )

        self.user1_goal = TDEEGoalModel.objects.create(
            user=self.user1,
            calculation=self.user1_calc,
            goal_type=TDEEGoalModel.GoalType.WEIGHT_LOSS,
            target_weight=Decimal("65.0"),
            current_weight=Decimal("70.0"),
            target_date=timezone.now() + timedelta(days=90),
        )

    def test_cross_user_calculation_access_denied(self):
        """Test that users cannot access other users' calculations."""
        self.client.force_authenticate(user=self.user2)

        
        url = reverse(
            "calculators:tdee:calculation-detail", kwargs={"id": self.user1_calc.id}
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_cross_user_goal_access_denied(self):
        """Test that users cannot access other users' goals."""
        self.client.force_authenticate(user=self.user2)

        
        url = reverse("calculators:tdee:goal-detail", kwargs={"id": self.user1_goal.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_unauthenticated_calculation_access_denied(self):
        """Test that unauthenticated users cannot access saved calculations."""
        url = reverse("calculators:tdee:calculation-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class TDEEValidationTest(TestCase):
    """Tests for validation in TDEE views."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )
        self.client.force_authenticate(user=self.user)

    def test_invalid_age_validation(self):
        """Test validation for invalid age values."""
        url = reverse("calculators:tdee:calculation-list")
        data = {
            "age": 150,  
            "gender": TDEECalculationModel.Gender.MALE,
            "weight_kg": "70.0",
            "height_cm": "175.0",
            "activity_level": TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_invalid_weight_validation(self):
        """Test validation for invalid weight values."""
        url = reverse("calculators:tdee:calculation-list")
        data = {
            "age": 30,
            "gender": TDEECalculationModel.Gender.MALE,
            "weight_kg": "500.0",  
            "height_cm": "175.0",
            "activity_level": TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_goal_past_target_date(self):
        """Test validation for goals with past target dates."""
        
        calculation = TDEECalculationModel.objects.create(
            user=self.user,
            age=30,
            gender=TDEECalculationModel.Gender.MALE,
            weight_kg=Decimal("70.0"),
            height_cm=Decimal("175.0"),
            activity_level=TDEECalculationModel.ActivityLevel.MODERATELY_ACTIVE,
            bmr=Decimal("1650.0"),
            tdee=Decimal("2558.0"),
            goal_type=TDEECalculationModel.GoalType.LOSE_WEIGHT,
        )

        url = reverse("calculators:tdee:goal-list")
        past_date = (timezone.now() - timedelta(days=1)).date()

        data = {
            "calculation": str(calculation.id),
            "goal_type": TDEEGoalModel.GoalType.WEIGHT_LOSS,
            "target_weight": "65.0",
            "current_weight": "70.0",
            "target_date": past_date.isoformat(),
            "target_calories_per_day": 2000,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
