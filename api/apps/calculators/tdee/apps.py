"""
TDEE Calculator App Configuration

This module configures the TDEE calculator Django app,
following Django app configuration best practices.
"""

from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class TdeeConfig(AppConfig):
    """Configuration for the TDEE calculator app"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.calculators.tdee'
    label = 'apps_calculators_tdee'
    verbose_name = _('TDEE Calculator')
    
    def ready(self):
        """Initialize the app when Django starts"""
        
        
        pass
