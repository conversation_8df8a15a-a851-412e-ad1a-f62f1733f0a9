"""
TDEE Goal Serializer

This module contains the serializer for TDEE goals,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from ..models import TDEEGoalModel, GoalType


class TDEEGoalSerializer(serializers.ModelSerializer):
    """
    Serializer for TDEE goals with full CRUD operations.
    
    This serializer handles the complete TDEE goal model with all fields
    and automatic calculations for goal progress and estimates.
    """
    
    id = serializers.UUIDField(read_only=True)
    _id = serializers.IntegerField(source='pk', read_only=True)
    
    # Display fields
    goal_type_display = serializers.CharField(
        source='get_goal_type_display', 
        read_only=True
    )
    goal_status_message = serializers.CharField(read_only=True)
    
    # Calculated fields
    weight_difference = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        read_only=True
    )
    calories_difference_from_maintenance = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        read_only=True
    )
    estimated_weekly_weight_change = serializers.DecimalField(
        max_digits=4, 
        decimal_places=2, 
        read_only=True
    )
    estimated_time_to_goal_weeks = serializers.IntegerField(read_only=True)
    
    # Timestamps
    created = serializers.DateTimeField(read_only=True)
    updated = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = TDEEGoalModel
        fields = [
            'id',
            '_id',
            'goal_type',
            'target_calories_per_day',
            'current_weight_kg',
            'target_weight_kg',
            'target_date',
            'weekly_weight_change_goal',
            'is_achieved',
            'achieved_date',
            'notes',
            'goal_type_display',
            'goal_status_message',
            'weight_difference',
            'calories_difference_from_maintenance',
            'estimated_weekly_weight_change',
            'estimated_time_to_goal_weeks',
            'created',
            'updated',
        ]
        read_only_fields = [
            'id',
            '_id',
            'is_achieved',
            'achieved_date',
            'goal_type_display',
            'goal_status_message',
            'weight_difference',
            'calories_difference_from_maintenance',
            'estimated_weekly_weight_change',
            'estimated_time_to_goal_weeks',
            'created',
            'updated',
        ]
    
    def validate_goal_type(self, value):
        """Validate goal type is a valid choice"""
        if value not in [choice[0] for choice in GoalType.choices]:
            raise serializers.ValidationError(_("Invalid goal type choice."))
        return value
    
    def validate_target_calories_per_day(self, value):
        """Validate target calories are within reasonable range"""
        if value < Decimal('500.00'):
            raise serializers.ValidationError(_("Target calories seem too low for health (minimum 500)."))
        if value > Decimal('9999.99'):
            raise serializers.ValidationError(_("Target calories seem unreasonably high (maximum 9999)."))
        return value
    
    def validate_current_weight_kg(self, value):
        """Validate current weight is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Current weight must be positive."))
        if value > Decimal('999.99'):
            raise serializers.ValidationError(_("Current weight seems unreasonably high."))
        if value < Decimal('1.00'):
            raise serializers.ValidationError(_("Current weight seems unreasonably low."))
        return value
    
    def validate_target_weight_kg(self, value):
        """Validate target weight is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Target weight must be positive."))
        if value > Decimal('999.99'):
            raise serializers.ValidationError(_("Target weight seems unreasonably high."))
        if value < Decimal('1.00'):
            raise serializers.ValidationError(_("Target weight seems unreasonably low."))
        return value
    
    def validate_weekly_weight_change_goal(self, value):
        """Validate weekly weight change goal is reasonable"""
        if abs(value) > Decimal('2.00'):
            raise serializers.ValidationError(_("Weekly weight change goal should not exceed 2 kg per week for health reasons."))
        return value
    
    def validate_target_date(self, value):
        """Validate target date is in the future"""
        if value:
            from datetime import date
            if value <= date.today():
                raise serializers.ValidationError(_("Target date must be in the future."))
        return value
    
    def validate_notes(self, value):
        """Validate notes length"""
        if value and len(value) > 500:
            raise serializers.ValidationError(_("Notes are too long (maximum 500 characters)."))
        return value
    
    def validate(self, data):
        """Cross-field validation"""
        current_weight = data.get('current_weight_kg')
        target_weight = data.get('target_weight_kg')
        goal_type = data.get('goal_type')
        weekly_change = data.get('weekly_weight_change_goal', Decimal('0.00'))
        
        if current_weight and target_weight and goal_type:
            weight_diff = target_weight - current_weight
            
            # Validate goal type matches weight change direction
            if goal_type == GoalType.LOSE and weight_diff >= 0:
                raise serializers.ValidationError({
                    'target_weight_kg': _("For weight loss goals, target weight should be less than current weight.")
                })
            elif goal_type == GoalType.GAIN and weight_diff <= 0:
                raise serializers.ValidationError({
                    'target_weight_kg': _("For weight gain goals, target weight should be more than current weight.")
                })
            elif goal_type == GoalType.MAINTAIN and abs(weight_diff) > Decimal('2.00'):
                raise serializers.ValidationError({
                    'target_weight_kg': _("For maintenance goals, target weight should be close to current weight.")
                })
            
            # Validate weekly change goal aligns with goal type
            if goal_type == GoalType.LOSE and weekly_change > 0:
                raise serializers.ValidationError({
                    'weekly_weight_change_goal': _("For weight loss goals, weekly change should be negative or zero.")
                })
            elif goal_type == GoalType.GAIN and weekly_change < 0:
                raise serializers.ValidationError({
                    'weekly_weight_change_goal': _("For weight gain goals, weekly change should be positive or zero.")
                })
        
        return data
    
    def create(self, validated_data):
        """Create a new TDEE goal"""
        # Set the user from the request context
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Update an existing TDEE goal"""
        # Don't allow changing the user
        validated_data.pop('user', None)
        return super().update(instance, validated_data) 