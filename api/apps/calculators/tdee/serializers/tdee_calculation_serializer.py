"""
TDEE Calculation Serializer

This module contains the main serializer for TDEE calculations,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import TDEECalculationModel, ActivityLevel, Gender


class TDEECalculationSerializer(serializers.ModelSerializer):
    """
    Main serializer for TDEE calculations with full CRUD operations.
    
    This serializer handles the complete TDEE calculation model with all fields
    and automatic calculations. Used for authenticated users who can save
    their calculations to the database.
    """
    
    id = serializers.UUIDField(read_only=True)
    _id = serializers.IntegerField(source='pk', read_only=True)
    
    # Display fields
    activity_level_display = serializers.CharField(
        source='get_activity_level_display', 
        read_only=True
    )
    gender_display = serializers.CharField(
        source='get_gender_display', 
        read_only=True
    )
    tdee_status_message = serializers.CharField(read_only=True)
    activity_factor = serializers.DecimalField(
        max_digits=4, 
        decimal_places=3, 
        read_only=True
    )
    
    # Timestamps
    created = serializers.DateTimeField(read_only=True)
    updated = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = TDEECalculationModel
        fields = [
            'id',
            '_id',
            'weight_kg',
            'height_cm',
            'age_years',
            'gender',
            'activity_level',
            'bmr_value',
            'tdee_value',
            'notes',
            'is_active',
            'activity_level_display',
            'gender_display',
            'tdee_status_message',
            'activity_factor',
            'created',
            'updated',
        ]
        read_only_fields = [
            'id',
            '_id',
            'bmr_value',
            'tdee_value',
            'activity_level_display',
            'gender_display',
            'tdee_status_message',
            'activity_factor',
            'created',
            'updated',
        ]
    
    def validate_weight_kg(self, value):
        """Validate weight is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Weight must be positive."))
        if value > 999.99:
            raise serializers.ValidationError(_("Weight seems unreasonably high."))
        if value < 1.00:
            raise serializers.ValidationError(_("Weight seems unreasonably low."))
        return value
    
    def validate_height_cm(self, value):
        """Validate height is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Height must be positive."))
        if value > 300.00:
            raise serializers.ValidationError(_("Height seems unreasonably high."))
        if value < 50.00:
            raise serializers.ValidationError(_("Height seems unreasonably low."))
        return value
    
    def validate_age_years(self, value):
        """Validate age is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Age must be positive."))
        if value > 120:
            raise serializers.ValidationError(_("Age seems unreasonably high."))
        if value < 1:
            raise serializers.ValidationError(_("Age seems unreasonably low."))
        return value
    
    def validate_gender(self, value):
        """Validate gender is a valid choice"""
        if value not in [choice[0] for choice in Gender.choices]:
            raise serializers.ValidationError(_("Invalid gender choice."))
        return value
    
    def validate_activity_level(self, value):
        """Validate activity level is a valid choice"""
        if value not in [choice[0] for choice in ActivityLevel.choices]:
            raise serializers.ValidationError(_("Invalid activity level choice."))
        return value
    
    def validate_notes(self, value):
        """Validate notes length"""
        if value and len(value) > 500:
            raise serializers.ValidationError(_("Notes are too long (maximum 500 characters)."))
        return value
    
    def create(self, validated_data):
        """Create a new TDEE calculation"""
        # Set the user from the request context
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Update an existing TDEE calculation"""
        # Don't allow changing the user
        validated_data.pop('user', None)
        return super().update(instance, validated_data) 