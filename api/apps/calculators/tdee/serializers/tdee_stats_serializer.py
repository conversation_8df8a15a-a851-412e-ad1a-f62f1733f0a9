"""
TDEE Stats Serializer

This module contains the serializer for TDEE statistics and analytics,
following Django REST Framework best practices.
"""

from rest_framework import serializers


class TDEEStatsSerializer(serializers.Serializer):
    """
    Serializer for TDEE statistics and analytics data.
    
    This serializer provides comprehensive statistics about a user's
    TDEE calculations, goals, and progress over time.
    """
    
    
    current_tdee = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    current_bmr = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    current_activity_level = serializers.CharField(allow_null=True)
    current_activity_level_display = serializers.CharField(allow_null=True)
    
    
    total_calculations = serializers.IntegerField()
    calculations_this_month = serializers.IntegerField()
    
    
    tdee_trend = serializers.ChoiceField(
        choices=[
            ('increasing', 'Increasing'),
            ('decreasing', 'Decreasing'),
            ('stable', 'Stable'),
            ('insufficient_data', 'Insufficient Data')
        ]
    )
    weight_trend = serializers.ChoiceField(
        choices=[
            ('increasing', 'Increasing'),
            ('decreasing', 'Decreasing'),
            ('stable', 'Stable'),
            ('insufficient_data', 'Insufficient Data')
        ]
    )
    
    
    avg_tdee_last_30_days = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    avg_bmr_last_30_days = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    
    
    min_tdee = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    max_tdee = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    min_bmr = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    max_bmr = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        allow_null=True
    )
    
    
    latest_calculation_date = serializers.DateTimeField(allow_null=True)
    
    
    has_active_goals = serializers.BooleanField()
    active_goals_count = serializers.IntegerField()
    achieved_goals_count = serializers.IntegerField()
    
    
    activity_level_distribution = serializers.DictField(
        child=serializers.IntegerField(),
        allow_empty=True
    )
    
    
    weekly_averages = serializers.ListField(
        child=serializers.DictField(),
        allow_empty=True
    )
    
    
    monthly_summary = serializers.DictField(
        child=serializers.DictField(),
        allow_empty=True
    ) 