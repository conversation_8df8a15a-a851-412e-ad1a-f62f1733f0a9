"""
TDEE History Serializer

This module contains the serializer for TDEE calculation history,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import TDEECalculationModel, ActivityLevel, Gender


class TDEEHistorySerializer(serializers.ModelSerializer):
    """
    Simplified serializer for TDEE calculation history views.
    
    This serializer provides essential historical data about TDEE calculations
    for analytics, charts, and trend analysis.
    """
    
    id = serializers.UUIDField(read_only=True)
    _id = serializers.IntegerField(source='pk', read_only=True)
    
    # Display fields
    activity_level_display = serializers.CharField(
        source='get_activity_level_display', 
        read_only=True
    )
    gender_display = serializers.CharField(
        source='get_gender_display', 
        read_only=True
    )
    activity_factor = serializers.DecimalField(
        max_digits=4, 
        decimal_places=3, 
        read_only=True
    )
    
    # Date for charting
    calculation_date = serializers.DateTimeField(source='created', read_only=True)
    
    class Meta:
        model = TDEECalculationModel
        fields = [
            'id',
            '_id',
            'weight_kg',
            'height_cm',
            'age_years',
            'gender',
            'activity_level',
            'bmr_value',
            'tdee_value',
            'is_active',
            'activity_level_display',
            'gender_display',
            'activity_factor',
            'calculation_date',
        ]
        read_only_fields = fields  # All fields are read-only for history 