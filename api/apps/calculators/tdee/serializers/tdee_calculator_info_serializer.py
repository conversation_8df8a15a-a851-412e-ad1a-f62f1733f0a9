"""
TDEE Calculator Info Serializer

This module contains the serializer for TDEE calculator information,
following Django REST Framework best practices.
"""

from rest_framework import serializers


class TDEECalculatorInfoSerializer(serializers.Serializer):
    """
    Serializer for TDEE calculator information and metadata.
    
    This serializer provides general information about the TDEE calculator,
    including activity levels, formulas, and recommendations.
    """
    
    activity_levels = serializers.ListField(
        child=serializers.DictField()
    )
    
    gender_options = serializers.ListField(
        child=serializers.DictField()
    )
    
    bmr_formula = serializers.DictField()
    
    activity_factors = serializers.DictField()
    
    units = serializers.DictField()
    
    recommendations = serializers.DictField()
    
    limitations = serializers.ListField(
        child=serializers.CharField()
    ) 