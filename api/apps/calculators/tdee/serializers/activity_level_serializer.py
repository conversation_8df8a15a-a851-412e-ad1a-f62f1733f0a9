"""
Activity Level Serializer

This module contains the serializer for activity level information,
following Django REST Framework best practices.
"""

from rest_framework import serializers

from ..models import ActivityLevel


class ActivityLevelSerializer(serializers.Serializer):
    """
    Serializer for activity level information.
    
    This serializer provides information about activity levels
    used in TDEE calculations.
    """
    
    value = serializers.CharField()
    label = serializers.CharField()
    factor = serializers.DecimalField(max_digits=4, decimal_places=3)
    description = serializers.CharField()
    
    def to_representation(self, instance):
        """Convert activity level choice to structured data"""
        if isinstance(instance, tuple):
            value, label = instance
        else:
            value = instance
            label = dict(ActivityLevel.choices).get(value, value)
        
        from ..models import TDEECalculationModel
        factor = TDEECalculationModel.get_activity_factor(value)
        
        descriptions = {
            'sedentary': 'Spend most of the day sitting (e.g. bank teller, desk job)',
            'light': 'Spend a good part of the day on your feet (e.g. teacher, salesperson)',
            'moderate': 'Spend a good part of the day doing some physical activity (e.g. food server, postal carrier)',
            'high': 'Spend most of the day doing heavy physical activity (e.g. bike messenger, carpenter)',
            'extreme': 'Very heavy physical work or training twice a day, marathon, contest, etc.'
        }
        
        return {
            'value': value,
            'label': label,
            'factor': factor,
            'description': descriptions.get(value, '')
        } 