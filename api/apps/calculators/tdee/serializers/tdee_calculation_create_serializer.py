"""
TDEE Calculation Create Serializer

This module contains the serializer for anonymous TDEE calculations,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from ..models import TDEECalculationModel, ActivityLevel, Gender


class TDEECalculationCreateSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for creating TDEE calculations without saving to database.
    
    This is used for the anonymous TDEE calculation endpoint that doesn't require authentication.
    It calculates and returns TDEE data without persisting it to the database.
    """
    
    bmr_value = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        read_only=True
    )
    tdee_value = serializers.DecimalField(
        max_digits=7, 
        decimal_places=2, 
        read_only=True
    )
    activity_level_display = serializers.CharField(read_only=True)
    gender_display = serializers.Char<PERSON>ield(read_only=True)
    tdee_status_message = serializers.CharField(read_only=True)
    activity_factor = serializers.DecimalField(
        max_digits=4, 
        decimal_places=3, 
        read_only=True
    )
    
    class Meta:
        model = TDEECalculationModel
        fields = [
            'weight_kg',
            'height_cm',
            'age_years',
            'gender',
            'activity_level',
            'bmr_value',
            'tdee_value',
            'activity_level_display',
            'gender_display',
            'tdee_status_message',
            'activity_factor'
        ]
    
    def validate_weight_kg(self, value):
        """Validate weight is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Weight must be positive."))
        if value > Decimal('999.99'):
            raise serializers.ValidationError(_("Weight seems unreasonably high."))
        if value < Decimal('1.00'):
            raise serializers.ValidationError(_("Weight seems unreasonably low."))
        return value
    
    def validate_height_cm(self, value):
        """Validate height is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Height must be positive."))
        if value > Decimal('300.00'):
            raise serializers.ValidationError(_("Height seems unreasonably high."))
        if value < Decimal('50.00'):
            raise serializers.ValidationError(_("Height seems unreasonably low."))
        return value
    
    def validate_age_years(self, value):
        """Validate age is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Age must be positive."))
        if value > 120:
            raise serializers.ValidationError(_("Age seems unreasonably high."))
        if value < 1:
            raise serializers.ValidationError(_("Age seems unreasonably low."))
        return value
    
    def validate_gender(self, value):
        """Validate gender is a valid choice"""
        if value not in [choice[0] for choice in Gender.choices]:
            raise serializers.ValidationError(_("Invalid gender choice."))
        return value
    
    def validate_activity_level(self, value):
        """Validate activity level is a valid choice"""
        if value not in [choice[0] for choice in ActivityLevel.choices]:
            raise serializers.ValidationError(_("Invalid activity level choice."))
        return value
    
    def to_representation(self, instance):
        """Add calculated TDEE data to the representation"""
        data = super().to_representation(instance)
        
        # Calculate BMR
        bmr_value = TDEECalculationModel.calculate_bmr(
            instance.weight_kg,
            instance.height_cm,
            instance.age_years,
            instance.gender
        )
        
        # Calculate TDEE
        tdee_value = TDEECalculationModel.calculate_tdee(bmr_value, instance.activity_level)
        
        # Get activity factor
        activity_factor = TDEECalculationModel.get_activity_factor(instance.activity_level)
        
        # Create temporary instance for status message
        temp_instance = TDEECalculationModel(
            weight_kg=instance.weight_kg,
            height_cm=instance.height_cm,
            age_years=instance.age_years,
            gender=instance.gender,
            activity_level=instance.activity_level,
            bmr_value=bmr_value,
            tdee_value=tdee_value
        )
        
        data.update({
            'bmr_value': bmr_value,
            'tdee_value': tdee_value,
            'activity_level_display': dict(ActivityLevel.choices)[instance.activity_level],
            'gender_display': dict(Gender.choices)[instance.gender],
            'tdee_status_message': temp_instance.tdee_status_message,
            'activity_factor': activity_factor
        })
        
        return data 