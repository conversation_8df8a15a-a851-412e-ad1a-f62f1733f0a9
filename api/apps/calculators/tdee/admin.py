"""
TDEE Calculator Admin Configuration

This module configures the Django admin interface for TDEE calculator models,
following Django admin best practices.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import TDEECalculationModel, TDEEGoalModel, ActivityLevel, Gender, GoalType


@admin.register(TDEECalculationModel)
class TDEECalculationAdmin(admin.ModelAdmin):
    """Admin configuration for TDEE calculations"""

    list_display = [
        "user_email",
        "tdee_value_display",
        "bmr_value_display",
        "activity_level_display",
        "gender_display",
        "weight_kg",
        "height_cm",
        "age_years",
        "is_active",
        "created_display",
    ]

    list_filter = ["activity_level", "gender", "is_active", "created", "updated"]

    search_fields = ["user__email", "user__first_name", "user__last_name", "notes"]

    readonly_fields = [
        "id",
        "bmr_value",
        "tdee_value",
        "activity_factor",
        "tdee_status_message",
        "created",
        "updated",
    ]

    fieldsets = (
        (_("User Information"), {"fields": ("user",)}),
        (
            _("Physical Measurements"),
            {"fields": ("weight_kg", "height_cm", "age_years", "gender")},
        ),
        (_("Activity Information"), {"fields": ("activity_level",)}),
        (
            _("Calculated Values"),
            {
                "fields": ("bmr_value", "tdee_value", "activity_factor"),
                "classes": ("collapse",),
            },
        ),
        (_("Status"), {"fields": ("is_active", "notes")}),
        (
            _("System Information"),
            {"fields": ("id", "created", "updated"), "classes": ("collapse",)},
        ),
        (
            _("Recommendations"),
            {"fields": ("tdee_status_message",), "classes": ("collapse",)},
        ),
    )

    ordering = ["-created"]
    date_hierarchy = "created"

    def user_email(self, obj):
        """Display user email with link to user admin"""
        if obj.user:
            url = reverse("admin:auth_user_change", args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.email)
        return "-"

    user_email.short_description = _("User")
    user_email.admin_order_field = "user__email"

    def tdee_value_display(self, obj):
        """Display TDEE value with formatting"""
        if obj.tdee_value:
            return format_html("<strong>{:.0f}</strong> kcal/day", obj.tdee_value)
        return "-"

    tdee_value_display.short_description = _("TDEE")
    tdee_value_display.admin_order_field = "tdee_value"

    def bmr_value_display(self, obj):
        """Display BMR value with formatting"""
        if obj.bmr_value:
            return format_html("<strong>{:.0f}</strong> kcal/day", obj.bmr_value)
        return "-"

    bmr_value_display.short_description = _("BMR")
    bmr_value_display.admin_order_field = "bmr_value"

    def activity_level_display(self, obj):
        """Display activity level with color coding"""
        colors = {
            "sedentary": "#dc3545",  
            "light": "#fd7e14",  
            "moderate": "#ffc107",  
            "high": "#28a745",  
            "extreme": "#6f42c1",  
        }
        color = colors.get(obj.activity_level, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_activity_level_display(),
        )

    activity_level_display.short_description = _("Activity Level")
    activity_level_display.admin_order_field = "activity_level"

    def gender_display(self, obj):
        """Display gender with icon"""
        icons = {"male": "♂", "female": "♀"}
        icon = icons.get(obj.gender, "")
        return format_html("{} {}", icon, obj.get_gender_display())

    gender_display.short_description = _("Gender")
    gender_display.admin_order_field = "gender"

    def created_display(self, obj):
        """Display creation date with relative time"""
        return obj.created.strftime("%Y-%m-%d %H:%M")

    created_display.short_description = _("Created")
    created_display.admin_order_field = "created"

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related("user")

    def save_model(self, request, obj, form, change):
        """Custom save logic"""
        if not change:  
            
            pass
        super().save_model(request, obj, form, change)


@admin.register(TDEEGoalModel)
class TDEEGoalAdmin(admin.ModelAdmin):
    """Admin configuration for TDEE goals"""

    list_display = [
        "user_email",
        "goal_type_display",
        "target_calories_display",
        "weight_change_display",
        "target_date",
        "is_achieved_display",
        "created_display",
    ]

    list_filter = ["goal_type", "is_achieved", "target_date", "created", "updated"]

    search_fields = ["user__email", "user__first_name", "user__last_name", "notes"]

    readonly_fields = [
        "id",
        "weight_difference",
        "calories_difference_from_maintenance",
        "estimated_weekly_weight_change",
        "estimated_time_to_goal_weeks",
        "goal_status_message",
        "created",
        "updated",
    ]

    fieldsets = (
        (_("User Information"), {"fields": ("user",)}),
        (
            _("Goal Settings"),
            {
                "fields": (
                    "goal_type",
                    "target_calories_per_day",
                    "weekly_weight_change_goal",
                )
            },
        ),
        (
            _("Weight Information"),
            {"fields": ("current_weight_kg", "target_weight_kg", "target_date")},
        ),
        (
            _("Calculated Values"),
            {
                "fields": (
                    "weight_difference",
                    "calories_difference_from_maintenance",
                    "estimated_weekly_weight_change",
                    "estimated_time_to_goal_weeks",
                ),
                "classes": ("collapse",),
            },
        ),
        (_("Achievement"), {"fields": ("is_achieved", "achieved_date")}),
        (_("Additional Information"), {"fields": ("notes",)}),
        (
            _("System Information"),
            {"fields": ("id", "created", "updated"), "classes": ("collapse",)},
        ),
        (
            _("Status Message"),
            {"fields": ("goal_status_message",), "classes": ("collapse",)},
        ),
    )

    ordering = ["-created"]
    date_hierarchy = "created"

    def user_email(self, obj):
        """Display user email with link to user admin"""
        if obj.user:
            url = reverse("admin:auth_user_change", args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.email)
        return "-"

    user_email.short_description = _("User")
    user_email.admin_order_field = "user__email"

    def goal_type_display(self, obj):
        """Display goal type with color coding"""
        colors = {
            "lose": "#dc3545",  
            "maintain": "#ffc107",  
            "gain": "#28a745",  
        }
        color = colors.get(obj.goal_type, "#6c757d")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_goal_type_display(),
        )

    goal_type_display.short_description = _("Goal Type")
    goal_type_display.admin_order_field = "goal_type"

    def target_calories_display(self, obj):
        """Display target calories with formatting"""
        return format_html(
            "<strong>{:.0f}</strong> kcal/day", obj.target_calories_per_day
        )

    target_calories_display.short_description = _("Target Calories")
    target_calories_display.admin_order_field = "target_calories_per_day"

    def weight_change_display(self, obj):
        """Display weight change information"""
        current = obj.current_weight_kg
        target = obj.target_weight_kg
        diff = obj.weight_difference

        if diff > 0:
            arrow = "↗"
            color = "#28a745"  
        elif diff < 0:
            arrow = "↘"
            color = "#dc3545"  
        else:
            arrow = "→"
            color = "#ffc107"  

        return format_html(
            '<span style="color: {};">{:.1f} kg {} {:.1f} kg ({:+.1f} kg)</span>',
            color,
            current,
            arrow,
            target,
            diff,
        )

    weight_change_display.short_description = _("Weight Change")

    def is_achieved_display(self, obj):
        """Display achievement status with icon"""
        if obj.is_achieved:
            return format_html(
                '<span style="color: #28a745;">✓ {}</span>',
                (
                    obj.achieved_date.strftime("%Y-%m-%d")
                    if obj.achieved_date
                    else _("Achieved")
                ),
            )
        else:
            return format_html(
                '<span style="color: #dc3545;">✗ {}</span>', _("In Progress")
            )

    is_achieved_display.short_description = _("Status")
    is_achieved_display.admin_order_field = "is_achieved"

    def created_display(self, obj):
        """Display creation date with relative time"""
        return obj.created.strftime("%Y-%m-%d %H:%M")

    created_display.short_description = _("Created")
    created_display.admin_order_field = "created"

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related("user")

    actions = ["mark_as_achieved", "mark_as_not_achieved"]

    def mark_as_achieved(self, request, queryset):
        """Mark selected goals as achieved"""
        from datetime import date

        updated = queryset.update(is_achieved=True, achieved_date=date.today())
        self.message_user(request, f"{updated} goals marked as achieved.")

    mark_as_achieved.short_description = _("Mark selected goals as achieved")

    def mark_as_not_achieved(self, request, queryset):
        """Mark selected goals as not achieved"""
        updated = queryset.update(is_achieved=False, achieved_date=None)
        self.message_user(request, f"{updated} goals marked as not achieved.")

    mark_as_not_achieved.short_description = _("Mark selected goals as not achieved")



admin.site.site_header = _("TDEE Calculator Administration")
admin.site.site_title = _("TDEE Calculator Admin")
admin.site.index_title = _("Welcome to TDEE Calculator Administration")
