"""
Heart Rate Zone Goal Serializer
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import HeartRateZoneGoal, HeartRateZone


class HeartRateZoneGoalSerializer(serializers.ModelSerializer):
    """
    Serializer for heart rate zone training goals.
    """
    
    target_zone_display = serializers.CharField(source='get_target_zone_display', read_only=True)
    zone_display_name = serializers.CharField(read_only=True)
    daily_minutes_target = serializers.FloatField(read_only=True)
    
    class Meta:
        model = HeartRateZoneGoal
        fields = [
            'id', '_id', 'user', 'target_zone', 'target_zone_display',
            'zone_display_name', 'weekly_minutes', 'daily_minutes_target',
            'current_age', 'goal_description', 'is_achieved', 
            'achieved_date', 'notes', 'created', 'updated'
        ]
        read_only_fields = ['id', '_id', 'user', 'created', 'updated']
    
    def validate_weekly_minutes(self, value):
        """Validate weekly minutes is reasonable"""
        if value <= 0:
            raise serializers.ValidationError(_("Weekly minutes must be positive."))
        if value > 10080:  # 7 days * 24 hours * 60 minutes
            raise serializers.ValidationError(_("Weekly minutes cannot exceed total minutes in a week."))
        return value
    
    def validate_current_age(self, value):
        """Validate age is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Age must be positive."))
        if value > 120:
            raise serializers.ValidationError(_("Age seems unreasonably high."))
        return value 