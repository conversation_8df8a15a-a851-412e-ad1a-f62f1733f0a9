"""
Heart Rate Zone History Serializer
"""

from rest_framework import serializers
from ..models import HeartRateZoneCalculation


class HeartRateZoneHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for heart rate zone calculation history.
    """
    
    method_display = serializers.CharField(source='get_method_display', read_only=True)
    zones_dict = serializers.JSONField(read_only=True)
    
    class Meta:
        model = HeartRateZoneCalculation
        fields = [
            'id', '_id', 'age', 'resting_heart_rate', 'method', 'method_display',
            'max_heart_rate', 'zones_dict', 'notes', 'is_active', 'created'
        ] 