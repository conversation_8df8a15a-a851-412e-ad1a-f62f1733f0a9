"""
Heart Rate Zone Calculation Create Serializer

This module contains the serializer for anonymous heart rate zone calculations,
following Django REST Framework best practices.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import HeartRateZoneCalculation, HeartRateCalculationMethod, HeartRateZone


class HeartRateZoneCalculationCreateSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for creating heart rate zone calculations without saving to database.
    
    This is used for the anonymous heart rate zone calculation endpoint that doesn't require authentication.
    It calculates and returns heart rate zone data without persisting it to the database.
    """
    
    max_heart_rate = serializers.IntegerField(read_only=True)
    heart_rate_reserve = serializers.IntegerField(read_only=True)
    method_display = serializers.CharField(read_only=True)
    zones = serializers.DictField(read_only=True)
    zone_descriptions = serializers.DictField(read_only=True)
    
    class Meta:
        model = HeartRateZoneCalculation
        fields = [
            'age',
            'resting_heart_rate',
            'method',
            'max_heart_rate',
            'heart_rate_reserve',
            'method_display',
            'zones',
            'zone_descriptions'
        ]
    
    def validate_age(self, value):
        """Validate age is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Age must be positive."))
        if value > 120:
            raise serializers.ValidationError(_("Age seems unreasonably high."))
        if value < 1:
            raise serializers.ValidationError(_("Age must be at least 1 year."))
        return value
    
    def validate_resting_heart_rate(self, value):
        """Validate resting heart rate if provided"""
        if value is not None:
            if value <= 0:
                raise serializers.ValidationError(_("Resting heart rate must be positive."))
            if value < 30:
                raise serializers.ValidationError(_("Resting heart rate seems unreasonably low."))
            if value > 120:
                raise serializers.ValidationError(_("Resting heart rate seems unreasonably high."))
        return value
    
    def validate(self, attrs):
        """Cross-field validation"""
        method = attrs.get('method', HeartRateCalculationMethod.SIMPLE_MAX)
        resting_hr = attrs.get('resting_heart_rate')
        age = attrs.get('age')
        
        # Karvonen method requires resting heart rate
        if method == HeartRateCalculationMethod.KARVONEN and not resting_hr:
            raise serializers.ValidationError({
                'resting_heart_rate': _('Resting heart rate is required for Karvonen method.')
            })
        
        # Validate resting HR vs estimated max HR
        if resting_hr and age:
            estimated_max_hr = HeartRateZoneCalculation.calculate_max_heart_rate(age, method)
            if resting_hr >= estimated_max_hr:
                raise serializers.ValidationError({
                    'resting_heart_rate': _('Resting heart rate must be less than maximum heart rate.')
                })
        
        return attrs
    
    def to_representation(self, instance):
        """Add calculated heart rate zone data to the representation"""
        data = super().to_representation(instance)
        
        # Calculate maximum heart rate
        max_hr = HeartRateZoneCalculation.calculate_max_heart_rate(
            instance.age, 
            instance.method
        )
        
        # Calculate zones based on method
        if instance.method == HeartRateCalculationMethod.KARVONEN and instance.resting_heart_rate:
            zones = HeartRateZoneCalculation.calculate_zones_karvonen(
                max_hr, instance.resting_heart_rate
            )
            heart_rate_reserve = max_hr - instance.resting_heart_rate
        else:
            zones = HeartRateZoneCalculation.calculate_zones_simple(max_hr)
            heart_rate_reserve = 0 if not instance.resting_heart_rate else max_hr - instance.resting_heart_rate
        
        # Format zones for API response
        formatted_zones = {}
        for zone_key, (min_hr, max_hr_zone) in zones.items():
            zone_number = zone_key.split('_')[1]
            formatted_zones[zone_key] = {
                'number': int(zone_number),
                'name': self._get_zone_name(zone_key),
                'min_bpm': min_hr,
                'max_bpm': max_hr_zone,
                'description': self._get_zone_description(zone_key),
                'percentage_range': self._get_zone_percentage_range(zone_key),
                'training_benefit': self._get_zone_training_benefit(zone_key),
                'color': self._get_zone_color(zone_key)
            }
        
        # Zone descriptions for UI
        zone_descriptions = {
            zone_key: {
                'name': self._get_zone_name(zone_key),
                'description': self._get_zone_description(zone_key),
                'training_benefit': self._get_zone_training_benefit(zone_key),
                'percentage_range': self._get_zone_percentage_range(zone_key)
            }
            for zone_key in zones.keys()
        }
        
        data.update({
            'max_heart_rate': max_hr,
            'heart_rate_reserve': heart_rate_reserve,
            'method_display': dict(HeartRateCalculationMethod.choices)[instance.method],
            'zones': formatted_zones,
            'zone_descriptions': zone_descriptions
        })
        
        return data
    
    def _get_zone_name(self, zone_key: str) -> str:
        """Get localized zone name"""
        zone_names = {
            'zone_1': _("Very Light"),
            'zone_2': _("Light"),
            'zone_3': _("Moderate"),
            'zone_4': _("Hard"),
            'zone_5': _("Maximum"),
        }
        return zone_names.get(zone_key, zone_key)
    
    def _get_zone_description(self, zone_key: str) -> str:
        """Get localized zone description"""
        descriptions = {
            'zone_1': _("Very light activity, warm-up, recovery"),
            'zone_2': _("Light activity, base fitness, fat burning"),
            'zone_3': _("Moderate activity, aerobic base building"),
            'zone_4': _("Hard activity, lactate threshold training"),
            'zone_5': _("Maximum effort, VO2 max, anaerobic power"),
        }
        return descriptions.get(zone_key, "")
    
    def _get_zone_training_benefit(self, zone_key: str) -> str:
        """Get training benefit for each zone"""
        benefits = {
            'zone_1': _("Active recovery, warm-up, cool-down"),
            'zone_2': _("Base fitness, fat burning, easy conversation pace"),
            'zone_3': _("Aerobic capacity, endurance building"),
            'zone_4': _("Lactate threshold, race pace training"),
            'zone_5': _("VO2 max, neuromuscular power, speed"),
        }
        return benefits.get(zone_key, "")
    
    def _get_zone_percentage_range(self, zone_key: str) -> str:
        """Get percentage range for each zone"""
        ranges = {
            'zone_1': "50-60%",
            'zone_2': "60-70%",
            'zone_3': "70-80%",
            'zone_4': "80-90%",
            'zone_5': "90-100%",
        }
        return ranges.get(zone_key, "")
    
    def _get_zone_color(self, zone_key: str) -> str:
        """Get color for each zone for UI consistency"""
        colors = {
            'zone_1': "#22c55e",  # Green
            'zone_2': "#84cc16",  # Lime
            'zone_3': "#eab308",  # Yellow
            'zone_4': "#f97316",  # Orange
            'zone_5': "#ef4444",  # Red
        }
        return colors.get(zone_key, "#6b7280") 