"""
Heart Rate Zone Calculation Serializer

Full serializer for heart rate zone calculations with user association.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import HeartRateZoneCalculation, HeartRateCalculationMethod


class HeartRateZoneCalculationSerializer(serializers.ModelSerializer):
    """
    Full serializer for heart rate zone calculations with all fields.
    Used for authenticated users and stored calculations.
    """
    
    max_heart_rate = serializers.IntegerField(read_only=True)
    heart_rate_reserve = serializers.IntegerField(read_only=True)
    method_display = serializers.CharField(source='get_method_display', read_only=True)
    zones_dict = serializers.JSONField(read_only=True)
    
    class Meta:
        model = HeartRateZoneCalculation
        fields = [
            'id', '_id', 'user', 'age', 'resting_heart_rate', 'method',
            'max_heart_rate', 'heart_rate_reserve', 'method_display',
            'zone_1_min', 'zone_1_max', 'zone_2_min', 'zone_2_max',
            'zone_3_min', 'zone_3_max', 'zone_4_min', 'zone_4_max',
            'zone_5_min', 'zone_5_max', 'zones_dict', 'notes', 
            'is_active', 'created', 'updated'
        ]
        read_only_fields = [
            'id', '_id', 'user', 'max_heart_rate', 'heart_rate_reserve',
            'zone_1_min', 'zone_1_max', 'zone_2_min', 'zone_2_max',
            'zone_3_min', 'zone_3_max', 'zone_4_min', 'zone_4_max',
            'zone_5_min', 'zone_5_max', 'created', 'updated'
        ]
    
    def validate_age(self, value):
        """Validate age is within reasonable range"""
        if value <= 0:
            raise serializers.ValidationError(_("Age must be positive."))
        if value > 120:
            raise serializers.ValidationError(_("Age seems unreasonably high."))
        return value
    
    def validate_resting_heart_rate(self, value):
        """Validate resting heart rate if provided"""
        if value is not None:
            if value <= 0:
                raise serializers.ValidationError(_("Resting heart rate must be positive."))
            if value < 30:
                raise serializers.ValidationError(_("Resting heart rate seems unreasonably low."))
            if value > 120:
                raise serializers.ValidationError(_("Resting heart rate seems unreasonably high."))
        return value
    
    def validate(self, attrs):
        """Cross-field validation"""
        method = attrs.get('method', HeartRateCalculationMethod.SIMPLE_MAX)
        resting_hr = attrs.get('resting_heart_rate')
        age = attrs.get('age')
        
        # Karvonen method requires resting heart rate
        if method == HeartRateCalculationMethod.KARVONEN and not resting_hr:
            raise serializers.ValidationError({
                'resting_heart_rate': _('Resting heart rate is required for Karvonen method.')
            })
        
        # Validate resting HR vs estimated max HR
        if resting_hr and age:
            estimated_max_hr = HeartRateZoneCalculation.calculate_max_heart_rate(age, method)
            if resting_hr >= estimated_max_hr:
                raise serializers.ValidationError({
                    'resting_heart_rate': _('Resting heart rate must be less than maximum heart rate.')
                })
        
        return attrs 