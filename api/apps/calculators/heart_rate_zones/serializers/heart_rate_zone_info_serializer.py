"""
Heart Rate Zone Info Serializer
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import HeartRateCalculationMethod, HeartRateZone


class HeartRateZoneInfoSerializer(serializers.Serializer):
    """
    Serializer for general heart rate zone information.
    Provides educational content about heart rate zones.
    """
    
    calculation_methods = serializers.SerializerMethodField()
    zone_information = serializers.SerializerMethodField()
    training_benefits = serializers.SerializerMethodField()
    general_guidelines = serializers.SerializerMethodField()
    
    def get_calculation_methods(self, obj):
        """Get available calculation methods with descriptions"""
        return [
            {
                'value': HeartRateCalculationMethod.SIMPLE_MAX,
                'label': _("220 - Age (Simple)"),
                'description': _("Basic formula: 220 minus your age. Quick and widely used."),
                'accuracy': _("Good for general population"),
                'requires_resting_hr': False
            },
            {
                'value': HeartRateCalculationMethod.TANAKA,
                'label': _("208 - (0.7 × Age) (<PERSON>)"),
                'description': _("More accurate formula, especially for older adults."),
                'accuracy': _("Better accuracy for all ages"),
                'requires_resting_hr': False
            },
            {
                'value': HeartRateCalculationMethod.KARVONEN,
                'label': _("Karvonen Method"),
                'description': _("Uses heart rate reserve for personalized zones."),
                'accuracy': _("Most personalized and accurate"),
                'requires_resting_hr': True
            }
        ]
    
    def get_zone_information(self, obj):
        """Get detailed zone information"""
        return [
            {
                'zone': 'zone_1',
                'number': 1,
                'name': _("Very Light"),
                'percentage': "50-60%",
                'description': _("Very light activity, warm-up, recovery"),
                'color': "#22c55e",
                'intensity': _("Very Low"),
                'breathing': _("Easy conversation possible"),
                'duration': _("20-90+ minutes")
            },
            {
                'zone': 'zone_2',
                'number': 2,
                'name': _("Light"),
                'percentage': "60-70%",
                'description': _("Light activity, base fitness, fat burning"),
                'color': "#84cc16",
                'intensity': _("Low"),
                'breathing': _("Can maintain conversation"),
                'duration': _("40-150+ minutes")
            },
            {
                'zone': 'zone_3',
                'number': 3,
                'name': _("Moderate"),
                'percentage': "70-80%",
                'description': _("Moderate activity, aerobic base building"),
                'color': "#eab308",
                'intensity': _("Moderate"),
                'breathing': _("Slightly breathless"),
                'duration': _("20-60 minutes")
            },
            {
                'zone': 'zone_4',
                'number': 4,
                'name': _("Hard"),
                'percentage': "80-90%",
                'description': _("Hard activity, lactate threshold training"),
                'color': "#f97316",
                'intensity': _("High"),
                'breathing': _("Difficult to speak"),
                'duration': _("10-40 minutes")
            },
            {
                'zone': 'zone_5',
                'number': 5,
                'name': _("Maximum"),
                'percentage': "90-100%",
                'description': _("Maximum effort, VO2 max, anaerobic power"),
                'color': "#ef4444",
                'intensity': _("Maximum"),
                'breathing': _("Cannot speak"),
                'duration': _("30 seconds - 8 minutes")
            }
        ]
    
    def get_training_benefits(self, obj):
        """Get training benefits for each zone"""
        return {
            'zone_1': {
                'primary_benefit': _("Recovery and Base Building"),
                'benefits': [
                    _("Active recovery"),
                    _("Improved circulation"),
                    _("Enhanced fat metabolism"),
                    _("Stress reduction")
                ],
                'ideal_for': _("Beginners, recovery days, warm-up")
            },
            'zone_2': {
                'primary_benefit': _("Aerobic Base and Fat Burning"),
                'benefits': [
                    _("Improved aerobic capacity"),
                    _("Enhanced fat burning"),
                    _("Better endurance"),
                    _("Cardiovascular health")
                ],
                'ideal_for': _("Base building, weight loss, long workouts")
            },
            'zone_3': {
                'primary_benefit': _("Aerobic Power"),
                'benefits': [
                    _("Increased aerobic power"),
                    _("Better oxygen utilization"),
                    _("Improved endurance"),
                    _("Enhanced recovery")
                ],
                'ideal_for': _("Tempo runs, steady-state cardio")
            },
            'zone_4': {
                'primary_benefit': _("Lactate Threshold"),
                'benefits': [
                    _("Improved lactate clearance"),
                    _("Higher sustainable pace"),
                    _("Better race performance"),
                    _("Increased power output")
                ],
                'ideal_for': _("Race pace training, intervals")
            },
            'zone_5': {
                'primary_benefit': _("VO2 Max and Power"),
                'benefits': [
                    _("Maximum oxygen uptake"),
                    _("Neuromuscular power"),
                    _("Speed development"),
                    _("Anaerobic capacity")
                ],
                'ideal_for': _("High-intensity intervals, speed work")
            }
        }
    
    def get_general_guidelines(self, obj):
        """Get general training guidelines"""
        return {
            'weekly_distribution': {
                'zone_1': _("20-30% of total training time"),
                'zone_2': _("40-50% of total training time"),
                'zone_3': _("15-20% of total training time"),
                'zone_4': _("5-10% of total training time"),
                'zone_5': _("2-5% of total training time")
            },
            'beginner_tips': [
                _("Start with mostly Zone 1 and 2 training"),
                _("Build base fitness before adding intensity"),
                _("Use a heart rate monitor for accuracy"),
                _("Listen to your body and adjust as needed")
            ],
            'safety_notes': [
                _("Consult a doctor before starting intense training"),
                _("Stay hydrated during all zones"),
                _("Allow adequate recovery between hard sessions"),
                _("Watch for signs of overtraining")
            ],
            'measurement_tips': [
                _("Take resting heart rate in the morning"),
                _("Use chest strap for most accurate readings"),
                _("Consider individual variation in formulas"),
                _("Regular fitness testing can refine zones")
            ]
        } 