"""
Heart Rate Zone URLs Configuration

URL patterns for heart rate zone calculator endpoints.
"""

from django.urls import path

from .views import (
    calculate_heart_rate_zones,
    HeartRateZoneCalculationListCreateView,
    HeartRateZoneCalculationRetrieveUpdateDestroyView,
    HeartRateZoneGoalListCreateView,
    HeartRateZoneGoalRetrieveUpdateDestroyView,
    HeartRateZoneInfoView,
    HeartRateZoneStatsView,
    HeartRateZoneHistoryView,
    set_active_heart_rate_zone_calculation,
    mark_heart_rate_zone_goal_achieved,
)

app_name = 'heart_rate_zones'

urlpatterns = [
    # Anonymous calculation endpoint
    path(
        'calculate/',
        calculate_heart_rate_zones,
        name='calculate_heart_rate_zones'
    ),
    
    # Heart rate zone calculation CRUD
    path(
        'calculations/',
        HeartRateZoneCalculationListCreateView.as_view(),
        name='heart_rate_zone_calculation_list_create'
    ),
    path(
        'calculations/<str:pk>/',
        HeartRateZoneCalculationRetrieveUpdateDestroyView.as_view(),
        name='heart_rate_zone_calculation_detail'
    ),
    
    # Set active calculation
    path(
        'calculations/<str:calculation_id>/set-active/',
        set_active_heart_rate_zone_calculation,
        name='set_active_heart_rate_zone_calculation'
    ),
    
    # Heart rate zone goals CRUD
    path(
        'goals/',
        HeartRateZoneGoalListCreateView.as_view(),
        name='heart_rate_zone_goal_list_create'
    ),
    path(
        'goals/<str:pk>/',
        HeartRateZoneGoalRetrieveUpdateDestroyView.as_view(),
        name='heart_rate_zone_goal_detail'
    ),
    
    # Mark goal as achieved
    path(
        'goals/<str:goal_id>/mark-achieved/',
        mark_heart_rate_zone_goal_achieved,
        name='mark_heart_rate_zone_goal_achieved'
    ),
    
    # Information and statistics endpoints
    path(
        'info/',
        HeartRateZoneInfoView.as_view(),
        name='heart_rate_zone_info'
    ),
    path(
        'stats/',
        HeartRateZoneStatsView.as_view(),
        name='heart_rate_zone_stats'
    ),
    path(
        'history/',
        HeartRateZoneHistoryView.as_view(),
        name='heart_rate_zone_history'
    ),
] 