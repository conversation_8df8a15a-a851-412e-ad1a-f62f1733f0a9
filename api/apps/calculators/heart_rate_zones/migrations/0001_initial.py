

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="HeartRateZoneGoalModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "target_zone",
                    models.CharField(
                        choices=[
                            ("zone_1", "Zone 1 - Very Light (50-60%)"),
                            ("zone_2", "Zone 2 - Light (60-70%)"),
                            ("zone_3", "Zone 3 - Moderate (70-80%)"),
                            ("zone_4", "Zone 4 - Hard (80-90%)"),
                            ("zone_5", "Zone 5 - Maximum (90-100%)"),
                        ],
                        max_length=10,
                        verbose_name="Target Training Zone",
                    ),
                ),
                (
                    "weekly_minutes",
                    models.PositiveIntegerField(
                        help_text="Target minutes per week in this zone",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(10080),
                        ],
                        verbose_name="Weekly Training Minutes",
                    ),
                ),
                (
                    "current_age",
                    models.PositiveIntegerField(
                        help_text="Current age for goal calculation",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(120),
                        ],
                        verbose_name="Current Age",
                    ),
                ),
                (
                    "goal_description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the training goal",
                        null=True,
                        verbose_name="Goal Description",
                    ),
                ),
                (
                    "is_achieved",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this goal has been achieved",
                        verbose_name="Achieved",
                    ),
                ),
                (
                    "achieved_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when goal was achieved",
                        null=True,
                        verbose_name="Achieved Date",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this goal",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="heart_rate_zone_goals",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Heart Rate Zone Goal",
                "verbose_name_plural": "Heart Rate Zone Goals",
                "db_table": "heart_rate_zones_goal",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="heart_rate__user_id_597203_idx",
                    ),
                    models.Index(
                        fields=["target_zone"], name="heart_rate__target__d66f38_idx"
                    ),
                    models.Index(
                        fields=["is_achieved"], name="heart_rate__is_achi_6f68ef_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="HeartRateZoneCalculationModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "age",
                    models.PositiveIntegerField(
                        help_text="Age in years (1-120)",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(120),
                        ],
                        verbose_name="Age",
                    ),
                ),
                (
                    "resting_heart_rate",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Resting heart rate in beats per minute (30-120). Optional for Karvonen method.",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(30),
                            django.core.validators.MaxValueValidator(120),
                        ],
                        verbose_name="Resting Heart Rate (bpm)",
                    ),
                ),
                (
                    "method",
                    models.CharField(
                        choices=[
                            ("simple_max", "220 - Age (Simple)"),
                            ("tanaka", "208 - (0.7 × Age) (Tanaka)"),
                            ("karvonen", "Karvonen Method (with resting HR)"),
                        ],
                        default="simple_max",
                        max_length=20,
                        verbose_name="Calculation Method",
                    ),
                ),
                (
                    "max_heart_rate",
                    models.PositiveIntegerField(
                        help_text="Calculated maximum heart rate",
                        verbose_name="Maximum Heart Rate (bpm)",
                    ),
                ),
                (
                    "zone_1_min",
                    models.PositiveIntegerField(verbose_name="Zone 1 Min (bpm)"),
                ),
                (
                    "zone_1_max",
                    models.PositiveIntegerField(verbose_name="Zone 1 Max (bpm)"),
                ),
                (
                    "zone_2_min",
                    models.PositiveIntegerField(verbose_name="Zone 2 Min (bpm)"),
                ),
                (
                    "zone_2_max",
                    models.PositiveIntegerField(verbose_name="Zone 2 Max (bpm)"),
                ),
                (
                    "zone_3_min",
                    models.PositiveIntegerField(verbose_name="Zone 3 Min (bpm)"),
                ),
                (
                    "zone_3_max",
                    models.PositiveIntegerField(verbose_name="Zone 3 Max (bpm)"),
                ),
                (
                    "zone_4_min",
                    models.PositiveIntegerField(verbose_name="Zone 4 Min (bpm)"),
                ),
                (
                    "zone_4_max",
                    models.PositiveIntegerField(verbose_name="Zone 4 Max (bpm)"),
                ),
                (
                    "zone_5_min",
                    models.PositiveIntegerField(verbose_name="Zone 5 Min (bpm)"),
                ),
                (
                    "zone_5_max",
                    models.PositiveIntegerField(verbose_name="Zone 5 Max (bpm)"),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Optional notes about this heart rate zone calculation",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this calculation is active/current",
                        verbose_name="Active",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="heart_rate_zone_calculations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Heart Rate Zone Calculation",
                "verbose_name_plural": "Heart Rate Zone Calculations",
                "db_table": "heart_rate_zones_calculation",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="heart_rate__user_id_15c678_idx",
                    ),
                    models.Index(
                        fields=["method"], name="heart_rate__method_d7b6dd_idx"
                    ),
                    models.Index(
                        fields=["is_active"], name="heart_rate__is_acti_2d6b4f_idx"
                    ),
                ],
            },
        ),
    ]
