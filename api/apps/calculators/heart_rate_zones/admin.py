"""
Heart Rate Zone Admin Configuration

Django admin configuration for heart rate zone models.
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from .models import HeartRateZoneCalculation, HeartRateZoneGoal


@admin.register(HeartRateZoneCalculation)
class HeartRateZoneCalculationAdmin(admin.ModelAdmin):
    """
    Admin interface for heart rate zone calculations.
    """
    
    list_display = [
        'user', 'age', 'method', 'max_heart_rate', 
        'resting_heart_rate', 'is_active', 'created'
    ]
    list_filter = ['method', 'is_active', 'created']
    search_fields = ['user__email', 'user__username', 'notes']
    ordering = ['-created']
    readonly_fields = [
        'id', '_id', 'max_heart_rate', 'zone_1_min', 'zone_1_max',
        'zone_2_min', 'zone_2_max', 'zone_3_min', 'zone_3_max',
        'zone_4_min', 'zone_4_max', 'zone_5_min', 'zone_5_max',
        'created', 'updated'
    ]
    
    fieldsets = (
        (_('User Information'), {
            'fields': ('user', 'created', 'updated')
        }),
        (_('Calculation Parameters'), {
            'fields': ('age', 'resting_heart_rate', 'method')
        }),
        (_('Calculated Results'), {
            'fields': (
                'max_heart_rate',
                ('zone_1_min', 'zone_1_max'),
                ('zone_2_min', 'zone_2_max'),
                ('zone_3_min', 'zone_3_max'),
                ('zone_4_min', 'zone_4_max'),
                ('zone_5_min', 'zone_5_max'),
            ),
            'classes': ('collapse',)
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'is_active')
        }),
        (_('System Information'), {
            'fields': ('id', '_id'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user')


@admin.register(HeartRateZoneGoal)
class HeartRateZoneGoalAdmin(admin.ModelAdmin):
    """
    Admin interface for heart rate zone goals.
    """
    
    list_display = [
        'user', 'target_zone', 'weekly_minutes', 'current_age',
        'is_achieved', 'achieved_date', 'created'
    ]
    list_filter = ['target_zone', 'is_achieved', 'created']
    search_fields = ['user__email', 'user__username', 'goal_description', 'notes']
    ordering = ['-created']
    readonly_fields = ['id', '_id', 'created', 'updated', 'daily_minutes_target']
    
    fieldsets = (
        (_('User Information'), {
            'fields': ('user', 'created', 'updated')
        }),
        (_('Goal Details'), {
            'fields': (
                'target_zone', 'weekly_minutes', 'daily_minutes_target',
                'current_age', 'goal_description'
            )
        }),
        (_('Achievement Status'), {
            'fields': ('is_achieved', 'achieved_date')
        }),
        (_('Additional Information'), {
            'fields': ('notes',)
        }),
        (_('System Information'), {
            'fields': ('id', '_id'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user')
    
    def daily_minutes_target(self, obj):
        """Display daily minutes target"""
        return f"{obj.daily_minutes_target} min/day"
    daily_minutes_target.short_description = _('Daily Target')



admin.site.site_header = _("The Proper Human Diet Administration")
admin.site.site_title = _("Heart Rate Zone Admin")
admin.site.index_title = _("Welcome to Heart Rate Zone Administration") 