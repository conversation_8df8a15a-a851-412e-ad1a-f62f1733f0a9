"""
Mark Heart Rate Zone Goal Achieved View
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone

from ..models import HeartRateZoneGoal


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_heart_rate_zone_goal_achieved(request, goal_id):
    """
    Mark a heart rate zone goal as achieved for the authenticated user.
    """
    user = request.user
    
    # Get the goal to mark as achieved
    goal = get_object_or_404(
        HeartRateZoneGoal, 
        id=goal_id, 
        user=user
    )
    
    # Mark goal as achieved
    goal.is_achieved = True
    goal.achieved_date = timezone.now().date()
    goal.save()
    
    return Response(
        {'message': 'Heart rate zone goal marked as achieved successfully.'}, 
        status=status.HTTP_200_OK
    ) 