"""
Heart Rate Zone Goal List Create View
"""

from rest_framework import generics, permissions

from ..models import HeartRateZoneGoal
from ..serializers import HeartRateZoneGoalSerializer


class HeartRateZoneGoalListCreateView(generics.ListCreateAPIView):
    """
    List and create heart rate zone goals for authenticated users.
    """
    
    serializer_class = HeartRateZoneGoalSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter goals by current user"""
        return HeartRateZoneGoal.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """Associate goal with current user"""
        serializer.save(user=self.request.user) 