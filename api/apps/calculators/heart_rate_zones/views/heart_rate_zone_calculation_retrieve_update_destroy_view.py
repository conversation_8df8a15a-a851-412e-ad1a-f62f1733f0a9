"""
Heart Rate Zone Calculation Retrieve Update Destroy View
"""

from rest_framework import generics, permissions

from ..models import HeartRateZoneCalculation
from ..serializers import HeartRateZoneCalculationSerializer


class HeartRateZoneCalculationRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, and delete heart rate zone calculations.
    """
    
    serializer_class = HeartRateZoneCalculationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter calculations by current user"""
        return HeartRateZoneCalculation.objects.filter(user=self.request.user) 