"""
Heart Rate Zone Calculation List Create View
"""

from rest_framework import generics, permissions
from rest_framework.response import Response
from rest_framework import status

from ..models import HeartRateZoneCalculation
from ..serializers import HeartRateZoneCalculationSerializer


class HeartRateZoneCalculationListCreateView(generics.ListCreateAPIView):
    """
    List and create heart rate zone calculations for authenticated users.
    """
    
    serializer_class = HeartRateZoneCalculationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter calculations by current user"""
        return HeartRateZoneCalculation.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        """Associate calculation with current user"""
        serializer.save(user=self.request.user) 