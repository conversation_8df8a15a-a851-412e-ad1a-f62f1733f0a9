"""
Heart Rate Zone History View
"""

from rest_framework import generics, permissions

from ..models import HeartRateZoneCalculation
from ..serializers import HeartRateZoneHistorySerializer


class HeartRateZoneHistoryView(generics.ListAPIView):
    """
    List heart rate zone calculation history for authenticated users.
    """
    
    serializer_class = HeartRateZoneHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter history by current user"""
        return HeartRateZoneCalculation.objects.filter(user=self.request.user).order_by('-created') 