"""
Heart Rate Zone Goal Retrieve Update Destroy View
"""

from rest_framework import generics, permissions

from ..models import HeartRateZoneGoal
from ..serializers import HeartRateZoneGoalSerializer


class HeartRateZoneGoalRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, and delete heart rate zone goals.
    """
    
    serializer_class = HeartRateZoneGoalSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter goals by current user"""
        return HeartRateZoneGoal.objects.filter(user=self.request.user) 