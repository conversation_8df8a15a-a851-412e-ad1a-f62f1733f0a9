"""
Calculate Heart Rate Zones View

This module contains the function-based view for anonymous heart rate zone calculation,
following Django REST Framework best practices.
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response

from ..models import HeartRateZoneCalculation
from ..serializers import HeartRateZoneCalculationCreateSerializer


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def calculate_heart_rate_zones(request):
    """
    Calculate heart rate zones without saving to database.
    
    This endpoint can be used by anonymous users to calculate heart rate zones
    without requiring authentication. It's useful for the public
    heart rate zone calculator functionality.
    """
    serializer = HeartRateZoneCalculationCreateSerializer(data=request.data)
    
    if serializer.is_valid():
        # Create temporary instance for calculation
        temp_instance = HeartRateZoneCalculation(
            age=serializer.validated_data['age'],
            resting_heart_rate=serializer.validated_data.get('resting_heart_rate'),
            method=serializer.validated_data.get('method', 'simple_max')
        )
        
        # Return calculated heart rate zone data
        return Response(
            serializer.to_representation(temp_instance),
            status=status.HTTP_200_OK
        )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST) 