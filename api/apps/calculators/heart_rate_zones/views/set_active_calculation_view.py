"""
Set Active Heart Rate Zone Calculation View
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from ..models import HeartRateZoneCalculation


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def set_active_heart_rate_zone_calculation(request, calculation_id):
    """
    Set a heart rate zone calculation as active for the authenticated user.
    """
    user = request.user
    
    # Get the calculation to set as active
    calculation = get_object_or_404(
        HeartRateZoneCalculation, 
        id=calculation_id, 
        user=user
    )
    
    # Deactivate all other calculations for this user
    HeartRateZoneCalculation.objects.filter(user=user, is_active=True).update(is_active=False)
    
    # Set this calculation as active
    calculation.is_active = True
    calculation.save()
    
    return Response(
        {'message': 'Heart rate zone calculation set as active successfully.'}, 
        status=status.HTTP_200_OK
    ) 