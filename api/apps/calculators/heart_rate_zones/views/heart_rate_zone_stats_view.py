"""
Heart Rate Zone Stats View
"""

from rest_framework import generics, permissions
from rest_framework.response import Response
from django.utils import timezone
from datetime import timedelta

from ..models import HeartRateZoneCalculation, HeartRateZoneGoal
from ..serializers import HeartRateZoneStatsSerializer


class HeartRateZoneStatsView(generics.GenericAPIView):
    """
    Provides statistics about user's heart rate zone calculations.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = HeartRateZoneStatsSerializer
    
    def get(self, request):
        """Return heart rate zone statistics for the user"""
        user = request.user
        
        # Get calculations
        calculations = HeartRateZoneCalculation.objects.filter(user=user)
        
        # Get this month's calculations
        this_month = timezone.now() - timedelta(days=30)
        calculations_this_month = calculations.filter(created__gte=this_month).count()
        
        # Get current active calculation
        active_calculation = calculations.filter(is_active=True).first()
        
        # Get goals
        goals = HeartRateZoneGoal.objects.filter(user=user)
        active_goals = goals.filter(is_achieved=False)
        
        stats_data = {
            'total_calculations': calculations.count(),
            'calculations_this_month': calculations_this_month,
            'current_method': active_calculation.get_method_display() if active_calculation else '',
            'current_max_hr': active_calculation.max_heart_rate if active_calculation else 0,
            'has_active_goals': active_goals.exists(),
            'active_goals_count': active_goals.count(),
            'latest_calculation_date': calculations.first().created if calculations.exists() else None,
        }
        
        serializer = self.get_serializer(data=stats_data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data) 