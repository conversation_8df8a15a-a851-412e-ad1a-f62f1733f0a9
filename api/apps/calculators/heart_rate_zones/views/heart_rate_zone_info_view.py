"""
Heart Rate Zone Info View
"""

from rest_framework import generics, permissions
from rest_framework.response import Response

from ..serializers import HeartRateZoneInfoSerializer


class HeartRateZoneInfoView(generics.GenericAPIView):
    """
    Provides general information about heart rate zones.
    """
    
    permission_classes = [permissions.AllowAny]
    serializer_class = HeartRateZoneInfoSerializer
    
    def get(self, request):
        """Return heart rate zone information"""
        serializer = self.get_serializer(data={})
        serializer.is_valid()  # Always valid for info endpoint
        return Response(serializer.data) 