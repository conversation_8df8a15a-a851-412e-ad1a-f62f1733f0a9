from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from decimal import Decimal, ROUND_HALF_UP

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class HeartRateCalculationMethod(models.TextChoices):
    """Heart rate calculation method choices"""
    
    SIMPLE_MAX = "simple_max", _("220 - Age (Simple)")
    TANAKA = "tanaka", _("208 - (0.7 × Age) (Tanaka)")
    KARVONEN = "karvonen", _("Karvonen Method (with resting HR)")


class HeartRateZone(models.TextChoices):
    """Heart rate zone choices based on training intensity"""
    
    ZONE_1 = "zone_1", _("Zone 1 - Very Light (50-60%)")
    ZONE_2 = "zone_2", _("Zone 2 - Light (60-70%)")
    ZONE_3 = "zone_3", _("Zone 3 - Moderate (70-80%)")
    ZONE_4 = "zone_4", _("Zone 4 - Hard (80-90%)")
    ZONE_5 = "zone_5", _("Zone 5 - Maximum (90-100%)")


class HeartRateZoneCalculationModel(AbstractAutoIncrementModel):
    """
    Model to store heart rate zone calculations for users with historical tracking.
    
    Supports multiple calculation methods:
    - Simple: 220 - age
    - Tanaka: 208 - (0.7 × age) - more accurate for older adults
    - Karvonen: Uses heart rate reserve (max_hr - resting_hr) for personalized zones
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="heart_rate_zone_calculations",
        verbose_name=_("User"),
    )
    
    age = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1),
            MaxValueValidator(120),
        ],
        verbose_name=_("Age"),
        help_text=_("Age in years (1-120)"),
    )
    
    resting_heart_rate = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=[
            MinValueValidator(30),
            MaxValueValidator(120),
        ],
        verbose_name=_("Resting Heart Rate (bpm)"),
        help_text=_("Resting heart rate in beats per minute (30-120). Optional for Karvonen method."),
    )
    
    method = models.CharField(
        max_length=20,
        choices=HeartRateCalculationMethod.choices,
        default=HeartRateCalculationMethod.SIMPLE_MAX,
        verbose_name=_("Calculation Method"),
    )
    
    max_heart_rate = models.PositiveIntegerField(
        verbose_name=_("Maximum Heart Rate (bpm)"),
        help_text=_("Calculated maximum heart rate"),
    )
    
    # Zone ranges - stored as JSON in practice, but using separate fields for clarity
    zone_1_min = models.PositiveIntegerField(verbose_name=_("Zone 1 Min (bpm)"))
    zone_1_max = models.PositiveIntegerField(verbose_name=_("Zone 1 Max (bpm)"))
    zone_2_min = models.PositiveIntegerField(verbose_name=_("Zone 2 Min (bpm)"))
    zone_2_max = models.PositiveIntegerField(verbose_name=_("Zone 2 Max (bpm)"))
    zone_3_min = models.PositiveIntegerField(verbose_name=_("Zone 3 Min (bpm)"))
    zone_3_max = models.PositiveIntegerField(verbose_name=_("Zone 3 Max (bpm)"))
    zone_4_min = models.PositiveIntegerField(verbose_name=_("Zone 4 Min (bpm)"))
    zone_4_max = models.PositiveIntegerField(verbose_name=_("Zone 4 Max (bpm)"))
    zone_5_min = models.PositiveIntegerField(verbose_name=_("Zone 5 Min (bpm)"))
    zone_5_max = models.PositiveIntegerField(verbose_name=_("Zone 5 Max (bpm)"))
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Optional notes about this heart rate zone calculation"),
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this calculation is active/current"),
    )
    
    class Meta:
        app_label = "apps_calculators_heart_rate_zones"
        db_table = "heart_rate_zones_calculation"
        verbose_name = _("Heart Rate Zone Calculation")
        verbose_name_plural = _("Heart Rate Zone Calculations")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["method"]),
            models.Index(fields=["is_active"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - Age: {self.age}, Max HR: {self.max_heart_rate} ({self.get_method_display()})"
    
    @staticmethod
    def calculate_max_heart_rate(age: int, method: str = HeartRateCalculationMethod.SIMPLE_MAX) -> int:
        """
        Calculate maximum heart rate using the specified method.
        
        Args:
            age: Age in years
            method: Calculation method to use
            
        Returns:
            Maximum heart rate in bpm
            
        Raises:
            ValueError: If age is invalid
        """
        if age <= 0 or age > 120:
            raise ValueError("Age must be between 1 and 120 years")
            
        if method == HeartRateCalculationMethod.SIMPLE_MAX:
            return 220 - age
        elif method == HeartRateCalculationMethod.TANAKA:
            return int(208 - (0.7 * age))
        else:
            # Default to simple method for other cases (Karvonen uses max HR as base)
            return 220 - age
    
    @staticmethod
    def calculate_zones_simple(max_heart_rate: int) -> dict:
        """
        Calculate heart rate zones using simple percentage method.
        
        Args:
            max_heart_rate: Maximum heart rate in bpm
            
        Returns:
            Dictionary with zone ranges
        """
        zones = {
            'zone_1': (int(0.50 * max_heart_rate), int(0.60 * max_heart_rate)),
            'zone_2': (int(0.60 * max_heart_rate), int(0.70 * max_heart_rate)),
            'zone_3': (int(0.70 * max_heart_rate), int(0.80 * max_heart_rate)),
            'zone_4': (int(0.80 * max_heart_rate), int(0.90 * max_heart_rate)),
            'zone_5': (int(0.90 * max_heart_rate), int(1.00 * max_heart_rate)),
        }
        return zones
    
    @staticmethod
    def calculate_zones_karvonen(max_heart_rate: int, resting_heart_rate: int) -> dict:
        """
        Calculate heart rate zones using Karvonen method (heart rate reserve).
        
        Args:
            max_heart_rate: Maximum heart rate in bpm
            resting_heart_rate: Resting heart rate in bpm
            
        Returns:
            Dictionary with zone ranges
            
        Raises:
            ValueError: If resting heart rate is invalid
        """
        if resting_heart_rate <= 0 or resting_heart_rate >= max_heart_rate:
            raise ValueError("Resting heart rate must be positive and less than max heart rate")
            
        reserve = max_heart_rate - resting_heart_rate
        
        zones = {
            'zone_1': (
                int(resting_heart_rate + 0.50 * reserve), 
                int(resting_heart_rate + 0.60 * reserve)
            ),
            'zone_2': (
                int(resting_heart_rate + 0.60 * reserve), 
                int(resting_heart_rate + 0.70 * reserve)
            ),
            'zone_3': (
                int(resting_heart_rate + 0.70 * reserve), 
                int(resting_heart_rate + 0.80 * reserve)
            ),
            'zone_4': (
                int(resting_heart_rate + 0.80 * reserve), 
                int(resting_heart_rate + 0.90 * reserve)
            ),
            'zone_5': (
                int(resting_heart_rate + 0.90 * reserve), 
                int(resting_heart_rate + 1.00 * reserve)
            ),
        }
        return zones
    
    @property
    def zones_dict(self) -> dict:
        """Get heart rate zones as a dictionary"""
        return {
            'zone_1': {'min': self.zone_1_min, 'max': self.zone_1_max},
            'zone_2': {'min': self.zone_2_min, 'max': self.zone_2_max},
            'zone_3': {'min': self.zone_3_min, 'max': self.zone_3_max},
            'zone_4': {'min': self.zone_4_min, 'max': self.zone_4_max},
            'zone_5': {'min': self.zone_5_min, 'max': self.zone_5_max},
        }
    
    @property
    def heart_rate_reserve(self) -> int:
        """Calculate heart rate reserve if resting HR is available"""
        if self.resting_heart_rate:
            return self.max_heart_rate - self.resting_heart_rate
        return 0
    
    def save(self, *args, **kwargs):
        """Override save to auto-calculate heart rate zones"""
        
        # Calculate maximum heart rate
        self.max_heart_rate = self.calculate_max_heart_rate(self.age, self.method)
        
        # Calculate zones based on method
        if self.method == HeartRateCalculationMethod.KARVONEN and self.resting_heart_rate:
            zones = self.calculate_zones_karvonen(self.max_heart_rate, self.resting_heart_rate)
        else:
            zones = self.calculate_zones_simple(self.max_heart_rate)
        
        # Set zone values
        self.zone_1_min, self.zone_1_max = zones['zone_1']
        self.zone_2_min, self.zone_2_max = zones['zone_2']
        self.zone_3_min, self.zone_3_max = zones['zone_3']
        self.zone_4_min, self.zone_4_max = zones['zone_4']
        self.zone_5_min, self.zone_5_max = zones['zone_5']
        
        # Set only one calculation as active per user
        if self.is_active and not self.pk:
            HeartRateZoneCalculationModel.objects.filter(
                user=self.user, is_active=True
            ).update(is_active=False)
        
        super().save(*args, **kwargs)
    
    def clean(self):
        """Validate the model data"""
        from django.core.exceptions import ValidationError
        
        super().clean()
        
        if self.method == HeartRateCalculationMethod.KARVONEN and not self.resting_heart_rate:
            raise ValidationError({
                'resting_heart_rate': _('Resting heart rate is required for Karvonen method.')
            })
        
        if self.resting_heart_rate and self.resting_heart_rate >= (220 - self.age):
            raise ValidationError({
                'resting_heart_rate': _('Resting heart rate must be less than maximum heart rate.')
            })


class HeartRateZoneGoalModel(AbstractAutoIncrementModel):
    """
    Model to store user heart rate training goals.
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="heart_rate_zone_goals",
        verbose_name=_("User"),
    )
    
    target_zone = models.CharField(
        max_length=10,
        choices=HeartRateZone.choices,
        verbose_name=_("Target Training Zone"),
    )
    
    weekly_minutes = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1),
            MaxValueValidator(10080),  # 7 days * 24 hours * 60 minutes
        ],
        verbose_name=_("Weekly Training Minutes"),
        help_text=_("Target minutes per week in this zone"),
    )
    
    current_age = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1),
            MaxValueValidator(120),
        ],
        verbose_name=_("Current Age"),
        help_text=_("Current age for goal calculation"),
    )
    
    goal_description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Goal Description"),
        help_text=_("Description of the training goal"),
    )
    
    is_achieved = models.BooleanField(
        default=False,
        verbose_name=_("Achieved"),
        help_text=_("Whether this goal has been achieved"),
    )
    
    achieved_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Achieved Date"),
        help_text=_("Date when goal was achieved"),
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Additional notes about this goal"),
    )
    
    class Meta:
        app_label = "apps_calculators_heart_rate_zones"
        db_table = "heart_rate_zones_goal"
        verbose_name = _("Heart Rate Zone Goal")
        verbose_name_plural = _("Heart Rate Zone Goals")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["target_zone"]),
            models.Index(fields=["is_achieved"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.get_target_zone_display()}: {self.weekly_minutes}min/week"
    
    @property
    def zone_display_name(self) -> str:
        """Get a user-friendly zone name"""
        zone_names = {
            HeartRateZone.ZONE_1: _("Very Light Intensity"),
            HeartRateZone.ZONE_2: _("Light Intensity"),
            HeartRateZone.ZONE_3: _("Moderate Intensity"),
            HeartRateZone.ZONE_4: _("Hard Intensity"),
            HeartRateZone.ZONE_5: _("Maximum Intensity"),
        }
        return zone_names.get(self.target_zone, self.get_target_zone_display())
    
    @property
    def daily_minutes_target(self) -> float:
        """Calculate daily minutes target"""
        return round(self.weekly_minutes / 7, 1)


# Alias for easier imports (following BMI pattern)
HeartRateZoneCalculation = HeartRateZoneCalculationModel
HeartRateZoneGoal = HeartRateZoneGoalModel 