from django.urls import path

from .views import (
    BodyFatCalculationListCreateView,
    BodyFatCalculationDetailView,
    BodyFatCalculationActiveView,
    BodyFatGoalListCreateView,
    BodyFatGoalDetailView,
    BodyFatGoalAchieveView,
    BodyFatStatsView,
    BodyFatRecommendationsView,
    BodyFatQuickCalculationView,
)

app_name = 'apps_calculators_body_fat'

urlpatterns = [
    
    path(
        'calculations/',
        BodyFatCalculationListCreateView.as_view(),
        name='calculation-list-create'
    ),
    path(
        'calculations/<int:pk>/',
        BodyFatCalculationDetailView.as_view(),
        name='calculation-detail'
    ),
    path(
        'calculations/active/',
        BodyFatCalculationActiveView.as_view(),
        name='calculation-active'
    ),
    path(
        'calculations/quick/',
        BodyFatQuickCalculationView.as_view(),
        name='calculation-quick'
    ),
    
    
    path(
        'goals/',
        BodyFatGoalListCreateView.as_view(),
        name='goal-list-create'
    ),
    path(
        'goals/<int:pk>/',
        BodyFatGoalDetailView.as_view(),
        name='goal-detail'
    ),
    path(
        'goals/<int:pk>/achieve/',
        BodyFatGoalAchieveView.as_view(),
        name='goal-achieve'
    ),
    
    
    path(
        'stats/',
        BodyFatStatsView.as_view(),
        name='stats'
    ),
    path(
        'recommendations/',
        BodyFatRecommendationsView.as_view(),
        name='recommendations'
    ),
] 