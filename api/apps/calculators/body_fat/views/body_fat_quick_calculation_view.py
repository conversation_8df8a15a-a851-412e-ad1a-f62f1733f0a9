"""
Body Fat Quick Calculation View

Handles quick body fat calculations without saving to database.
"""

from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from ..models import BodyFatCalculationModel


class BodyFatQuickCalculationView(generics.GenericAPIView):
    """
    Perform a quick body fat calculation without saving to database.
    
    POST: Returns calculated body fat percentage and related metrics
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """Perform quick body fat calculation"""
        try:
            
            gender = request.data.get('gender')
            age = request.data.get('age')
            weight = request.data.get('weight')
            height = request.data.get('height')
            method = request.data.get('method', 'navy')
            
            
            if not all([gender, age, weight, height]):
                return Response(
                    {'error': _('Gender, age, weight, and height are required.')},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            
            age = int(age)
            weight = float(weight)
            height = int(height)
            
            
            if method == 'navy':
                waist = request.data.get('waist_circumference')
                neck = request.data.get('neck_circumference')
                hip = request.data.get('hip_circumference') if gender.lower() == 'female' else None
                
                if not waist or not neck:
                    return Response(
                        {'error': _('Waist and neck circumferences are required for Navy method.')},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                if gender.lower() == 'female' and not hip:
                    return Response(
                        {'error': _('Hip circumference is required for women using Navy method.')},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                body_fat_percentage = BodyFatCalculationModel.calculate_navy_method(
                    gender=gender.lower(),
                    waist=float(waist),
                    neck=float(neck),
                    height=height,
                    hip=float(hip) if hip else None
                )
                
            elif method == 'ymca':
                waist = request.data.get('waist_circumference')
                
                if not waist:
                    return Response(
                        {'error': _('Waist circumference is required for YMCA method.')},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                body_fat_percentage = BodyFatCalculationModel.calculate_ymca_method(
                    gender=gender.lower(),
                    weight=weight,
                    waist=float(waist)
                )
                
            elif method == 'skinfold_3':
                
                if gender.lower() == 'male':
                    chest = request.data.get('chest_skinfold')
                    abdomen = request.data.get('abdominal_skinfold')
                    thigh = request.data.get('thigh_skinfold')
                    
                    if not all([chest, abdomen, thigh]):
                        return Response(
                            {'error': _('Chest, abdominal, and thigh skinfolds are required for men using 3-site method.')},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    
                    body_fat_percentage = BodyFatCalculationModel.calculate_skinfold_3_site(
                        gender=gender.lower(),
                        age=age,
                        chest=float(chest),
                        abdominal=float(abdomen),
                        thigh=float(thigh)
                    )
                else:
                    tricep = request.data.get('tricep_skinfold')
                    suprailiac = request.data.get('suprailiac_skinfold')
                    thigh = request.data.get('thigh_skinfold')
                    
                    if not all([tricep, suprailiac, thigh]):
                        return Response(
                            {'error': _('Tricep, suprailiac, and thigh skinfolds are required for women using 3-site method.')},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    
                    body_fat_percentage = BodyFatCalculationModel.calculate_skinfold_3_site(
                        gender=gender.lower(),
                        age=age,
                        tricep=float(tricep),
                        suprailiac=float(suprailiac),
                        thigh=float(thigh)
                    )
                    
            elif method in ['bia', 'dexa']:
                manual_bf = request.data.get('manual_body_fat_percentage')
                
                if not manual_bf:
                    return Response(
                        {'error': _('Manual body fat percentage is required for {method} method.').format(method=method.upper())},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                body_fat_percentage = float(manual_bf)
                
            else:
                return Response(
                    {'error': _('Invalid calculation method.')},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            
            weight_decimal = Decimal(str(weight))
            bf_decimal = Decimal(str(body_fat_percentage))
            
            fat_mass = (weight_decimal * bf_decimal / 100).quantize(Decimal('0.1'))
            lean_mass = (weight_decimal - fat_mass).quantize(Decimal('0.1'))
            
            
            category = BodyFatCalculationModel.get_body_fat_category(
                gender=gender.lower(),
                body_fat_percentage=body_fat_percentage
            )
            
            
            height_m = height / 100
            bmi = weight / (height_m * height_m)
            
            return Response({
                'method': method,
                'body_fat_percentage': round(body_fat_percentage, 1),
                'fat_mass_kg': float(fat_mass),
                'lean_mass_kg': float(lean_mass),
                'category': category,
                'bmi': round(bmi, 1),
                'health_assessment': {
                    'category': category,
                    'category_display': category.replace('_', ' ').title(),
                    'bmi': round(bmi, 1),
                    'is_healthy': category in ['athletic', 'fitness'],
                },
                'body_composition': {
                    'body_fat_percentage': round(body_fat_percentage, 1),
                    'fat_mass_kg': float(fat_mass),
                    'lean_mass_kg': float(lean_mass),
                    'fat_mass_percentage': round(body_fat_percentage, 1),
                    'lean_mass_percentage': round(100 - body_fat_percentage, 1),
                    'weight_kg': weight,
                }
            })
            
        except ValueError as e:
            return Response(
                {'error': _('Invalid input values. Please check your data.')},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': _('An error occurred during calculation. Please try again.')},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 