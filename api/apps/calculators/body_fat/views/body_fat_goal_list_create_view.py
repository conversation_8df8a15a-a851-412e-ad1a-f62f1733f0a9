"""
Body Fat Goal List Create View

Handles listing and creating body fat goals for authenticated users.
"""

from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from ..models import BodyFatGoalModel
from ..serializers import (
    BodyFatGoalSerializer,
    BodyFatGoalCreateSerializer,
)


class BodyFatGoalListCreateView(generics.ListCreateAPIView):
    """
    List and create body fat goals for authenticated users.
    
    GET: Returns a paginated list of user's body fat goals
    POST: Creates a new body fat goal
    """
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter goals by authenticated user"""
        return BodyFatGoalModel.objects.filter(
            user=self.request.user
        ).order_by('-created')
    
    def get_serializer_class(self):
        """Use different serializers for create vs list operations"""
        if self.request.method == 'POST':
            return BodyFatGoalCreateSerializer
        return BodyFatGoalSerializer
    
    def perform_create(self, serializer):
        """Save goal with current user"""
        serializer.save(user=self.request.user) 