"""
Body Fat Goal Detail View

Handles retrieving, updating, and deleting individual body fat goals.
"""

from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from ..models import BodyFatGoalModel
from ..serializers import BodyFatGoalSerializer


class BodyFatGoalDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific body fat goal.
    
    GET: Returns detailed goal data with progress tracking
    PUT/PATCH: Updates the goal
    DELETE: Removes the goal
    """
    permission_classes = [IsAuthenticated]
    serializer_class = BodyFatGoalSerializer
    lookup_field = 'pk'
    
    def get_queryset(self):
        """Filter goals by authenticated user"""
        return BodyFatGoalModel.objects.filter(user=self.request.user) 