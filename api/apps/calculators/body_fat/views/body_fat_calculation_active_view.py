"""
Body Fat Calculation Active View

Handles retrieving the active body fat calculation for a user.
"""

from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from ..models import BodyFatCalculationModel
from ..serializers import BodyFatCalculationSerializer


class BodyFatCalculationActiveView(generics.RetrieveAPIView):
    """
    Get the active body fat calculation for the authenticated user.
    
    Returns the calculation marked as active, or the most recent one
    if no active calculation exists.
    """
    permission_classes = [IsAuthenticated]
    serializer_class = BodyFatCalculationSerializer
    
    def get_object(self):
        """Get active calculation or most recent if none active"""
        try:
            
            return BodyFatCalculationModel.objects.filter(
                user=self.request.user,
                is_active=True
            ).latest('created')
        except BodyFatCalculationModel.DoesNotExist:
            
            return BodyFatCalculationModel.objects.filter(
                user=self.request.user
            ).latest('created') 