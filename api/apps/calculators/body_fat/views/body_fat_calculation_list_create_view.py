"""
Body Fat Calculation List Create View

Handles listing and creating body fat calculations for authenticated users.
"""

from rest_framework import generics, permissions
from rest_framework.permissions import IsAuthenticated

from ..models import BodyFatCalculationModel
from ..serializers import (
    BodyFatCalculationSerializer,
    BodyFatCalculationCreateSerializer,
)


class BodyFatCalculationListCreateView(generics.ListCreateAPIView):
    """
    List and create body fat calculations for authenticated users.
    
    GET: Returns a paginated list of user's body fat calculations
    POST: Creates a new body fat calculation
    """
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter calculations by authenticated user"""
        return BodyFatCalculationModel.objects.filter(
            user=self.request.user
        ).order_by('-created')
    
    def get_serializer_class(self):
        """Use different serializers for create vs list operations"""
        if self.request.method == 'POST':
            return BodyFatCalculationCreateSerializer
        return BodyFatCalculationSerializer
    
    def perform_create(self, serializer):
        """Save calculation with current user"""
        serializer.save(user=self.request.user) 