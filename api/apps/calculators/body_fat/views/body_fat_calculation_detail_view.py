"""
Body Fat Calculation Detail View

Handles retrieving, updating, and deleting individual body fat calculations.
"""

from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from ..models import BodyFatCalculationModel
from ..serializers import BodyFatCalculationSerializer


class BodyFatCalculationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update, or delete a specific body fat calculation.
    
    GET: Returns detailed calculation data
    PUT/PATCH: Updates the calculation
    DELETE: Removes the calculation
    """
    permission_classes = [IsAuthenticated]
    serializer_class = BodyFatCalculationSerializer
    lookup_field = 'pk'
    
    def get_queryset(self):
        """Filter calculations by authenticated user"""
        return BodyFatCalculationModel.objects.filter(user=self.request.user) 