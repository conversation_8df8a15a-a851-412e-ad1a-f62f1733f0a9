"""
Body Fat Calculator Views Package

This package contains individual view files for the body fat calculator,
following Django REST framework best practices.
"""

from .body_fat_calculation_list_create_view import BodyFatCalculationListCreateView
from .body_fat_calculation_detail_view import BodyFatCalculationDetailView
from .body_fat_calculation_active_view import BodyFatCalculationActiveView
from .body_fat_goal_list_create_view import BodyFatGoalListCreateView
from .body_fat_goal_detail_view import BodyFatGoalDetailView
from .body_fat_goal_achieve_view import BodyFatGoalAchieveView
from .body_fat_stats_view import BodyFatStatsView
from .body_fat_recommendations_view import BodyFatRecommendationsView
from .body_fat_quick_calculation_view import BodyFatQuickCalculationView

__all__ = [
    'BodyFatCalculationListCreateView',
    'BodyFatCalculationDetailView',
    'BodyFatCalculationActiveView',
    'BodyFatGoalListCreateView',
    'BodyFatGoalDetailView',
    'BodyFatGoalAchieveView',
    'BodyFatStatsView',
    'BodyFatRecommendationsView',
    'BodyFatQuickCalculationView',
] 