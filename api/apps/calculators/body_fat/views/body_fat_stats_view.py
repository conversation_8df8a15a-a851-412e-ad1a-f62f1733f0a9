"""
Body Fat Stats View

Handles providing comprehensive statistics for user's body fat calculations.
"""

from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db.models import Avg, Count, Max, Min
from django.utils.translation import gettext_lazy as _

from ..models import BodyFatCalculationModel, BodyFatGoalModel


class BodyFatStatsView(generics.GenericAPIView):
    """
    Get comprehensive statistics for the user's body fat calculations.
    
    GET: Returns detailed stats including averages, trends, and goal progress
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get comprehensive body fat statistics"""
        user = request.user
        user_calculations = BodyFatCalculationModel.objects.filter(user=user)
        user_goals = BodyFatGoalModel.objects.filter(user=user)
        
        if not user_calculations.exists():
            return Response({
                'total_calculations': 0,
                'average_body_fat': None,
                'latest_body_fat': None,
                'lowest_body_fat': None,
                'highest_body_fat': None,
                'body_fat_trend': None,
                'total_goals': 0,
                'achieved_goals': 0,
                'active_goals': 0,
            })
        
        
        calculation_stats = user_calculations.aggregate(
            avg_body_fat=Avg('body_fat_percentage'),
            min_body_fat=Min('body_fat_percentage'),
            max_body_fat=Max('body_fat_percentage'),
            total_count=Count('id')
        )
        
        latest_calculation = user_calculations.first()
        
        
        trend = None
        if calculation_stats['avg_body_fat'] and latest_calculation:
            latest_bf = float(latest_calculation.body_fat_percentage)
            avg_bf = float(calculation_stats['avg_body_fat'])
            difference = latest_bf - avg_bf
            
            if abs(difference) < 0.5:
                trend = 'stable'
            elif difference > 0:
                trend = 'increasing'
            else:
                trend = 'decreasing'
        
        
        goal_stats = user_goals.aggregate(
            total_goals=Count('id'),
            achieved_goals=Count('id', filter=user_goals.filter(is_achieved=True).values('id')),
        )
        
        active_goals = user_goals.filter(is_achieved=False).count()
        
        
        method_stats = {}
        for calc in user_calculations:
            method = calc.method
            if method in method_stats:
                method_stats[method] += 1
            else:
                method_stats[method] = 1
        
        return Response({
            'total_calculations': calculation_stats['total_count'],
            'average_body_fat': round(float(calculation_stats['avg_body_fat']), 1) if calculation_stats['avg_body_fat'] else None,
            'latest_body_fat': float(latest_calculation.body_fat_percentage) if latest_calculation else None,
            'lowest_body_fat': round(float(calculation_stats['min_body_fat']), 1) if calculation_stats['min_body_fat'] else None,
            'highest_body_fat': round(float(calculation_stats['max_body_fat']), 1) if calculation_stats['max_body_fat'] else None,
            'body_fat_trend': trend,
            'total_goals': goal_stats['total_goals'] or 0,
            'achieved_goals': goal_stats['achieved_goals'] or 0,
            'active_goals': active_goals,
            'method_usage': method_stats,
            'health_category': latest_calculation.category if latest_calculation else None,
        }) 