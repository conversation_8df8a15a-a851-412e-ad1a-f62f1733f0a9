"""
Body Fat Goal Achieve View

Handles marking a body fat goal as achieved.
"""

from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from ..models import BodyFatGoalModel
from ..serializers import BodyFatGoalSerializer


class BodyFatGoalAchieveView(generics.UpdateAPIView):
    """
    Mark a body fat goal as achieved.
    
    PATCH: Marks the goal as achieved and sets the achieved date
    """
    permission_classes = [IsAuthenticated]
    serializer_class = BodyFatGoalSerializer
    lookup_field = 'pk'
    
    def get_queryset(self):
        """Filter goals by authenticated user"""
        return BodyFatGoalModel.objects.filter(user=self.request.user)
    
    def patch(self, request, *args, **kwargs):
        """Mark goal as achieved"""
        try:
            goal = self.get_object()
            
            if goal.is_achieved:
                return Response(
                    {'detail': _('Goal is already marked as achieved.')},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            goal.is_achieved = True
            goal.achieved_date = timezone.now().date()
            goal.save()
            
            serializer = self.get_serializer(goal)
            return Response(serializer.data)
            
        except BodyFatGoalModel.DoesNotExist:
            return Response(
                {'detail': _('Goal not found.')},
                status=status.HTTP_404_NOT_FOUND
            ) 