"""
Body Fat Recommendations View

Handles providing personalized body fat recommendations based on user profile and goals.
"""

from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _

from ..models import BodyFatCalculationModel


class BodyFatRecommendationsView(generics.GenericAPIView):
    """
    Get personalized body fat recommendations based on user profile and goals.
    
    POST: Returns recommendations based on provided or existing user data
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """Get personalized body fat recommendations"""
        
        latest_calc = BodyFatCalculationModel.objects.filter(
            user=request.user
        ).order_by('-created').first()
        
        
        gender = request.data.get('gender', latest_calc.gender if latest_calc else 'male')
        age = request.data.get('age', latest_calc.age if latest_calc else 30)
        current_body_fat = float(request.data.get('body_fat_percentage', 
                                               latest_calc.body_fat_percentage if latest_calc else 15))
        
        
        if gender.lower() == 'male':
            if age < 30:
                ranges = {
                    'essential': (2, 5), 
                    'athletic': (6, 13), 
                    'fitness': (14, 17), 
                    'average': (18, 24), 
                    'obese': (25, 100)
                }
            elif age < 40:
                ranges = {
                    'essential': (2, 5), 
                    'athletic': (7, 15), 
                    'fitness': (16, 20), 
                    'average': (21, 27), 
                    'obese': (28, 100)
                }
            else:
                ranges = {
                    'essential': (2, 5), 
                    'athletic': (8, 17), 
                    'fitness': (18, 22), 
                    'average': (23, 29), 
                    'obese': (30, 100)
                }
        else:  
            if age < 30:
                ranges = {
                    'essential': (10, 13), 
                    'athletic': (14, 20), 
                    'fitness': (21, 24), 
                    'average': (25, 31), 
                    'obese': (32, 100)
                }
            elif age < 40:
                ranges = {
                    'essential': (10, 13), 
                    'athletic': (15, 21), 
                    'fitness': (22, 25), 
                    'average': (26, 32), 
                    'obese': (33, 100)
                }
            else:
                ranges = {
                    'essential': (10, 13), 
                    'athletic': (16, 22), 
                    'fitness': (23, 26), 
                    'average': (27, 33), 
                    'obese': (34, 100)
                }
        
        
        current_category = 'obese'
        for category, (min_val, max_val) in ranges.items():
            if min_val <= current_body_fat <= max_val:
                current_category = category
                break
        
        
        recommendations = []
        target_range = None
        
        if current_category == 'essential':
            recommendations.append(_("Your body fat is at essential levels. Consider gaining healthy weight through resistance training and proper nutrition."))
            target_range = ranges['fitness']
        elif current_category == 'athletic':
            recommendations.append(_("Excellent! You're in the athletic range. Maintain with regular training and balanced nutrition."))
            target_range = ranges['athletic']
        elif current_category == 'fitness':
            recommendations.append(_("Great! You're in the fitness range. Continue your current approach or aim for athletic levels."))
            target_range = ranges['fitness']
        elif current_category == 'average':
            recommendations.append(_("You're in the average range. Consider targeting the fitness range through diet and exercise improvements."))
            target_range = ranges['fitness']
        else:  
            recommendations.append(_("Consider focusing on gradual fat loss through a combination of resistance training, cardiovascular exercise, and nutrition optimization."))
            target_range = ranges['average']
        
        
        if current_body_fat > ranges['fitness'][1]:
            recommendations.extend([
                _("Focus on creating a moderate caloric deficit (300-500 calories per day)"),
                _("Include both resistance training and cardiovascular exercise"),
                _("Aim for 1-2 pounds of fat loss per week"),
                _("Consider tracking your food intake to ensure proper nutrition")
            ])
        elif current_body_fat < ranges['fitness'][0]:
            recommendations.extend([
                _("Focus on muscle building through progressive resistance training"),
                _("Ensure adequate protein intake (0.8-1g per pound of body weight)"),
                _("Consider a slight caloric surplus for muscle growth"),
                _("Monitor recovery and stress levels")
            ])
        
        return Response({
            'gender': gender,
            'age': age,
            'current_body_fat_percentage': current_body_fat,
            'current_category': current_category,
            'current_category_display': current_category.replace('_', ' ').title(),
            'health_ranges': ranges,
            'target_range': {
                'category': list(ranges.keys())[list(ranges.values()).index(target_range)],
                'min': target_range[0],
                'max': target_range[1]
            },
            'recommendations': recommendations,
            'next_steps': [
                _("Set a realistic goal using our goal tracker"),
                _("Track your progress with regular measurements"),
                _("Consider consulting with a fitness professional"),
                _("Focus on sustainable lifestyle changes")
            ]
        }) 