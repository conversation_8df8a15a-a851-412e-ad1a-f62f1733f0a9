"""
Body Fat Calculation Serializer

Serializer for displaying body fat calculations with all calculated fields
and health assessments.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import BodyFatCalculationModel


class BodyFatCalculationSerializer(serializers.ModelSerializer):
    """
    Serializer for body fat calculations with all calculated fields.
    """
    health_category_display = serializers.CharField(
        source='get_health_category_display',
        read_only=True
    )
    calculation_method_display = serializers.CharField(
        source='get_calculation_method_display',
        read_only=True
    )
    body_composition = serializers.SerializerMethodField()
    health_assessment = serializers.SerializerMethodField()
    bmi = serializers.ReadOnlyField()
    ideal_body_fat_range = serializers.ReadOnlyField()
    is_in_healthy_range = serializers.ReadOnlyField()
    
    class Meta:
        model = BodyFatCalculationModel
        fields = [
            'id',
            'created',
            'updated',
            'is_active',
            
            
            'age',
            'gender',
            'weight',
            'height',
            'method',
            'waist_circumference',
            'neck_circumference',
            'hip_circumference',
            'tricep_skinfold',
            'subscapular_skinfold',
            'suprailiac_skinfold',
            'abdominal_skinfold',
            'thigh_skinfold',
            'chest_skinfold',
            'midaxillary_skinfold',
            'manual_body_fat_percentage',
            
            
            'body_fat_percentage',
            'fat_mass',
            'lean_mass',
            'category',
            'health_category_display',
            'calculation_method_display',
            'bmi',
            'ideal_body_fat_range',
            'is_in_healthy_range',
            
            
            'body_composition',
            'health_assessment',
            'notes',
        ]
        read_only_fields = [
            'id',
            'created',
            'updated',
            'body_fat_percentage',
            'fat_mass',
            'lean_mass',
            'category',
            'bmi',
            'ideal_body_fat_range',
            'is_in_healthy_range',
        ]
    
    def get_body_composition(self, obj):
        """Get body composition breakdown"""
        return {
            'body_fat_percentage': float(obj.body_fat_percentage),
            'fat_mass_kg': float(obj.fat_mass),
            'lean_mass_kg': float(obj.lean_mass),
            'fat_mass_percentage': float(obj.body_fat_percentage),
            'lean_mass_percentage': 100 - float(obj.body_fat_percentage),
            'weight_kg': float(obj.weight),
        }
    
    def get_health_assessment(self, obj):
        """Get health assessment based on body fat percentage"""
        return {
            'category': obj.category,
            'category_display': obj.get_category_display(),
            'is_healthy': obj.is_in_healthy_range,
            'bmi': float(obj.bmi),
            'ideal_range': {
                'min': float(obj.ideal_body_fat_range[0]),
                'max': float(obj.ideal_body_fat_range[1]),
            },
            'recommendations': self._get_health_recommendations(obj),
        }
    
    def _get_health_recommendations(self, obj):
        """Generate health recommendations based on body fat percentage"""
        recommendations = []
        
        if obj.body_fat_percentage < obj.ideal_body_fat_range[0]:
            recommendations.append(_("Consider gaining healthy weight through resistance training and proper nutrition."))
        elif obj.body_fat_percentage > obj.ideal_body_fat_range[1]:
            recommendations.append(_("Consider a balanced approach to fat loss through diet and exercise."))
        else:
            recommendations.append(_("Your body fat percentage is in a healthy range. Maintain with regular exercise."))
        
        return recommendations 