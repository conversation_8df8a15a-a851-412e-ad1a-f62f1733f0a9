"""
Body Fat Goal Serializer

Serializer for displaying body fat goals with progress tracking
and achievement status.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from ..models import BodyFatGoalModel


class BodyFatGoalSerializer(serializers.ModelSerializer):
    """
    Serializer for body fat goals with progress tracking.
    """
    progress_percentage = serializers.SerializerMethodField()
    days_remaining = serializers.SerializerMethodField()
    days_to_goal = serializers.ReadOnlyField()
    body_fat_difference = serializers.ReadOnlyField()
    is_achievable = serializers.SerializerMethodField()
    goal_status = serializers.SerializerMethodField()
    
    class Meta:
        model = BodyFatGoalModel
        fields = [
            'id',
            'created',
            'updated',
            'target_body_fat_percentage',
            'current_body_fat_percentage',
            'target_date',
            'goal_description',
            'is_achieved',
            'achieved_date',
            'notes',
            
            
            'progress_percentage',
            'days_remaining',
            'days_to_goal',
            'body_fat_difference',
            'is_achievable',
            'goal_status',
        ]
        read_only_fields = [
            'id',
            'created',
            'updated',
            'is_achieved',
            'achieved_date',
            'progress_percentage',
            'days_remaining',
            'days_to_goal',
            'body_fat_difference',
            'is_achievable',
            'goal_status',
        ]
    
    def get_progress_percentage(self, obj):
        """Calculate progress percentage towards the goal"""
        if obj.body_fat_difference == 0:
            return 100.0
        
        
        from ..models import BodyFatCalculationModel
        latest_calc = BodyFatCalculationModel.objects.filter(
            user=obj.user
        ).first()
        
        if not latest_calc:
            return 0.0
        
        current_bf = float(latest_calc.body_fat_percentage)
        initial_bf = float(obj.current_body_fat_percentage)
        target_bf = float(obj.target_body_fat_percentage)
        
        if initial_bf == target_bf:
            return 100.0
        
        progress = (initial_bf - current_bf) / (initial_bf - target_bf) * 100
        return max(0.0, min(100.0, progress))
    
    def get_days_remaining(self, obj):
        """Calculate days remaining to target date"""
        if obj.is_achieved:
            return 0
        
        today = timezone.now().date()
        if obj.target_date <= today:
            return 0
        
        return (obj.target_date - today).days
    
    def get_is_achievable(self, obj):
        """Determine if goal is realistically achievable"""
        if obj.is_achieved:
            return True
        
        days_remaining = self.get_days_remaining(obj)
        if days_remaining <= 0:
            return False
        
        
        weeks_remaining = days_remaining / 7
        max_realistic_change = weeks_remaining * 1.0  
        
        required_change = abs(float(obj.body_fat_difference))
        return required_change <= max_realistic_change
    
    def get_goal_status(self, obj):
        """Get comprehensive goal status"""
        if obj.is_achieved:
            return {
                'status': 'achieved',
                'message': _('Goal achieved!'),
                'color': 'success'
            }
        
        days_remaining = self.get_days_remaining(obj)
        progress = self.get_progress_percentage(obj)
        
        if days_remaining <= 0:
            return {
                'status': 'overdue',
                'message': _('Goal is overdue'),
                'color': 'danger'
            }
        elif progress >= 75:
            return {
                'status': 'on_track',
                'message': _('Great progress! You\'re on track.'),
                'color': 'success'
            }
        elif progress >= 25:
            return {
                'status': 'moderate_progress',
                'message': _('Making progress, keep going!'),
                'color': 'warning'
            }
        else:
            return {
                'status': 'needs_attention',
                'message': _('Consider adjusting your approach.'),
                'color': 'info'
            } 