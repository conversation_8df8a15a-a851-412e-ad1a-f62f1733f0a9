"""
Body Fat Goal Create Serializer

Serializer for creating body fat goals with validation
and automatic current body fat detection.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta

from ..models import BodyFatGoalModel


class BodyFatGoalCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating body fat goals with validation.
    """
    
    class Meta:
        model = BodyFatGoalModel
        fields = [
            'target_body_fat_percentage',
            'current_body_fat_percentage',
            'target_date',
            'goal_description',
            'notes',
        ]
    
    def validate_target_date(self, value):
        """Validate that target date is in the future"""
        if value <= timezone.now().date():
            raise serializers.ValidationError(
                _('Target date must be in the future.')
            )
        
        
        max_date = timezone.now().date() + timedelta(days=730)
        if value > max_date:
            raise serializers.ValidationError(
                _('Target date cannot be more than 2 years in the future.')
            )
        
        return value
    
    def validate(self, data):
        """
        Validate goal data for consistency and realism.
        """
        target_bf = data.get('target_body_fat_percentage')
        current_bf = data.get('current_body_fat_percentage')
        target_date = data.get('target_date')
        
        if target_bf and current_bf:
            
            bf_difference = abs(float(target_bf) - float(current_bf))
            
            if bf_difference == 0:
                raise serializers.ValidationError({
                    'target_body_fat_percentage': _(
                        'Target body fat percentage must be different from current.'
                    )
                })
            
            if bf_difference > 20:
                raise serializers.ValidationError({
                    'target_body_fat_percentage': _(
                        'Body fat percentage change cannot exceed 20%.'
                    )
                })
            
            
            if target_date:
                days_to_goal = (target_date - timezone.now().date()).days
                weeks_to_goal = days_to_goal / 7
                
                
                max_realistic_change = weeks_to_goal * 1.0
                
                if bf_difference > max_realistic_change and bf_difference > 2:
                    raise serializers.ValidationError({
                        'target_date': _(
                            'Target date is too ambitious for the desired body fat change. '
                            'Consider extending the timeline or reducing the target change.'
                        )
                    })
        
        return data
    
    def create(self, validated_data):
        """
        Create a new body fat goal with auto-detected current body fat if not provided.
        """
        user = validated_data['user']
        
        
        if not validated_data.get('current_body_fat_percentage'):
            from ..models import BodyFatCalculationModel
            latest_calc = BodyFatCalculationModel.objects.filter(
                user=user
            ).first()
            
            if latest_calc:
                validated_data['current_body_fat_percentage'] = latest_calc.body_fat_percentage
            else:
                raise serializers.ValidationError({
                    'current_body_fat_percentage': _(
                        'Current body fat percentage is required. '
                        'Please provide it or create a body fat calculation first.'
                    )
                })
        
        return super().create(validated_data) 