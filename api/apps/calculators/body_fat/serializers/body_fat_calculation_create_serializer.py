"""
Body Fat Calculation Create Serializer

Serializer for creating body fat calculations with comprehensive validation
based on calculation method requirements.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import BodyFatCalculationModel


class BodyFatCalculationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating body fat calculations with validation.
    """
    
    class Meta:
        model = BodyFatCalculationModel
        fields = [
            'age',
            'gender',
            'weight',
            'height',
            'method',
            'waist_circumference',
            'neck_circumference',
            'hip_circumference',
            'tricep_skinfold',
            'subscapular_skinfold',
            'suprailiac_skinfold',
            'abdominal_skinfold',
            'thigh_skinfold',
            'chest_skinfold',
            'midaxillary_skinfold',
            'manual_body_fat_percentage',
            'is_active',
            'notes',
        ]
    
    def validate(self, data):
        """
        Validate input data based on calculation method.
        """
        method = data.get('method')
        gender = data.get('gender')
        
        if method == 'navy':
            if not data.get('waist_circumference'):
                raise serializers.ValidationError({
                    'waist_circumference': _('Waist circumference is required for Navy method.')
                })
            if not data.get('neck_circumference'):
                raise serializers.ValidationError({
                    'neck_circumference': _('Neck circumference is required for Navy method.')
                })
            if gender == 'female' and not data.get('hip_circumference'):
                raise serializers.ValidationError({
                    'hip_circumference': _('Hip circumference is required for women using Navy method.')
                })
        
        elif method == 'ymca':
            if not data.get('waist_circumference'):
                raise serializers.ValidationError({
                    'waist_circumference': _('Waist circumference is required for YMCA method.')
                })
        
        elif method == 'skinfold_3':
            
            if gender == 'male':
                required_sites = ['chest_skinfold', 'abdominal_skinfold', 'thigh_skinfold']
                required_names = ['chest', 'abdominal', 'thigh']
            else:
                required_sites = ['tricep_skinfold', 'suprailiac_skinfold', 'thigh_skinfold']
                required_names = ['tricep', 'suprailiac', 'thigh']
            
            missing_sites = []
            for i, site in enumerate(required_sites):
                if not data.get(site):
                    missing_sites.append(required_names[i])
            
            if missing_sites:
                raise serializers.ValidationError({
                    'skinfold_measurements': _(
                        'Missing required skinfold measurements for 3-site method: {sites}'
                    ).format(sites=', '.join(missing_sites))
                })
        
        elif method == 'skinfold_7':
            
            required_sites = [
                'chest_skinfold', 'abdominal_skinfold', 'thigh_skinfold',
                'tricep_skinfold', 'subscapular_skinfold', 'suprailiac_skinfold',
                'midaxillary_skinfold'
            ]
            required_names = [
                'chest', 'abdominal', 'thigh', 'tricep', 
                'subscapular', 'suprailiac', 'midaxillary'
            ]
            
            missing_sites = []
            for i, site in enumerate(required_sites):
                if not data.get(site):
                    missing_sites.append(required_names[i])
            
            if missing_sites:
                raise serializers.ValidationError({
                    'skinfold_measurements': _(
                        'Missing required skinfold measurements for 7-site method: {sites}'
                    ).format(sites=', '.join(missing_sites))
                })
        
        elif method in ['bia', 'dexa']:
            if not data.get('manual_body_fat_percentage'):
                raise serializers.ValidationError({
                    'manual_body_fat_percentage': _(
                        'Manual body fat percentage is required for {method} method.'
                    ).format(method=method.upper())
                })
        
        return data
    
    def create(self, validated_data):
        """
        Create a new body fat calculation with proper calculations.
        """
        
        if validated_data.get('is_active', True):
            BodyFatCalculationModel.objects.filter(
                user=validated_data['user'],
                is_active=True
            ).update(is_active=False)
        
        return super().create(validated_data) 