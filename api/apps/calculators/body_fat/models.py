from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from decimal import Decimal, ROUND_HALF_UP
import math

from core.abstract.models import AbstractAutoIncrementModel

User = get_user_model()


class BodyFatCalculationMethod(models.TextChoices):
    """Body fat calculation method choices"""
    
    NAVY = "navy", _("US Navy Method (circumference)")
    YMCA = "ymca", _("YMCA Method (circumference)")
    SKINFOLD_3 = "skinfold_3", _("3-Site Skinfold")
    SKINFOLD_7 = "skinfold_7", _("7-Site Skinfold")
    BIA = "bia", _("Bioelectrical Impedance Analysis")
    DEXA = "dexa", _("DEXA Scan (manual entry)")


class BodyFatCategory(models.TextChoices):
    """Body fat category choices based on percentage"""
    
    ESSENTIAL = "essential", _("Essential Fat")
    ATHLETE = "athlete", _("Athlete")
    FITNESS = "fitness", _("Fitness")
    AVERAGE = "average", _("Average")
    OBESE = "obese", _("Obese")


class BodyFatCalculationModel(AbstractAutoIncrementModel):
    """
    Model to store body fat calculations for users with historical tracking.
    
    Supports multiple calculation methods:
    - US Navy Method: Uses waist, neck, and hip circumferences
    - YMCA Method: Uses waist and weight
    - Skinfold Methods: Uses skinfold measurements at various sites
    - BIA: Bioelectrical Impedance Analysis results
    - DEXA: Manual entry of DEXA scan results
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="body_fat_calculations",
        verbose_name=_("User"),
    )
    
    # Basic Information
    age = models.PositiveIntegerField(
        validators=[
            MinValueValidator(1),
            MaxValueValidator(120),
        ],
        verbose_name=_("Age"),
        help_text=_("Age in years (1-120)"),
    )
    
    gender = models.CharField(
        max_length=10,
        choices=[
            ('male', _('Male')),
            ('female', _('Female')),
        ],
        verbose_name=_("Gender"),
    )
    
    weight = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        validators=[
            MinValueValidator(Decimal('20.0')),
            MaxValueValidator(Decimal('500.0')),
        ],
        verbose_name=_("Weight (kg)"),
        help_text=_("Weight in kilograms (20-500 kg)"),
    )
    
    height = models.PositiveIntegerField(
        validators=[
            MinValueValidator(50),
            MaxValueValidator(300),
        ],
        verbose_name=_("Height (cm)"),
        help_text=_("Height in centimeters (50-300 cm)"),
    )
    
    method = models.CharField(
        max_length=20,
        choices=BodyFatCalculationMethod.choices,
        default=BodyFatCalculationMethod.NAVY,
        verbose_name=_("Calculation Method"),
    )
    
    # Circumference Measurements (for Navy/YMCA methods)
    waist_circumference = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('30.0')),
            MaxValueValidator(Decimal('200.0')),
        ],
        verbose_name=_("Waist Circumference (cm)"),
        help_text=_("Waist circumference in centimeters"),
    )
    
    neck_circumference = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('20.0')),
            MaxValueValidator(Decimal('80.0')),
        ],
        verbose_name=_("Neck Circumference (cm)"),
        help_text=_("Neck circumference in centimeters"),
    )
    
    hip_circumference = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('50.0')),
            MaxValueValidator(Decimal('200.0')),
        ],
        verbose_name=_("Hip Circumference (cm)"),
        help_text=_("Hip circumference in centimeters (for women)"),
    )
    
    # Skinfold Measurements (in mm)
    tricep_skinfold = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Tricep Skinfold (mm)"),
    )
    
    subscapular_skinfold = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Subscapular Skinfold (mm)"),
    )
    
    suprailiac_skinfold = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Suprailiac Skinfold (mm)"),
    )
    
    abdominal_skinfold = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Abdominal Skinfold (mm)"),
    )
    
    thigh_skinfold = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Thigh Skinfold (mm)"),
    )
    
    chest_skinfold = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Chest Skinfold (mm)"),
    )
    
    midaxillary_skinfold = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Midaxillary Skinfold (mm)"),
    )
    
    # Manual Entry (for BIA/DEXA)
    manual_body_fat_percentage = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True,
        validators=[
            MinValueValidator(Decimal('1.0')),
            MaxValueValidator(Decimal('60.0')),
        ],
        verbose_name=_("Manual Body Fat Percentage"),
        help_text=_("Manually entered body fat percentage from BIA/DEXA"),
    )
    
    # Calculated Results
    body_fat_percentage = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        verbose_name=_("Body Fat Percentage"),
        help_text=_("Calculated body fat percentage"),
    )
    
    fat_mass = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        verbose_name=_("Fat Mass (kg)"),
        help_text=_("Calculated fat mass in kilograms"),
    )
    
    lean_mass = models.DecimalField(
        max_digits=5,
        decimal_places=1,
        verbose_name=_("Lean Mass (kg)"),
        help_text=_("Calculated lean mass in kilograms"),
    )
    
    category = models.CharField(
        max_length=20,
        choices=BodyFatCategory.choices,
        verbose_name=_("Body Fat Category"),
        help_text=_("Body fat category based on percentage"),
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Optional notes about this body fat calculation"),
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this calculation is active/current"),
    )
    
    class Meta:
        app_label = "apps_calculators_body_fat"
        db_table = "body_fat_calculation"
        verbose_name = _("Body Fat Calculation")
        verbose_name_plural = _("Body Fat Calculations")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["method"]),
            models.Index(fields=["is_active"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.body_fat_percentage}% ({self.get_method_display()})"
    
    @staticmethod
    def calculate_navy_method(gender: str, waist: float, neck: float, height: int, hip: float = None) -> float:
        """
        Calculate body fat using US Navy method.
        
        Args:
            gender: 'male' or 'female'
            waist: Waist circumference in cm
            neck: Neck circumference in cm
            height: Height in cm
            hip: Hip circumference in cm (for women)
            
        Returns:
            Body fat percentage
        """
        if gender.lower() == 'male':
            # Men: 495 / (1.0324 - 0.19077 * log10(waist - neck) + 0.15456 * log10(height)) - 450
            body_fat = 495 / (1.0324 - 0.19077 * math.log10(waist - neck) + 0.15456 * math.log10(height)) - 450
        else:
            # Women: 495 / (1.29579 - 0.35004 * log10(waist + hip - neck) + 0.22100 * log10(height)) - 450
            if hip is None:
                raise ValueError("Hip circumference required for women")
            body_fat = 495 / (1.29579 - 0.35004 * math.log10(waist + hip - neck) + 0.22100 * math.log10(height)) - 450
        
        return max(1.0, min(60.0, body_fat))
    
    @staticmethod
    def calculate_ymca_method(gender: str, weight: float, waist: float) -> float:
        """
        Calculate body fat using YMCA method.
        
        Args:
            gender: 'male' or 'female'
            weight: Weight in kg
            waist: Waist circumference in cm
            
        Returns:
            Body fat percentage
        """
        if gender.lower() == 'male':
            # Men: (waist * 0.74) - (weight * 0.082) + 4.15
            body_fat = (waist * 0.74) - (weight * 0.082) + 4.15
        else:
            # Women: (waist * 0.74) - (weight * 0.082) + 4.15 (same formula, different constants in some versions)
            body_fat = (waist * 0.74) - (weight * 0.082) + 4.15
        
        return max(1.0, min(60.0, body_fat))
    
    @staticmethod
    def calculate_skinfold_3_site(gender: str, age: int, tricep: float, suprailiac: float, thigh: float = None, chest: float = None, abdominal: float = None, subscapular: float = None) -> float:
        """
        Calculate body fat using 3-site skinfold method (Jackson & Pollock).
        
        Args:
            gender: 'male' or 'female'
            age: Age in years
            tricep: Tricep skinfold in mm
            suprailiac: Suprailiac skinfold in mm
            thigh: Thigh skinfold in mm (for women)
            chest: Chest skinfold in mm (for men)
            abdominal: Abdominal skinfold in mm (for men)
            subscapular: Subscapular skinfold in mm (alternative)
            
        Returns:
            Body fat percentage
        """
        if gender.lower() == 'male':
            # Men: chest, abdominal, thigh
            if chest is None or abdominal is None:
                if thigh is None:
                    raise ValueError("Chest, abdominal, and thigh measurements required for men")
                sum_skinfolds = tricep + suprailiac + thigh  # Alternative sites
            else:
                sum_skinfolds = chest + abdominal + (thigh or tricep)
            
            # Jackson & Pollock formula for men
            body_density = 1.10938 - (0.0008267 * sum_skinfolds) + (0.0000016 * sum_skinfolds**2) - (0.0002574 * age)
        else:
            # Women: tricep, suprailiac, thigh
            if thigh is None:
                thigh = subscapular or 10.0  # Use subscapular as alternative
            sum_skinfolds = tricep + suprailiac + thigh
            
            # Jackson & Pollock formula for women
            body_density = 1.0994921 - (0.0009929 * sum_skinfolds) + (0.0000023 * sum_skinfolds**2) - (0.0001392 * age)
        
        # Convert body density to body fat percentage using Siri equation
        body_fat = (495 / body_density) - 450
        return max(1.0, min(60.0, body_fat))
    
    @staticmethod
    def get_body_fat_category(gender: str, body_fat_percentage: float) -> str:
        """
        Get body fat category based on percentage and gender.
        
        Args:
            gender: 'male' or 'female'
            body_fat_percentage: Body fat percentage
            
        Returns:
            Body fat category
        """
        if gender.lower() == 'male':
            if body_fat_percentage <= 5:
                return BodyFatCategory.ESSENTIAL
            elif body_fat_percentage <= 13:
                return BodyFatCategory.ATHLETE
            elif body_fat_percentage <= 17:
                return BodyFatCategory.FITNESS
            elif body_fat_percentage <= 25:
                return BodyFatCategory.AVERAGE
            else:
                return BodyFatCategory.OBESE
        else:  # female
            if body_fat_percentage <= 13:
                return BodyFatCategory.ESSENTIAL
            elif body_fat_percentage <= 20:
                return BodyFatCategory.ATHLETE
            elif body_fat_percentage <= 24:
                return BodyFatCategory.FITNESS
            elif body_fat_percentage <= 31:
                return BodyFatCategory.AVERAGE
            else:
                return BodyFatCategory.OBESE
    
    @property
    def bmi(self) -> float:
        """Calculate BMI for additional context"""
        height_m = self.height / 100
        return float(self.weight) / (height_m ** 2)
    
    @property
    def ideal_body_fat_range(self) -> tuple:
        """Get ideal body fat range for gender"""
        if self.gender == 'male':
            return (10, 18)  # 10-18% for men
        else:
            return (16, 25)  # 16-25% for women
    
    @property
    def is_in_healthy_range(self) -> bool:
        """Check if body fat is in healthy range"""
        min_bf, max_bf = self.ideal_body_fat_range
        return min_bf <= float(self.body_fat_percentage) <= max_bf
    
    def save(self, *args, **kwargs):
        """Override save to auto-calculate body fat values"""
        
        # Calculate body fat percentage based on method
        if self.method == BodyFatCalculationMethod.NAVY:
            if not all([self.waist_circumference, self.neck_circumference]):
                raise ValueError("Waist and neck circumferences required for Navy method")
            if self.gender == 'female' and not self.hip_circumference:
                raise ValueError("Hip circumference required for women using Navy method")
            
            self.body_fat_percentage = Decimal(str(self.calculate_navy_method(
                self.gender, 
                float(self.waist_circumference), 
                float(self.neck_circumference), 
                self.height,
                float(self.hip_circumference) if self.hip_circumference else None
            ))).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)
            
        elif self.method == BodyFatCalculationMethod.YMCA:
            if not self.waist_circumference:
                raise ValueError("Waist circumference required for YMCA method")
            
            self.body_fat_percentage = Decimal(str(self.calculate_ymca_method(
                self.gender, 
                float(self.weight), 
                float(self.waist_circumference)
            ))).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)
            
        elif self.method == BodyFatCalculationMethod.SKINFOLD_3:
            if not all([self.tricep_skinfold, self.suprailiac_skinfold]):
                raise ValueError("Tricep and suprailiac skinfolds required for 3-site method")
            
            self.body_fat_percentage = Decimal(str(self.calculate_skinfold_3_site(
                self.gender,
                self.age,
                float(self.tricep_skinfold),
                float(self.suprailiac_skinfold),
                float(self.thigh_skinfold) if self.thigh_skinfold else None,
                float(self.chest_skinfold) if self.chest_skinfold else None,
                float(self.abdominal_skinfold) if self.abdominal_skinfold else None,
                float(self.subscapular_skinfold) if self.subscapular_skinfold else None
            ))).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)
            
        elif self.method in [BodyFatCalculationMethod.BIA, BodyFatCalculationMethod.DEXA]:
            if not self.manual_body_fat_percentage:
                raise ValueError("Manual body fat percentage required for BIA/DEXA methods")
            self.body_fat_percentage = self.manual_body_fat_percentage
        
        # Calculate fat mass and lean mass
        self.fat_mass = (self.weight * self.body_fat_percentage / 100).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)
        self.lean_mass = (self.weight - self.fat_mass).quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)
        
        # Determine category
        self.category = self.get_body_fat_category(self.gender, float(self.body_fat_percentage))
        
        # Set only one calculation as active per user
        if self.is_active and not self.pk:
            BodyFatCalculationModel.objects.filter(
                user=self.user, is_active=True
            ).update(is_active=False)
        
        super().save(*args, **kwargs)


class BodyFatGoalModel(AbstractAutoIncrementModel):
    """
    Model to store user body fat goals and track progress.
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="body_fat_goals",
        verbose_name=_("User"),
    )
    
    target_body_fat_percentage = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        validators=[
            MinValueValidator(Decimal('3.0')),
            MaxValueValidator(Decimal('50.0')),
        ],
        verbose_name=_("Target Body Fat Percentage"),
        help_text=_("Target body fat percentage"),
    )
    
    current_body_fat_percentage = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        validators=[
            MinValueValidator(Decimal('3.0')),
            MaxValueValidator(Decimal('60.0')),
        ],
        verbose_name=_("Current Body Fat Percentage"),
        help_text=_("Current body fat percentage"),
    )
    
    target_date = models.DateField(
        verbose_name=_("Target Date"),
        help_text=_("Date to achieve the goal"),
    )
    
    goal_description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Goal Description"),
        help_text=_("Description of the body fat goal"),
    )
    
    is_achieved = models.BooleanField(
        default=False,
        verbose_name=_("Achieved"),
        help_text=_("Whether this goal has been achieved"),
    )
    
    achieved_date = models.DateField(
        blank=True,
        null=True,
        verbose_name=_("Achieved Date"),
        help_text=_("Date when goal was achieved"),
    )
    
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Notes"),
        help_text=_("Additional notes about this goal"),
    )
    
    class Meta:
        app_label = "apps_calculators_body_fat"
        db_table = "body_fat_goal"
        verbose_name = _("Body Fat Goal")
        verbose_name_plural = _("Body Fat Goals")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["user", "-created"]),
            models.Index(fields=["is_achieved"]),
        ]
    
    def __str__(self):
        return f"{self.user.email} - Target: {self.target_body_fat_percentage}%"
    
    @property
    def body_fat_difference(self) -> float:
        """Calculate body fat difference to target"""
        return float(self.current_body_fat_percentage - self.target_body_fat_percentage)
    
    @property
    def days_to_goal(self) -> int:
        """Calculate days remaining to reach goal"""
        from django.utils import timezone
        today = timezone.now().date()
        return (self.target_date - today).days
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage towards goal"""
        if self.is_achieved:
            return 100.0
        
        total_change_needed = abs(self.body_fat_difference)
        if total_change_needed == 0:
            return 100.0
        
        # This would typically be calculated from actual body fat tracking data
        # For now, we'll estimate based on time elapsed
        from django.utils import timezone
        days_elapsed = (timezone.now().date() - self.created.date()).days
        
        # Assume 0.1% body fat change per week (0.014% per day)
        expected_change = 0.014 * days_elapsed
        progress = min(expected_change / total_change_needed * 100, 100)
        return max(0, progress)


# Aliases for easier imports (following BMI pattern)
BodyFatCalculation = BodyFatCalculationModel
BodyFatGoal = BodyFatGoalModel 