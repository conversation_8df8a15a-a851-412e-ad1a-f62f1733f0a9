# Generated by Django 4.2.23 on 2025-06-20 17:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("apps_calculators_body_fat", "0001_initial"),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name="bodyfatgoalmodel",
            name="body_fat_go_target__6e36bf_idx",
        ),
        migrations.RenameIndex(
            model_name="bodyfatcalculationmodel",
            new_name="body_fat_ca_user_id_252652_idx",
            old_name="body_fat_ca_user_id_c9ef88_idx",
        ),
        migrations.RenameIndex(
            model_name="bodyfatcalculationmodel",
            new_name="body_fat_ca_is_acti_7b6c29_idx",
            old_name="body_fat_ca_is_acti_8ee42a_idx",
        ),
        migrations.RenameIndex(
            model_name="bodyfatgoalmodel",
            new_name="body_fat_go_user_id_bcc14b_idx",
            old_name="body_fat_go_user_id_f7f87c_idx",
        ),
        migrations.RenameIndex(
            model_name="bodyfatgoalmodel",
            new_name="body_fat_go_is_achi_562886_idx",
            old_name="body_fat_go_is_achi_0e72b6_idx",
        ),
        migrations.AddField(
            model_name="bodyfatcalculationmodel",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AddField(
            model_name="bodyfatgoalmodel",
            name="_id",
            field=models.IntegerField(
                db_index=True, editable=False, null=True, unique=True
            ),
        ),
        migrations.AlterField(
            model_name="bodyfatcalculationmodel",
            name="is_active",
            field=models.BooleanField(
                default=True,
                help_text="Whether this calculation is active/current",
                verbose_name="Active",
            ),
        ),
        migrations.AlterField(
            model_name="bodyfatgoalmodel",
            name="notes",
            field=models.TextField(
                blank=True,
                help_text="Additional notes about this goal",
                null=True,
                verbose_name="Notes",
            ),
        ),
        migrations.AddIndex(
            model_name="bodyfatcalculationmodel",
            index=models.Index(fields=["method"], name="body_fat_ca_method_0029c3_idx"),
        ),
    ]
