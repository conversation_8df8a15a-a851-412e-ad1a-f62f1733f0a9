

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BodyFatCalculationModel',
            fields=[
                ('created', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('age', models.PositiveIntegerField(help_text='Age in years (1-120)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(120)], verbose_name='Age')),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female')], max_length=10, verbose_name='Gender')),
                ('weight', models.DecimalField(decimal_places=1, help_text='Weight in kilograms (20-500 kg)', max_digits=5, validators=[django.core.validators.MinValueValidator(20.0), django.core.validators.MaxValueValidator(500.0)], verbose_name='Weight (kg)')),
                ('height', models.PositiveIntegerField(help_text='Height in centimeters (50-300 cm)', validators=[django.core.validators.MinValueValidator(50), django.core.validators.MaxValueValidator(300)], verbose_name='Height (cm)')),
                ('method', models.CharField(choices=[('navy', 'US Navy Method (circumference)'), ('ymca', 'YMCA Method (circumference)'), ('skinfold_3', '3-Site Skinfold'), ('skinfold_7', '7-Site Skinfold'), ('bia', 'Bioelectrical Impedance Analysis'), ('dexa', 'DEXA Scan (manual entry)')], default='navy', max_length=20, verbose_name='Calculation Method')),
                ('waist_circumference', models.DecimalField(blank=True, decimal_places=1, help_text='Waist circumference in centimeters', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(30.0), django.core.validators.MaxValueValidator(200.0)], verbose_name='Waist Circumference (cm)')),
                ('neck_circumference', models.DecimalField(blank=True, decimal_places=1, help_text='Neck circumference in centimeters', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(20.0), django.core.validators.MaxValueValidator(80.0)], verbose_name='Neck Circumference (cm)')),
                ('hip_circumference', models.DecimalField(blank=True, decimal_places=1, help_text='Hip circumference in centimeters (for women)', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(50.0), django.core.validators.MaxValueValidator(200.0)], verbose_name='Hip Circumference (cm)')),
                ('tricep_skinfold', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Tricep Skinfold (mm)')),
                ('subscapular_skinfold', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Subscapular Skinfold (mm)')),
                ('suprailiac_skinfold', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Suprailiac Skinfold (mm)')),
                ('abdominal_skinfold', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Abdominal Skinfold (mm)')),
                ('thigh_skinfold', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Thigh Skinfold (mm)')),
                ('chest_skinfold', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Chest Skinfold (mm)')),
                ('midaxillary_skinfold', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Midaxillary Skinfold (mm)')),
                ('manual_body_fat_percentage', models.DecimalField(blank=True, decimal_places=1, help_text='Manually entered body fat percentage from BIA/DEXA', max_digits=4, null=True, validators=[django.core.validators.MinValueValidator(1.0), django.core.validators.MaxValueValidator(60.0)], verbose_name='Manual Body Fat Percentage')),
                ('body_fat_percentage', models.DecimalField(decimal_places=1, help_text='Calculated body fat percentage', max_digits=4, verbose_name='Body Fat Percentage')),
                ('fat_mass', models.DecimalField(decimal_places=1, help_text='Calculated fat mass in kilograms', max_digits=5, verbose_name='Fat Mass (kg)')),
                ('lean_mass', models.DecimalField(decimal_places=1, help_text='Calculated lean mass in kilograms', max_digits=5, verbose_name='Lean Mass (kg)')),
                ('category', models.CharField(choices=[('essential', 'Essential Fat'), ('athlete', 'Athlete'), ('fitness', 'Fitness'), ('average', 'Average'), ('obese', 'Obese')], help_text='Body fat category based on percentage', max_length=20, verbose_name='Body Fat Category')),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this body fat calculation', null=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this is the active body fat calculation', verbose_name='Active')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='body_fat_calculations', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Body Fat Calculation',
                'verbose_name_plural': 'Body Fat Calculations',
                'db_table': 'body_fat_calculation',
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='BodyFatGoalModel',
            fields=[
                ('created', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('target_body_fat_percentage', models.DecimalField(decimal_places=1, help_text='Target body fat percentage', max_digits=4, validators=[django.core.validators.MinValueValidator(3.0), django.core.validators.MaxValueValidator(50.0)], verbose_name='Target Body Fat Percentage')),
                ('current_body_fat_percentage', models.DecimalField(decimal_places=1, help_text='Current body fat percentage', max_digits=4, validators=[django.core.validators.MinValueValidator(3.0), django.core.validators.MaxValueValidator(60.0)], verbose_name='Current Body Fat Percentage')),
                ('target_date', models.DateField(help_text='Date to achieve the goal', verbose_name='Target Date')),
                ('goal_description', models.TextField(blank=True, help_text='Description of the body fat goal', null=True, verbose_name='Goal Description')),
                ('is_achieved', models.BooleanField(default=False, help_text='Whether this goal has been achieved', verbose_name='Achieved')),
                ('achieved_date', models.DateField(blank=True, help_text='Date when goal was achieved', null=True, verbose_name='Achieved Date')),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this body fat goal', null=True, verbose_name='Notes')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='body_fat_goals', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Body Fat Goal',
                'verbose_name_plural': 'Body Fat Goals',
                'db_table': 'body_fat_goal',
                'ordering': ['-created'],
            },
        ),
        migrations.AddIndex(
            model_name='bodyfatcalculationmodel',
            index=models.Index(fields=['user', '-created'], name='body_fat_ca_user_id_c9ef88_idx'),
        ),
        migrations.AddIndex(
            model_name='bodyfatcalculationmodel',
            index=models.Index(fields=['is_active'], name='body_fat_ca_is_acti_8ee42a_idx'),
        ),
        migrations.AddIndex(
            model_name='bodyfatgoalmodel',
            index=models.Index(fields=['user', '-created'], name='body_fat_go_user_id_f7f87c_idx'),
        ),
        migrations.AddIndex(
            model_name='bodyfatgoalmodel',
            index=models.Index(fields=['is_achieved'], name='body_fat_go_is_achi_0e72b6_idx'),
        ),
        migrations.AddIndex(
            model_name='bodyfatgoalmodel',
            index=models.Index(fields=['target_date'], name='body_fat_go_target__6e36bf_idx'),
        ),
    ] 