"""
Tests for Water Intake Goal Views
"""

from decimal import Decimal
from datetime import date, timedelta
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from apps.accounts.user.tests.factories import UserFactory

from apps.calculators.water_intake.models import WaterIntakeGoalModel

User = get_user_model()


class WaterIntakeGoalViewsTestCase(TestCase):
    """Test cases for Water Intake Goal Views"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
        
        
        self.goal = WaterIntakeGoalModel.objects.create(
            user=self.user,
            target_daily_ml=2500,
            reminder_interval_hours=2,
            goal_description='Test daily hydration goal',
            notes='Test goal'
        )

    def test_goal_list_create_view_get(self):
        """Test listing water intake goals"""
        url = reverse('water_intake:goal-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['count'], 1)
        results = data['results']
        self.assertEqual(len(results), 1)
        self.assertEqual(str(results[0]['id']), str(self.goal.id))
        self.assertIn('progress_percentage', results[0])
        self.assertIn('days_remaining', results[0])
        self.assertIn('current_adherence', results[0])

    def test_goal_list_create_view_post(self):
        """Test creating a new water intake goal"""
        url = reverse('water_intake:goal-list-create')
        payload = {
            'target_daily_ml': 3000,
            'reminder_interval_hours': 3,
            'goal_description': 'New hydration goal',
            'notes': 'New hydration goal for increased activity'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        
        self.assertEqual(data['target_daily_ml'], 3000)
        self.assertEqual(data['reminder_interval_hours'], 3)
        self.assertEqual(data['goal_description'], 'New hydration goal')

    def test_goal_list_create_view_invalid_date(self):
        """Test creating goal with invalid reminder interval"""
        url = reverse('water_intake:goal-list-create')
        payload = {
            'target_daily_ml': 2000,
            'reminder_interval_hours': 10,  
            'goal_description': 'Invalid goal'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('reminder_interval_hours', response.json()['error'])

    def test_goal_list_create_view_invalid_intake(self):
        """Test creating goal with invalid intake amount"""
        url = reverse('water_intake:goal-list-create')
        payload = {
            'target_daily_ml': 10000,  
            'reminder_interval_hours': 2,
            'goal_description': 'Invalid intake goal'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('target_daily_ml', response.json()['error'])

    def test_goal_detail_view_get(self):
        """Test retrieving a specific goal"""
        url = reverse('water_intake:goal-detail', kwargs={'pk': self.goal.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(str(data['id']), str(self.goal.id))
        self.assertEqual(data['target_daily_ml'], 2500)
        self.assertEqual(data['reminder_interval_hours'], 2)

    def test_goal_detail_view_update(self):
        """Test updating a specific goal"""
        url = reverse('water_intake:goal-detail', kwargs={'pk': self.goal.pk})
        payload = {
            'notes': 'Updated goal notes',
            'target_daily_ml': 2800
        }
        
        response = self.client.patch(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['notes'], 'Updated goal notes')
        self.assertEqual(data['target_daily_ml'], 2800)

    def test_goal_detail_view_delete(self):
        """Test deleting a specific goal"""
        url = reverse('water_intake:goal-detail', kwargs={'pk': self.goal.pk})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(
            WaterIntakeGoalModel.objects.filter(pk=self.goal.pk).exists()
        )

    def test_goal_detail_view_not_found(self):
        """Test retrieving non-existent goal"""
        url = reverse('water_intake:goal-detail', kwargs={'pk': '12345678-1234-1234-1234-123456789012'})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_goal_achieve_view(self):
        """Test marking a goal as achieved"""
        url = reverse('water_intake:goal-achieve', kwargs={'pk': self.goal.pk})
        response = self.client.patch(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['is_achieved'], True)
        self.assertIsNotNone(data['achieved_date'])
        
        
        self.goal.refresh_from_db()
        self.assertTrue(self.goal.is_achieved)
        self.assertIsNotNone(self.goal.achieved_date)

    def test_goal_achieve_view_already_achieved(self):
        """Test marking an already achieved goal"""
        
        self.goal.is_achieved = True
        self.goal.achieved_date = timezone.now().date()
        self.goal.save()
        
        url = reverse('water_intake:goal-achieve', kwargs={'pk': self.goal.pk})
        response = self.client.patch(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('already marked as achieved', response.json()['detail'])

    def test_goal_achieve_view_not_found(self):
        """Test marking non-existent goal as achieved"""
        url = reverse('water_intake:goal-achieve', kwargs={'pk': '12345678-1234-1234-1234-123456789012'})
        response = self.client.patch(url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_goal_serializer_progress_percentage(self):
        """Test progress percentage calculation"""
        url = reverse('water_intake:goal-detail', kwargs={'pk': self.goal.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('progress_percentage', data)
        self.assertIsInstance(data['progress_percentage'], (int, float))

    def test_goal_serializer_days_remaining(self):
        """Test days remaining calculation"""
        url = reverse('water_intake:goal-detail', kwargs={'pk': self.goal.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('days_remaining', data)
        self.assertIsInstance(data['days_remaining'], (int, type(None)))

    def test_goal_serializer_current_adherence(self):
        """Test current adherence calculation"""
        url = reverse('water_intake:goal-detail', kwargs={'pk': self.goal.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('current_adherence', data)
        self.assertIsInstance(data['current_adherence'], (int, float, type(None)))

    def test_views_require_authentication(self):
        """Test that goal views require authentication"""
        self.client.force_authenticate(user=None)
        
        urls = [
            reverse('water_intake:goal-list-create'),
            reverse('water_intake:goal-detail', kwargs={'pk': self.goal.pk}),
            reverse('water_intake:goal-achieve', kwargs={'pk': self.goal.pk}),
        ]
        
        for url in urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_user_isolation(self):
        """Test that users can only see their own goals"""
        other_user = UserFactory()
        other_goal = WaterIntakeGoalModel.objects.create(
            user=other_user,
            target_daily_ml=3000,
            reminder_interval_hours=3,
            goal_description='Other user goal'
        )
        
        
        url = reverse('water_intake:goal-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['count'], 1)
        results = data['results']
        self.assertEqual(len(results), 1)
        self.assertEqual(str(results[0]['id']), str(self.goal.id))
        
        
        url = reverse('water_intake:goal-detail', kwargs={'pk': other_goal.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_goal_create_deactivates_existing(self):
        """Test that creating a new goal deactivates existing ones (if applicable)"""
        
        url = reverse('water_intake:goal-list-create')
        payload = {
            'target_daily_ml': 3500,
            'reminder_interval_hours': 4,
            'goal_description': 'New primary goal'
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        
        goals = WaterIntakeGoalModel.objects.filter(user=self.user)
        self.assertEqual(goals.count(), 2) 