"""
Tests for Water Intake Utility Views (Stats and Recommendations)
"""

from decimal import Decimal
from datetime import date, timedelta
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status
from apps.accounts.user.tests.factories import UserFactory

from apps.calculators.water_intake.models import WaterIntakeCalculationModel, WaterIntakeGoalModel

User = get_user_model()


class WaterIntakeUtilityViewsTestCase(TestCase):
    """Test cases for Water Intake Utility Views"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.user = UserFactory()
        self.client.force_authenticate(user=self.user)
        
        
        self.calculation1 = WaterIntakeCalculationModel.objects.create(
            user=self.user,
            weight=Decimal('70.0'),
            age=30,
            gender='male',
            activity_level='moderately_active',
            exercise_duration_minutes=60,
            exercise_intensity='moderate',
            climate_condition='temperate',
            health_conditions='diabetes',
            caffeine_intake_mg=100,
            alcohol_servings=1,
            is_active=True,
            notes='Recent calculation',
            
            base_water_intake_ml=2000,
            activity_adjustment_ml=500,
            climate_adjustment_ml=0,
            health_adjustment_ml=200,
            lifestyle_adjustment_ml=0,
            total_water_intake_ml=2700,
            total_water_intake_liters=Decimal('2.70'),
            glasses_8oz=11,
            bottles_500ml=5
        )
        
        self.calculation2 = WaterIntakeCalculationModel.objects.create(
            user=self.user,
            weight=Decimal('72.0'),
            age=32,
            gender='male',
            activity_level='very_active',
            exercise_duration_minutes=90,
            exercise_intensity='high',
            climate_condition='hot_dry',
            health_conditions='none',
            caffeine_intake_mg=50,
            alcohol_servings=0,
            is_active=False,
            notes='Older calculation',
            
            base_water_intake_ml=2200,
            activity_adjustment_ml=700,
            climate_adjustment_ml=300,
            health_adjustment_ml=0,
            lifestyle_adjustment_ml=0,
            total_water_intake_ml=3200,
            total_water_intake_liters=Decimal('3.20'),
            glasses_8oz=13,
            bottles_500ml=6
        )
        
        
        self.goal1 = WaterIntakeGoalModel.objects.create(
            user=self.user,
            target_daily_ml=2500,
            reminder_interval_hours=2,
            goal_description='Daily hydration goal',
            is_achieved=False
        )
        
        self.goal2 = WaterIntakeGoalModel.objects.create(
            user=self.user,
            target_daily_ml=3000,
            reminder_interval_hours=3,
            goal_description='High activity hydration goal',
            is_achieved=True,
            achieved_date=timezone.now().date()
        )

    def test_stats_view_with_data(self):
        """Test retrieving stats when user has calculations and goals"""
        url = reverse('water_intake:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['total_calculations'], 2)
        self.assertEqual(data['total_goals'], 2)
        self.assertEqual(data['achieved_goals'], 1)
        
        
        self.assertIsNotNone(data['average_daily_intake'])
        self.assertIsNotNone(data['latest_daily_intake'])
        self.assertIsInstance(data['monthly_progress'], list)

    def test_stats_view_no_data(self):
        """Test retrieving stats when user has no calculations"""
        
        WaterIntakeCalculationModel.objects.filter(user=self.user).delete()
        WaterIntakeGoalModel.objects.filter(user=self.user).delete()
        
        url = reverse('water_intake:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['total_calculations'], 0)
        self.assertEqual(data['total_goals'], 0)
        self.assertEqual(data['achieved_goals'], 0)
        self.assertIsNone(data['average_daily_intake'])
        self.assertIsNone(data['latest_daily_intake'])
        self.assertIsNone(data['hydration_trend'])
        self.assertEqual(data['monthly_progress'], [])

    def test_recommendations_view_with_data(self):
        """Test getting recommendations with input data"""
        url = reverse('water_intake:recommendations')
        payload = {
            'weight': 75.0,
            'age': 28,
            'gender': 'female',
            'activity_level': 'very_active',
            'exercise_duration_minutes': 90,
            'exercise_intensity': 'high',
            'climate_condition': 'hot_dry',
            'health_conditions': 'diabetes',
            'caffeine_intake_mg': 200,
            'alcohol_servings': 0
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('recommended_daily_intake', data)
        self.assertIn('intake_range', data)
        self.assertIn('breakdown', data)
        self.assertIn('timing_recommendations', data)
        self.assertIn('hydration_tips', data)
        self.assertIn('warning_signs', data)
        
        
        self.assertIn('minimum', data['intake_range'])
        self.assertIn('maximum', data['intake_range'])
        
        
        breakdown = data['breakdown']
        self.assertIn('base_intake', breakdown)
        self.assertIn('activity_adjustment', breakdown)
        self.assertIn('climate_adjustment', breakdown)
        self.assertIn('health_adjustment', breakdown)

    def test_recommendations_view_with_defaults(self):
        """Test getting recommendations using defaults from user's latest calculation"""
        url = reverse('water_intake:recommendations')
        payload = {}  
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('recommended_daily_intake', data)
        self.assertGreater(data['recommended_daily_intake'], 0)

    def test_recommendations_view_active_user(self):
        """Test recommendations for very active user include exercise hydration"""
        url = reverse('water_intake:recommendations')
        payload = {
            'weight': 80.0,
            'age': 30,
            'gender': 'male',
            'activity_level': 'very_active',
            'exercise_duration_minutes': 120,
            'exercise_intensity': 'high',
            'climate_condition': 'temperate',
            'health_conditions': 'none',
            'caffeine_intake_mg': 100,
            'alcohol_servings': 0
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('exercise_hydration', data)
        self.assertIsInstance(data['exercise_hydration'], list)
        self.assertGreater(len(data['exercise_hydration']), 0)

    def test_recommendations_view_hot_climate(self):
        """Test recommendations for hot climate include weather tips"""
        url = reverse('water_intake:recommendations')
        payload = {
            'weight': 70.0,
            'age': 25,
            'gender': 'female',
            'activity_level': 'moderately_active',
            'exercise_duration_minutes': 45,
            'exercise_intensity': 'moderate',
            'climate_condition': 'hot_dry',
            'health_conditions': 'none',
            'caffeine_intake_mg': 50,
            'alcohol_servings': 0
        }
        
        response = self.client.post(url, payload, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('hot_weather_tips', data)
        self.assertIsInstance(data['hot_weather_tips'], list)
        self.assertGreater(len(data['hot_weather_tips']), 0)

    def test_recommendations_view_health_adjustments(self):
        """Test recommendations properly adjust for health conditions"""
        url = reverse('water_intake:recommendations')
        
        
        payload_diabetes = {
            'weight': 70.0,
            'age': 30,
            'gender': 'male',
            'activity_level': 'moderately_active',
            'exercise_duration_minutes': 60,
            'exercise_intensity': 'moderate',
            'climate_condition': 'temperate',
            'health_conditions': 'diabetes',
            'caffeine_intake_mg': 100,
            'alcohol_servings': 0
        }
        
        response_diabetes = self.client.post(url, payload_diabetes, format='json')
        self.assertEqual(response_diabetes.status_code, status.HTTP_200_OK)
        
        
        payload_none = payload_diabetes.copy()
        payload_none['health_conditions'] = 'none'
        
        response_none = self.client.post(url, payload_none, format='json')
        self.assertEqual(response_none.status_code, status.HTTP_200_OK)
        
        
        diabetes_intake = response_diabetes.json()['recommended_daily_intake']
        none_intake = response_none.json()['recommended_daily_intake']
        self.assertGreater(diabetes_intake, none_intake)

    def test_recommendations_view_intake_bounds(self):
        """Test recommendations respect reasonable intake bounds"""
        url = reverse('water_intake:recommendations')
        
        
        payload_low = {
            'weight': 30.0,
            'age': 25,
            'gender': 'female',
            'activity_level': 'sedentary',
            'exercise_duration_minutes': 0,
            'exercise_intensity': 'low',
            'climate_condition': 'cold',
            'health_conditions': 'none',
            'caffeine_intake_mg': 0,
            'alcohol_servings': 0
        }
        
        response_low = self.client.post(url, payload_low, format='json')
        self.assertEqual(response_low.status_code, status.HTTP_200_OK)
        
        
        payload_high = {
            'weight': 150.0,
            'age': 35,
            'gender': 'male',
            'activity_level': 'extremely_active',
            'exercise_duration_minutes': 180,
            'exercise_intensity': 'high',
            'climate_condition': 'hot_dry',
            'health_conditions': 'fever',
            'caffeine_intake_mg': 500,
            'alcohol_servings': 5
        }
        
        response_high = self.client.post(url, payload_high, format='json')
        self.assertEqual(response_high.status_code, status.HTTP_200_OK)
        
        
        low_intake = response_low.json()['recommended_daily_intake']
        high_intake = response_high.json()['recommended_daily_intake']
        
        self.assertGreaterEqual(low_intake, 0.5)
        self.assertLessEqual(low_intake, 6.0)
        self.assertGreaterEqual(high_intake, 0.5)
        self.assertLessEqual(high_intake, 6.0)

    def test_views_require_authentication(self):
        """Test that utility views require authentication"""
        self.client.force_authenticate(user=None)
        
        urls = [
            reverse('water_intake:stats'),
            reverse('water_intake:recommendations'),
        ]
        
        for url in urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_user_isolation_stats(self):
        """Test that stats view only shows user's own data"""
        other_user = UserFactory()
        
        
        WaterIntakeCalculationModel.objects.create(
            user=other_user,
            weight=Decimal('80.0'),
            age=25,
            gender='female',
            activity_level='lightly_active',
            exercise_duration_minutes=30,
            exercise_intensity='low',
            climate_condition='cold',
            health_conditions='none',
            caffeine_intake_mg=50,
            alcohol_servings=0,
            is_active=True,
            notes='Other user calculation',
            
            base_water_intake_ml=2200,
            activity_adjustment_ml=300,
            climate_adjustment_ml=100,
            health_adjustment_ml=0,
            lifestyle_adjustment_ml=50,
            total_water_intake_ml=2650,
            total_water_intake_liters=Decimal('2.65'),
            glasses_8oz=11,
            bottles_500ml=5
        )
        
        url = reverse('water_intake:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertEqual(data['total_calculations'], 2)
        self.assertEqual(data['total_goals'], 2)

    def test_stats_view_trend_calculation(self):
        """Test that stats view calculates hydration trend correctly"""
        
        yesterday_calculation = WaterIntakeCalculationModel.objects.create(
            user=self.user,
            weight=Decimal('69.0'),
            age=30,
            gender='male',
            activity_level='lightly_active',
            exercise_duration_minutes=30,
            exercise_intensity='low',
            climate_condition='temperate',
            health_conditions='none',
            caffeine_intake_mg=80,
            alcohol_servings=0,
            is_active=False,
            notes='Yesterday calculation',
            
            base_water_intake_ml=1800,
            activity_adjustment_ml=200,
            climate_adjustment_ml=0,
            health_adjustment_ml=0,
            lifestyle_adjustment_ml=0,
            total_water_intake_ml=2000,
            total_water_intake_liters=Decimal('2.00'),
            glasses_8oz=8,
            bottles_500ml=4
        )
        yesterday_calculation.created = timezone.now() - timedelta(days=1)
        yesterday_calculation.save()
        
        url = reverse('water_intake:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('hydration_trend', data)
        if data['hydration_trend'] is not None:
            self.assertIn(data['hydration_trend'], ['increasing', 'decreasing', 'stable'])

    def test_stats_view_monthly_progress(self):
        """Test that stats view includes monthly progress data"""
        url = reverse('water_intake:stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        
        self.assertIn('monthly_progress', data)
        self.assertIsInstance(data['monthly_progress'], list)
        
        
        if data['monthly_progress']:
            progress_entry = data['monthly_progress'][0]
            self.assertIn('date', progress_entry)
            self.assertIn('intake', progress_entry)
            self.assertIn('goal', progress_entry) 