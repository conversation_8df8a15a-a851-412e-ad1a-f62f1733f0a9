# Generated by Django 4.2.23 on 2025-06-20 18:18

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WaterIntakeGoalModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "target_daily_ml",
                    models.PositiveIntegerField(
                        help_text="Target daily water intake in milliliters",
                        validators=[
                            django.core.validators.MinValueValidator(500),
                            django.core.validators.MaxValueValidator(6000),
                        ],
                        verbose_name="Target Daily Intake (ml)",
                    ),
                ),
                (
                    "reminder_interval_hours",
                    models.PositiveIntegerField(
                        default=2,
                        help_text="Hours between hydration reminders",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(8),
                        ],
                        verbose_name="Reminder Interval (hours)",
                    ),
                ),
                (
                    "start_time",
                    models.TimeField(
                        default="07:00",
                        help_text="Time to start daily hydration tracking",
                        verbose_name="Start Time",
                    ),
                ),
                (
                    "end_time",
                    models.TimeField(
                        default="22:00",
                        help_text="Time to end daily hydration tracking",
                        verbose_name="End Time",
                    ),
                ),
                (
                    "goal_description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the hydration goal",
                        null=True,
                        verbose_name="Goal Description",
                    ),
                ),
                (
                    "is_achieved",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this goal has been achieved",
                        verbose_name="Achieved",
                    ),
                ),
                (
                    "achieved_date",
                    models.DateField(
                        blank=True,
                        help_text="Date when goal was achieved",
                        null=True,
                        verbose_name="Achieved Date",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional notes about this goal",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="water_intake_goals",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Water Intake Goal",
                "verbose_name_plural": "Water Intake Goals",
                "db_table": "water_intake_goal",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="water_intak_user_id_8340a3_idx",
                    ),
                    models.Index(
                        fields=["is_achieved"], name="water_intak_is_achi_be78f4_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="WaterIntakeCalculationModel",
            fields=[
                ("created", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_index=True,
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "_id",
                    models.IntegerField(
                        db_index=True, editable=False, null=True, unique=True
                    ),
                ),
                (
                    "weight",
                    models.DecimalField(
                        decimal_places=1,
                        help_text="Current weight in kilograms",
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("20.0")),
                            django.core.validators.MaxValueValidator(Decimal("500.0")),
                        ],
                        verbose_name="Weight (kg)",
                    ),
                ),
                (
                    "age",
                    models.PositiveIntegerField(
                        help_text="Age in years (affects base hydration needs)",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(120),
                        ],
                        verbose_name="Age",
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        choices=[("male", "Male"), ("female", "Female")],
                        max_length=10,
                        verbose_name="Gender",
                    ),
                ),
                (
                    "activity_level",
                    models.CharField(
                        choices=[
                            ("sedentary", "Sedentary (little to no exercise)"),
                            (
                                "lightly_active",
                                "Lightly Active (light exercise 1-3 days/week)",
                            ),
                            (
                                "moderately_active",
                                "Moderately Active (moderate exercise 3-5 days/week)",
                            ),
                            (
                                "very_active",
                                "Very Active (heavy exercise 6-7 days/week)",
                            ),
                            (
                                "extremely_active",
                                "Extremely Active (very heavy physical work or 2x/day training)",
                            ),
                        ],
                        default="moderately_active",
                        max_length=20,
                        verbose_name="Activity Level",
                    ),
                ),
                (
                    "exercise_duration_minutes",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Average daily exercise duration in minutes",
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(480),
                        ],
                        verbose_name="Daily Exercise Duration (minutes)",
                    ),
                ),
                (
                    "exercise_intensity",
                    models.CharField(
                        choices=[
                            ("low", "Low Intensity"),
                            ("moderate", "Moderate Intensity"),
                            ("high", "High Intensity"),
                        ],
                        default="moderate",
                        max_length=10,
                        verbose_name="Exercise Intensity",
                    ),
                ),
                (
                    "climate_condition",
                    models.CharField(
                        choices=[
                            ("temperate", "Temperate (15-25°C)"),
                            ("hot_dry", "Hot & Dry (>25°C, low humidity)"),
                            ("hot_humid", "Hot & Humid (>25°C, high humidity)"),
                            ("cold", "Cold (<15°C)"),
                            ("high_altitude", "High Altitude (>2500m)"),
                        ],
                        default="temperate",
                        max_length=15,
                        verbose_name="Climate Condition",
                    ),
                ),
                (
                    "average_temperature",
                    models.IntegerField(
                        blank=True,
                        help_text="Average daily temperature in Celsius",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(-20),
                            django.core.validators.MaxValueValidator(50),
                        ],
                        verbose_name="Average Temperature (°C)",
                    ),
                ),
                (
                    "humidity_percentage",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Average humidity percentage",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Humidity (%)",
                    ),
                ),
                (
                    "health_conditions",
                    models.CharField(
                        choices=[
                            ("none", "No specific conditions"),
                            ("pregnancy", "Pregnancy"),
                            ("breastfeeding", "Breastfeeding"),
                            ("fever", "Fever/Illness"),
                            ("kidney_stones", "History of Kidney Stones"),
                            ("diabetes", "Diabetes"),
                            ("heart_condition", "Heart Condition"),
                        ],
                        default="none",
                        max_length=20,
                        verbose_name="Health Conditions",
                    ),
                ),
                (
                    "caffeine_intake_mg",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Average daily caffeine consumption in milligrams",
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(1000),
                        ],
                        verbose_name="Daily Caffeine Intake (mg)",
                    ),
                ),
                (
                    "alcohol_servings",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Average daily alcohol servings (1 serving = 12oz beer, 5oz wine, 1.5oz spirits)",
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(20),
                        ],
                        verbose_name="Daily Alcohol Servings",
                    ),
                ),
                (
                    "base_water_intake_ml",
                    models.PositiveIntegerField(
                        help_text="Base daily water intake in milliliters",
                        verbose_name="Base Water Intake (ml)",
                    ),
                ),
                (
                    "activity_adjustment_ml",
                    models.IntegerField(
                        default=0,
                        help_text="Additional water needed for physical activity",
                        verbose_name="Activity Adjustment (ml)",
                    ),
                ),
                (
                    "climate_adjustment_ml",
                    models.IntegerField(
                        default=0,
                        help_text="Additional water needed for climate conditions",
                        verbose_name="Climate Adjustment (ml)",
                    ),
                ),
                (
                    "health_adjustment_ml",
                    models.IntegerField(
                        default=0,
                        help_text="Additional water needed for health conditions",
                        verbose_name="Health Adjustment (ml)",
                    ),
                ),
                (
                    "lifestyle_adjustment_ml",
                    models.IntegerField(
                        default=0,
                        help_text="Additional water needed for caffeine/alcohol consumption",
                        verbose_name="Lifestyle Adjustment (ml)",
                    ),
                ),
                (
                    "total_water_intake_ml",
                    models.PositiveIntegerField(
                        help_text="Total recommended daily water intake in milliliters",
                        verbose_name="Total Water Intake (ml)",
                    ),
                ),
                (
                    "total_water_intake_liters",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total recommended daily water intake in liters",
                        max_digits=4,
                        verbose_name="Total Water Intake (L)",
                    ),
                ),
                (
                    "glasses_8oz",
                    models.PositiveIntegerField(
                        help_text="Equivalent number of 8oz glasses",
                        verbose_name="8oz Glasses",
                    ),
                ),
                (
                    "bottles_500ml",
                    models.PositiveIntegerField(
                        help_text="Equivalent number of 500ml bottles",
                        verbose_name="500ml Bottles",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Optional notes about this water intake calculation",
                        null=True,
                        verbose_name="Notes",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this calculation is active/current",
                        verbose_name="Active",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="water_intake_calculations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Water Intake Calculation",
                "verbose_name_plural": "Water Intake Calculations",
                "db_table": "water_intake_calculation",
                "ordering": ["-created"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created"],
                        name="water_intak_user_id_6b6539_idx",
                    ),
                    models.Index(
                        fields=["activity_level"], name="water_intak_activit_f39812_idx"
                    ),
                    models.Index(
                        fields=["climate_condition"],
                        name="water_intak_climate_3c148a_idx",
                    ),
                    models.Index(
                        fields=["is_active"], name="water_intak_is_acti_4df2f3_idx"
                    ),
                ],
            },
        ),
    ]
