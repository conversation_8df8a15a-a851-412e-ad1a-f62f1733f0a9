"""
Water Intake Goal Create Serializer

Serializer for creating water intake goals with validation.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta, time

from ..models import WaterIntakeGoalModel


class WaterIntakeGoalCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating water intake goals with validation.
    """
    
    class Meta:
        model = WaterIntakeGoalModel
        fields = [
            'target_daily_ml',
            'reminder_interval_hours',
            'start_time',
            'end_time',
            'goal_description',
            'notes',
        ]
    
    def validate_target_daily_ml(self, value):
        """Validate target daily intake is within reasonable range"""
        if value < 500 or value > 6000:
            raise serializers.ValidationError(
                _('Target daily intake must be between 500ml and 6000ml.')
            )
        return value
    
    def validate_reminder_interval_hours(self, value):
        """Validate reminder interval is reasonable"""
        if value < 1 or value > 8:
            raise serializers.ValidationError(
                _('Reminder interval must be between 1 and 8 hours.')
            )
        return value
    
    def validate(self, data):
        """Validate goal data for consistency"""
        start_time = data.get('start_time', time(7, 0))
        end_time = data.get('end_time', time(22, 0))
        
        
        if start_time >= end_time:
            raise serializers.ValidationError({
                'end_time': _('End time must be after start time.')
            })
        
        return data
    
    def create(self, validated_data):
        """Create a new water intake goal"""
        user = self.context['request'].user
        
        
        goal = WaterIntakeGoalModel.objects.create(
            user=user,
            **validated_data
        )
        
        return goal 