"""
Water Intake Calculation Serializer

Serializer for water intake calculations with all calculated fields.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import WaterIntakeCalculationModel


class WaterIntakeCalculationSerializer(serializers.ModelSerializer):
    """
    Serializer for water intake calculations with all calculated fields.
    """
    activity_level_display = serializers.CharField(
        source='get_activity_level_display',
        read_only=True
    )
    climate_condition_display = serializers.CharField(
        source='get_climate_condition_display',
        read_only=True
    )
    health_conditions_display = serializers.CharField(
        source='get_health_conditions_display',
        read_only=True
    )
    exercise_intensity_display = serializers.CharField(
        source='get_exercise_intensity_display',
        read_only=True
    )
    gender_display = serializers.CharField(
        source='get_gender_display',
        read_only=True
    )
    hourly_intake_ml = serializers.ReadOnlyField()
    intake_per_kg = serializers.ReadOnlyField()
    hydration_schedule = serializers.ReadOnlyField()
    
    class Meta:
        model = WaterIntakeCalculationModel
        fields = [
            'id',
            'created',
            'updated',
            'is_active',
            
            
            'weight',
            'age',
            'gender',
            'gender_display',
            
            
            'activity_level',
            'activity_level_display',
            'exercise_duration_minutes',
            'exercise_intensity',
            'exercise_intensity_display',
            
            
            'climate_condition',
            'climate_condition_display',
            'average_temperature',
            'humidity_percentage',
            
            
            'health_conditions',
            'health_conditions_display',
            
            
            'caffeine_intake_mg',
            'alcohol_servings',
            
            
            'base_water_intake_ml',
            'activity_adjustment_ml',
            'climate_adjustment_ml',
            'health_adjustment_ml',
            'lifestyle_adjustment_ml',
            'total_water_intake_ml',
            'total_water_intake_liters',
            'glasses_8oz',
            'bottles_500ml',
            
            
            'hourly_intake_ml',
            'intake_per_kg',
            'hydration_schedule',
            
            'notes',
        ]
        read_only_fields = [
            'id',
            'created',
            'updated',
            'base_water_intake_ml',
            'activity_adjustment_ml',
            'climate_adjustment_ml',
            'health_adjustment_ml',
            'lifestyle_adjustment_ml',
            'total_water_intake_ml',
            'total_water_intake_liters',
            'glasses_8oz',
            'bottles_500ml',
            'hourly_intake_ml',
            'intake_per_kg',
            'hydration_schedule',
        ]
    
    def get_hydration_breakdown(self, obj):
        """Get hydration breakdown by source"""
        return {
            'water_intake_liters': float(obj.daily_water_intake_liters),
            'water_intake_ml': int(float(obj.daily_water_intake_liters) * 1000),
            'base_intake_ml': int(float(obj.base_water_intake) * 1000),
            'activity_adjustment_ml': int(float(obj.activity_adjustment) * 1000),
            'climate_adjustment_ml': int(float(obj.climate_adjustment) * 1000),
            'health_adjustment_ml': int(float(obj.health_adjustment) * 1000),
        }
    
    def get_daily_schedule(self, obj):
        """Get suggested daily hydration schedule"""
        return obj.daily_schedule 