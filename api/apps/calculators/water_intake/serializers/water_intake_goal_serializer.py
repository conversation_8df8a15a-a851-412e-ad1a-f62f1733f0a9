"""
Water Intake Goal Serializer

Serializer for water intake goals with progress tracking.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from datetime import date

from ..models import WaterIntakeGoalModel


class WaterIntakeGoalSerializer(serializers.ModelSerializer):
    """
    Serializer for water intake goals with progress tracking.
    """
    target_daily_liters = serializers.SerializerMethodField()
    hourly_target_ml = serializers.SerializerMethodField()
    reminder_times = serializers.SerializerMethodField()
    progress_percentage = serializers.SerializerMethodField()
    days_remaining = serializers.SerializerMethodField()
    current_adherence = serializers.SerializerMethodField()
    
    class Meta:
        model = WaterIntakeGoalModel
        fields = [
            'id',
            'created',
            'updated',
            'target_daily_ml',
            'target_daily_liters',
            'reminder_interval_hours',
            'start_time',
            'end_time',
            'goal_description',
            'is_achieved',
            'achieved_date',
            'hourly_target_ml',
            'reminder_times',
            'progress_percentage',
            'days_remaining',
            'current_adherence',
            'notes',
        ]
        read_only_fields = [
            'id',
            'created',
            'updated',
            'is_achieved',
            'achieved_date',
            'target_daily_liters',
            'hourly_target_ml',
            'reminder_times',
            'progress_percentage',
            'days_remaining',
            'current_adherence',
        ]
    
    def get_target_daily_liters(self, obj):
        """Get target daily intake in liters"""
        return obj.target_daily_liters
    
    def get_hourly_target_ml(self, obj):
        """Get hourly target in ml"""
        return obj.hourly_target_ml
    
    def get_reminder_times(self, obj):
        """Get reminder times list"""
        return obj.reminder_times
    
    def get_progress_percentage(self, obj):
        """Calculate progress percentage"""
        return obj.progress_percentage
    
    def get_days_remaining(self, obj):
        """Calculate days remaining for goal - placeholder since we don't have end_date"""
        
        return None
    
    def get_current_adherence(self, obj):
        """Calculate current adherence - placeholder for now"""
        
        
        return 0.0 