"""
Water Intake Calculation Create Serializer

Serializer for creating water intake calculations with validation.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _

from ..models import WaterIntakeCalculationModel, HealthCondition


class WaterIntakeCalculationCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating water intake calculations with validation.
    """
    
    class Meta:
        model = WaterIntakeCalculationModel
        fields = [
            
            'weight',
            'age',
            'gender',
            
            
            'activity_level',
            'exercise_duration_minutes',
            'exercise_intensity',
            
            
            'climate_condition',
            'average_temperature',
            'humidity_percentage',
            
            
            'health_conditions',
            
            
            'caffeine_intake_mg',
            'alcohol_servings',
            
            
            'is_active',
            'notes',
        ]
    
    def validate_weight(self, value):
        """Validate weight is within reasonable range"""
        if value < 20 or value > 500:
            raise serializers.ValidationError(
                _('Weight must be between 20 and 500 kg.')
            )
        return value
    
    def validate_age(self, value):
        """Validate age is within reasonable range"""
        if value < 1 or value > 120:
            raise serializers.ValidationError(
                _('Age must be between 1 and 120 years.')
            )
        return value
    
    def validate_exercise_duration_minutes(self, value):
        """Validate exercise duration"""
        if value < 0 or value > 480:  
            raise serializers.ValidationError(
                _('Exercise duration must be between 0 and 480 minutes.')
            )
        return value
    
    def validate_caffeine_intake_mg(self, value):
        """Validate caffeine intake"""
        if value < 0 or value > 1000:
            raise serializers.ValidationError(
                _('Caffeine intake must be between 0 and 1000 mg.')
            )
        return value
    
    def validate_alcohol_servings(self, value):
        """Validate alcohol servings"""
        if value < 0 or value > 20:
            raise serializers.ValidationError(
                _('Alcohol servings must be between 0 and 20.')
            )
        return value

    def validate_health_conditions(self, value):
        """
        Validate health conditions choice.
        """
        valid_choices = [choice[0] for choice in HealthCondition.choices]
        if value and value not in valid_choices:
            raise serializers.ValidationError(
                _('Invalid health condition: {condition}. Valid choices are: {choices}').format(
                    condition=value,
                    choices=', '.join(valid_choices)
                )
            )
        return value 