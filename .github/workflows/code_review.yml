name: Code Review

# Read target branches from review_config.yaml
# Default to running on push to main and develop branches, and on all pull requests
on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - "*"

jobs:
  # Read configuration
  config:
    name: Load Configuration
    runs-on: ubuntu-latest
    outputs:
      config: ${{ steps.read_config.outputs.config }}
      ui-enabled: ${{ steps.extract_config.outputs.ui-enabled }}
      api-enabled: ${{ steps.extract_config.outputs.api-enabled }}
      structure-enabled: ${{ steps.extract_config.outputs.structure-enabled }}
      security-enabled: ${{ steps.extract_config.outputs.security-enabled }}
      fail-on-warnings: ${{ steps.extract_config.outputs.fail-on-warnings }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Read configuration
        id: read_config
        run: |
          config=$(cat review_config.yaml | base64 -w 0)
          echo "config=$config" >> $GITHUB_OUTPUT

      - name: Extract configuration values
        id: extract_config
        run: |
          echo "ui-enabled=$(yq '.ui.enabled' review_config.yaml)" >> $GITHUB_OUTPUT
          echo "api-enabled=$(yq '.api.enabled' review_config.yaml)" >> $GITHUB_OUTPUT
          echo "structure-enabled=$(yq '.structure.enabled' review_config.yaml)" >> $GITHUB_OUTPUT
          echo "security-enabled=$(yq '.security.enabled' review_config.yaml)" >> $GITHUB_OUTPUT
          echo "fail-on-warnings=$(yq '.ci.fail_on_warnings' review_config.yaml)" >> $GITHUB_OUTPUT

  # UI checks
  ui-checks:
    name: UI Checks
    needs: config
    if: needs.config.outputs.ui-enabled == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'ui/package-lock.json'

      - name: Install dependencies
        run: |
          cd ui
          npm ci

      - name: Run ESLint
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).ui.eslint.enabled }}
        run: |
          cd ui
          npm run lint || exit $?

      - name: Run Prettier
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).ui.prettier.enabled }}
        run: |
          cd ui
          npx prettier --check . || exit $?

      - name: Run TypeScript check
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).ui.typescript.enabled }}
        run: |
          cd ui
          npx tsc --noEmit || exit $?

      - name: Run npm audit
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).security.dependency_scanning.enabled }}
        run: |
          cd ui
          npm audit --json || echo "Vulnerabilities found but continuing build"

  # API checks
  api-checks:
    name: API Checks
    needs: config
    if: needs.config.outputs.api-enabled == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
          cache: 'pip'
          cache-dependency-path: 'api/requirements*.txt'

      - name: Install dependencies
        run: |
          cd api
          python -m pip install --upgrade pip
          pip install ruff black mypy bandit pip-audit
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi
          # Install django-stubs for mypy checking
          pip install django-stubs djangorestframework-stubs

      - name: Run Black
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).api.black.enabled }}
        run: |
          cd api
          black --check . || exit $?

      - name: Run Ruff
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).api.ruff.enabled }}
        run: |
          cd api
          ruff check . || exit $?

      - name: Run Mypy
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).api.mypy.enabled }}
        run: |
          cd api
          mypy . || exit $?

      - name: Run Bandit
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).api.bandit.enabled }}
        run: |
          cd api
          bandit -r . || exit $?

      - name: Run pip-audit
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).security.dependency_scanning.enabled }}
        run: |
          cd api
          pip-audit || echo "Vulnerabilities found but continuing build"

  # Structure validation
  structure-validation:
    name: Structure Validation
    needs: config
    if: needs.config.outputs.structure-enabled == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyyaml

      - name: Run structure validation
        run: |
          python scripts/validate_structure.py

  # Security checks
  security-checks:
    name: Security Checks
    needs: config
    if: needs.config.outputs.security-enabled == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyyaml

      - name: Check for secrets
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).security.secrets_scanning.enabled }}
        run: |
          # Create a simple script to check for common secrets
          # In a real scenario, you might want to use a tool like trufflehog or gitleaks
          grep -r "SECRET_KEY\s*=" --include="*.py" . || true
          grep -r "password\s*=" --include="*.py" --include="*.js" --include="*.ts" . || true
          grep -r "api[_-]?key" --include="*.py" --include="*.js" --include="*.ts" . || true

      - name: Check production config
        if: ${{ fromJson(needs.config.outputs.config) == null || fromJson(needs.config.outputs.config).security.prod_config_checks.enabled }}
        run: |
          python scripts/check_prod_config.py

  # Full review with reviewer package
  full-review:
    name: Full Code Review
    needs: config
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'ui/package-lock.json'

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyyaml ruff black mypy bandit pip-audit

      - name: Install Node.js dependencies
        run: |
          cd ui
          npm ci

      - name: Run full review
        run: |
          python -m reviewer.cli --verbose ${{ needs.config.outputs.fail-on-warnings == 'true' && '--fail-on-warnings' || '' }} 